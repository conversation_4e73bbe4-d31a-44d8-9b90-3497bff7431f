-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 4.8.5
-- https://www.phpmyadmin.net/
--
-- 主机： localhost
-- 生成日期： 2025-08-08 15:38:11
-- 服务器版本： 5.7.26
-- PHP 版本： 7.3.4

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
SET AUTOCOMMIT = 0;
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- 数据库： `project_platform`
--

-- --------------------------------------------------------

--
-- 表的结构 `announcements`
--

CREATE TABLE `announcements` (
  `id` bigint(20) NOT NULL,
  `title` varchar(200) NOT NULL COMMENT '公告标题',
  `content` text NOT NULL COMMENT '公告内容',
  `type` enum('SYSTEM','URGENT','NOTICE','EVENT') NOT NULL DEFAULT 'NOTICE' COMMENT '公告类型：SYSTEM-系统公告，URGENT-紧急通知，NOTICE-一般通知，EVENT-活动公告',
  `priority` enum('LOW','MEDIUM','HIGH','URGENT') NOT NULL DEFAULT 'MEDIUM' COMMENT '优先级',
  `status` enum('DRAFT','PUBLISHED','ARCHIVED') NOT NULL DEFAULT 'DRAFT' COMMENT '状态：DRAFT-草稿，PUBLISHED-已发布，ARCHIVED-已归档',
  `publisher_id` bigint(20) NOT NULL COMMENT '发布者ID',
  `target_audience` enum('ALL','TEACHERS','STUDENTS') NOT NULL DEFAULT 'ALL' COMMENT '目标受众：ALL-所有用户，TEACHERS-仅教师，STUDENTS-仅学生',
  `is_pinned` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否置顶',
  `publish_time` datetime DEFAULT NULL COMMENT '发布时间',
  `expire_time` datetime DEFAULT NULL COMMENT '过期时间',
  `view_count` int(11) NOT NULL DEFAULT '0' COMMENT '查看次数',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='公告表';

--
-- 转存表中的数据 `announcements`
--

INSERT INTO `announcements` (`id`, `title`, `content`, `type`, `priority`, `status`, `publisher_id`, `target_audience`, `is_pinned`, `publish_time`, `expire_time`, `view_count`, `create_time`, `update_time`) VALUES
(1, '欢迎使用项目协作平台', '欢迎大家使用我们的项目协作平台！本平台旨在为师生提供便捷的项目管理和团队协作环境。1111111111111', 'SYSTEM', 'LOW', 'PUBLISHED', 14, 'TEACHERS', 1, '2025-07-20 13:11:27', '2025-08-09 00:00:00', 2, '2025-07-19 22:01:30', '2025-07-30 21:26:40'),
(2, '系统维护通知', '系统将于本周六晚上10:00-12:00进行维护升级，期间可能无法正常访问，请大家提前做好准备。', 'URGENT', 'URGENT', 'PUBLISHED', 14, 'ALL', 1, '2025-07-19 22:01:30', NULL, 1, '2025-07-19 22:01:30', '2025-07-20 18:11:01'),
(4, '1111111', '11111111111111111', 'NOTICE', 'MEDIUM', 'PUBLISHED', 14, 'ALL', 1, '2025-07-19 23:41:13', '2025-08-09 00:00:00', 18, '2025-07-19 23:41:13', '2025-07-31 10:37:18'),
(6, '1111111', '111111111111111111111', 'NOTICE', 'MEDIUM', 'PUBLISHED', 14, 'ALL', 0, '2025-07-20 15:39:14', NULL, 2, '2025-07-20 15:39:14', '2025-07-31 10:56:14'),
(7, '111111111111', '1111111111111111', 'NOTICE', 'MEDIUM', 'PUBLISHED', 14, 'ALL', 0, '2025-07-20 15:39:20', NULL, 1, '2025-07-20 15:39:20', '2025-07-20 18:14:36'),
(9, '系统测试公告', '这是一个系统测试公告，用于验证公告功能是否正常工作。', 'NOTICE', 'MEDIUM', 'PUBLISHED', 30, 'ALL', 0, '2025-07-23 15:15:35', NULL, 3, '2025-07-23 15:15:35', '2025-07-31 10:38:03'),
(10, '项目申请截止提醒', '各位同学请注意，本学期项目申请将于本月底截止，请尚未申请的团队抓紧时间。', 'URGENT', 'HIGH', 'PUBLISHED', 31, 'STUDENTS', 1, '2025-07-31 19:12:43', NULL, 1, '2025-07-23 15:15:35', '2025-07-31 19:12:43'),
(11, '教师会议通知', '定于下周三下午2点召开教师工作会议，请各位老师准时参加。', 'EVENT', 'MEDIUM', 'PUBLISHED', 30, 'TEACHERS', 0, '2025-07-23 15:15:35', NULL, 1, '2025-07-23 15:15:35', '2025-07-31 12:10:40');

-- --------------------------------------------------------

--
-- 表的结构 `evaluations`
--

CREATE TABLE `evaluations` (
  `id` bigint(20) NOT NULL,
  `project_id` bigint(20) NOT NULL COMMENT '项目ID',
  `team_id` bigint(20) NOT NULL COMMENT '团队ID',
  `evaluator_id` bigint(20) NOT NULL COMMENT '评价教师ID',
  `score` decimal(5,2) NOT NULL COMMENT '评分(0-100)',
  `grade` varchar(10) NOT NULL COMMENT '等级(优秀/良好/中等/及格/不及格)',
  `comment` text COMMENT '评价意见',
  `status` enum('DRAFT','PUBLISHED') DEFAULT 'DRAFT' COMMENT '状态',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='项目评价表';

--
-- 转存表中的数据 `evaluations`
--

INSERT INTO `evaluations` (`id`, `project_id`, `team_id`, `evaluator_id`, `score`, `grade`, `comment`, `status`, `create_time`, `update_time`) VALUES
(1, 1, 1, 1, '85.50', '良好', '前端界面设计美观，用户交互体验良好，代码结构清晰。建议增加用户体验测试和可用性评估，关注系统安全性设计。', 'PUBLISHED', '2025-07-03 13:32:21', '2025-07-03 13:32:21'),
(2, 1, 2, 1, '88.00', '良好', '全栈开发能力突出，前后端技术栈掌握扎实，系统架构设计合理。建议定期进行代码审查，加强团队内部技术分享。', 'PUBLISHED', '2025-07-03 13:32:21', '2025-07-03 13:32:21'),
(3, 2, 3, 2, '82.00', '良好', '移动端开发思路清晰，对电商业务理解较好，用户界面设计有创新性。建议先完成核心购物功能，重点关注移动端性能优化。', 'PUBLISHED', '2025-07-03 13:32:21', '2025-07-03 13:32:21'),
(4, 3, 4, 3, '90.00', '优秀', '数据分析能力强，机器学习算法应用合理，数据可视化效果出色，系统架构设计优秀。建议关注大数据处理的实时性和并发性能。', 'PUBLISHED', '2025-07-03 13:32:21', '2025-07-03 13:32:21'),
(5, 5, 5, 15, '78.00', '中等', '游戏创意有趣，具有一定的创新性和可玩性，技术实现方案基本可行。建议增加团队成员或简化游戏功能以匹配开发能力。', 'PUBLISHED', '2025-07-03 13:32:21', '2025-07-03 13:32:21');

-- --------------------------------------------------------

--
-- 表的结构 `file_info`
--

CREATE TABLE `file_info` (
  `id` bigint(20) NOT NULL,
  `content_type` varchar(100) COLLATE utf8_unicode_ci DEFAULT NULL,
  `description` text COLLATE utf8_unicode_ci,
  `download_count` int(11) DEFAULT NULL,
  `file_extension` varchar(20) COLLATE utf8_unicode_ci DEFAULT NULL,
  `file_name` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `file_path` varchar(500) COLLATE utf8_unicode_ci NOT NULL,
  `file_size` bigint(20) NOT NULL,
  `file_type` enum('ARCHIVE','AUDIO','CODE','DOCUMENT','IMAGE','OTHER','VIDEO') COLLATE utf8_unicode_ci NOT NULL,
  `original_name` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `project_id` bigint(20) DEFAULT NULL,
  `record_id` bigint(20) DEFAULT NULL,
  `status` enum('ACTIVE','DELETED','QUARANTINE') COLLATE utf8_unicode_ci NOT NULL,
  `team_id` bigint(20) DEFAULT NULL,
  `update_time` datetime(6) NOT NULL,
  `upload_time` datetime(6) NOT NULL,
  `uploader_id` bigint(20) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

--
-- 转存表中的数据 `file_info`
--

INSERT INTO `file_info` (`id`, `content_type`, `description`, `download_count`, `file_extension`, `file_name`, `file_path`, `file_size`, `file_type`, `original_name`, `project_id`, `record_id`, `status`, `team_id`, `update_time`, `upload_time`, `uploader_id`) VALUES
(15, 'image/jpeg', 'nnnnnnnnnnn', 3, '.jpg', '4b7d72df-1870-4fb5-8698-663039232aae.jpg', '2025/07/29\\4b7d72df-1870-4fb5-8698-663039232aae.jpg', 1417949, 'IMAGE', 'WallpaperEngineLockOverride_randomJDXGSC.jpg', NULL, NULL, 'ACTIVE', 1, '2025-08-08 12:04:34.762361', '2025-07-29 22:25:24.837732', 4),
(16, 'application/octet-stream', '数据库', 2, '.sql', '74f9493b-7888-4590-8edb-db5362b87961.sql', '2025/07/29\\74f9493b-7888-4590-8edb-db5362b87961.sql', 44530, 'CODE', 'project_platform (3).sql', NULL, NULL, 'ACTIVE', 1, '2025-07-29 22:56:12.982594', '2025-07-29 22:26:00.400134', 4),
(17, 'image/jpeg', '任务提交附件', 0, '.jpg', '0413251f-5f46-48b8-b4e6-57ed9e230de0.jpg', '2025/08/02\\0413251f-5f46-48b8-b4e6-57ed9e230de0.jpg', 1417949, 'IMAGE', 'WallpaperEngineLockOverride_randomJDXGSC.jpg', 1, 42, 'DELETED', 1, '2025-08-02 17:13:57.319234', '2025-08-02 17:10:03.404399', 4),
(18, 'image/jpeg', '任务提交附件', 0, '.jpg', '5788ab07-c96a-42d5-96a8-236e4bddded0.jpg', '2025/08/02\\5788ab07-c96a-42d5-96a8-236e4bddded0.jpg', 1417949, 'IMAGE', 'WallpaperEngineLockOverride_randomJDXGSC.jpg', 1, 40, 'DELETED', 1, '2025-08-02 17:13:54.176430', '2025-08-02 17:11:42.444534', 4),
(19, 'application/octet-stream', NULL, 1, '.sql', 'a828b1f9-96f0-45a2-8bb3-5c4c8be81e1f.sql', '2025/08/02\\a828b1f9-96f0-45a2-8bb3-5c4c8be81e1f.sql', 48887, 'CODE', 'project_platform (4).sql', NULL, NULL, 'ACTIVE', 1, '2025-08-02 17:33:13.481890', '2025-08-02 17:13:45.155301', 4),
(20, 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', NULL, 0, '.docx', 'cffbb449-371d-4365-acec-0a8577b06816.docx', '2025/08/02\\cffbb449-371d-4365-acec-0a8577b06816.docx', 15111, 'DOCUMENT', '校园经历.docx', NULL, NULL, 'DELETED', 1, '2025-08-02 17:15:16.396747', '2025-08-02 17:15:12.254409', 4);

-- --------------------------------------------------------

--
-- 表的结构 `projects`
--

CREATE TABLE `projects` (
  `id` bigint(20) NOT NULL,
  `name` varchar(200) NOT NULL COMMENT '项目名称',
  `description` text COMMENT '项目描述',
  `requirements` text COMMENT '项目需求',
  `objectives` text COMMENT '项目目标',
  `teacher_id` bigint(20) NOT NULL COMMENT '发布教师ID',
  `max_team_size` int(11) DEFAULT '5' COMMENT '团队最大人数',
  `max_teams` int(11) DEFAULT '10' COMMENT '最大团队数量',
  `status` enum('PUBLISHED','IN_PROGRESS','COMPLETED','CANCELLED') NOT NULL DEFAULT 'PUBLISHED' COMMENT '项目状态',
  `start_date` date DEFAULT NULL COMMENT '开始日期',
  `end_date` date DEFAULT NULL COMMENT '结束日期',
  `deadline` datetime DEFAULT NULL COMMENT '截止时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `completion_date` datetime(6) DEFAULT NULL,
  `completion_summary` varchar(2000) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='项目表';

--
-- 转存表中的数据 `projects`
--

INSERT INTO `projects` (`id`, `name`, `description`, `requirements`, `objectives`, `teacher_id`, `max_team_size`, `max_teams`, `status`, `start_date`, `end_date`, `deadline`, `create_time`, `update_time`, `completion_date`, `completion_summary`) VALUES
(1, '在线学习平台开发', '开发一个功能完整的在线学习平台，支持用户注册登录、课程管理、学习进度跟踪、在线测试、讨论区等核心功能。要求采用前后端分离架构，具备良好的用户体验和系统性能。', '1.熟悉SpringBoot+Vue技术栈 2.掌握MySQL数据库设计 3.了解RESTful API设计 4.具备前后端分离开发经验', '1.掌握全栈开发技能和项目架构设计 2.提升团队协作和项目管理能力 3.学习企业级应用开发规范', 1, 5, 3, 'IN_PROGRESS', '2024-09-01', '2025-12-31', '2025-12-31 23:59:59', '2025-07-03 13:32:21', '2025-07-19 21:31:30', NULL, NULL),
(2, '移动端购物应用', '设计并开发一款移动端购物应用，包含商品浏览、分类筛选、购物车管理、订单处理、用户评价、支付集成等完整电商功能。注重用户体验设计和性能优化。', '1.熟悉移动端开发框架(React Native/Flutter) 2.了解UI/UX设计原则 3.具备API接口设计和调用能力 4.掌握移动端性能优化技术', '1.掌握移动端开发技术和跨平台开发 2.提升产品设计思维和用户体验意识 3.学习电商业务逻辑和支付流程', 2, 4, 2, 'IN_PROGRESS', '2024-09-15', '2025-12-31', '2025-12-31 23:59:59', '2025-07-03 13:32:21', '2025-07-19 21:32:59', NULL, NULL),
(3, '智能数据分析系统', '构建一个智能数据分析系统，能够处理大量结构化和非结构化数据，提供数据清洗、统计分析、机器学习建模、可视化报表等功能，支持多种数据源接入。', '1.掌握Python数据分析技术栈 2.熟悉机器学习算法和框架 3.具备数据可视化和报表设计能力 4.了解大数据处理技术', '1.提升数据处理和分析能力 2.掌握AI技术在实际业务中的应用 3.学习数据驱动的决策思维', 3, 6, 2, 'IN_PROGRESS', '2024-10-01', '2025-12-31', '2025-12-31 23:59:59', '2025-07-03 13:32:21', '2025-07-19 21:33:06', NULL, NULL),
(4, '校园信息管理系统', '开发校园信息管理系统，包含学生信息管理、教师管理、课程管理、选课系统、成绩管理、考勤统计等模块。要求系统具备完善的权限控制和数据安全保障。', '1.熟悉企业级Java开发 2.了解权限控制和安全设计 3.具备系统架构设计能力 4.掌握复杂业务逻辑处理', '1.掌握企业级应用开发和系统架构设计 2.提升复杂业务系统的分析和设计能力 3.学习权限管理和数据安全技术', 1, 5, 4, 'IN_PROGRESS', '2024-09-20', '2025-12-31', '2025-12-31 23:59:59', '2025-07-03 13:32:21', '2025-07-14 15:24:53', NULL, NULL),
(5, '2D休闲游戏开发', '开发一款2D休闲游戏，包含游戏逻辑设计、关卡编辑器、角色动画、音效系统、积分排行榜等功能。要求游戏具备良好的可玩性和用户粘性。', '1.熟悉游戏开发引擎(Unity/Cocos2d) 2.具备创意思维和游戏设计理念 3.了解游戏开发流程和规范 4.掌握基本的美术和音效处理', '1.掌握游戏开发技术和引擎使用 2.提升创新思维和产品设计能力 3.学习游戏行业的开发流程和商业模式', 15, 4, 3, 'IN_PROGRESS', '2024-10-15', '2025-12-31', '2025-12-31 23:59:59', '2025-07-03 13:32:21', '2025-07-19 21:33:12', NULL, NULL),
(6, '微信小程序开发实训', '开发一个功能完整的微信小程序，包含用户管理、数据展示、交互功能等', '1.熟悉微信小程序开发框架 2.掌握JavaScript基础 3.了解云开发技术', '1.掌握小程序开发技能 2.提升前端开发能力 3.学习云端数据处理', 31, 4, 6, 'IN_PROGRESS', '2025-03-01', '2025-08-31', '2025-08-31 23:59:59', '2025-07-23 15:14:58', '2025-07-23 22:08:11', NULL, NULL),
(7, 'Python数据分析项目', '使用Python进行数据分析和可视化，完成一个完整的数据科学项目', '1.熟悉Python编程 2.掌握pandas、numpy等库 3.了解数据可视化技术', '1.掌握数据分析技能 2.学习机器学习基础 3.提升数据处理能力', 32, 3, 5, 'PUBLISHED', '2025-04-01', '2025-09-30', '2025-09-30 23:59:59', '2025-07-23 15:14:58', '2025-07-23 15:14:58', NULL, NULL),
(8, '区块链应用开发', '基于以太坊开发智能合约和DApp应用', '1.了解区块链基本原理 2.熟悉Solidity语言 3.掌握Web3.js使用', '1.掌握区块链开发技术 2.理解去中心化应用架构 3.学习智能合约编程', 33, 5, 4, 'PUBLISHED', '2025-05-01', '2025-10-31', '2025-10-31 23:59:59', '2025-07-23 15:14:58', '2025-07-23 15:14:58', NULL, NULL);

-- --------------------------------------------------------

--
-- 表的结构 `records`
--

CREATE TABLE `records` (
  `id` bigint(20) NOT NULL,
  `type` enum('TASK','DISCUSSION','SUBMISSION','ANNOUNCEMENT','FEEDBACK','EVALUATION_ITEM') NOT NULL COMMENT '记录类型',
  `title` varchar(200) NOT NULL COMMENT '标题',
  `content` text COMMENT '内容',
  `project_id` bigint(20) DEFAULT NULL COMMENT '关联项目ID',
  `team_id` bigint(20) DEFAULT NULL COMMENT '关联团队ID',
  `user_id` bigint(20) NOT NULL COMMENT '创建用户ID',
  `parent_id` bigint(20) DEFAULT NULL COMMENT '父记录ID（用于回复）',
  `target_id` bigint(20) DEFAULT NULL COMMENT '目标ID（评价项目关联评价ID）',
  `status` varchar(20) DEFAULT NULL,
  `priority` enum('LOW','MEDIUM','HIGH','URGENT') DEFAULT 'MEDIUM' COMMENT '优先级',
  `due_date` datetime DEFAULT NULL COMMENT '截止时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `attachments` text,
  `extra_data` json DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='多功能记录表';

--
-- 转存表中的数据 `records`
--

INSERT INTO `records` (`id`, `type`, `title`, `content`, `project_id`, `team_id`, `user_id`, `parent_id`, `target_id`, `status`, `priority`, `due_date`, `create_time`, `update_time`, `attachments`, `extra_data`) VALUES
(2, 'DISCUSSION', '技术选型讨论', '关于前端框架的选择，Vue.js还是React？大家来讨论一下', 1, 2, 5, NULL, NULL, 'ACTIVE', 'MEDIUM', NULL, '2025-07-03 13:32:21', '2025-07-03 13:32:21', NULL, NULL),
(3, 'DISCUSSION', '移动端UI设计讨论', '移动端的用户界面设计风格讨论，简约风还是扁平化设计？', 2, 3, 6, NULL, NULL, 'ACTIVE', 'MEDIUM', NULL, '2025-07-03 13:32:21', '2025-07-03 13:32:21', NULL, NULL),
(4, 'TASK', '数据库设计任务', '开发系统的完整数据库11', 1, 1, 1, NULL, NULL, 'COMPLETED', 'MEDIUM', '2025-07-14 00:00:00', '2025-07-03 13:32:21', '2025-07-13 21:27:05', 'project_platform.sql;', NULL),
(5, 'TASK', '前端页面开发', '111', 1, 1, 1, NULL, NULL, 'COMPLETED', 'MEDIUM', '2025-07-23 16:00:00', '2025-07-03 13:32:21', '2025-08-08 12:04:08', NULL, NULL),
(6, 'TASK', 'API接口设计', '设计用户管理、课程管理相关的RESTful API接口', 1, 2, 1, NULL, NULL, 'ACTIVE', 'HIGH', NULL, '2025-07-03 13:32:21', '2025-07-03 13:32:21', NULL, NULL),
(7, 'TASK', '移动端原型设计', '完成移动端购物应用的原型设计和交互流程', 2, 3, 2, NULL, NULL, 'ACTIVE', 'MEDIUM', NULL, '2025-07-03 13:32:21', '2025-07-03 13:32:21', NULL, NULL),
(8, 'ANNOUNCEMENT', '项目启动会议通知', '各团队注意：项目启动会议定于本周五下午2点在会议室A举行，请准时参加', 1, 1, 1, NULL, NULL, 'ACTIVE', 'HIGH', NULL, '2025-07-03 13:32:21', '2025-07-11 17:35:26', NULL, NULL),
(9, 'ANNOUNCEMENT', '中期检查安排', '中期检查将在下月15日进行，请各团队提前准备项目演示和进度报告', 2, NULL, 2, NULL, NULL, 'ACTIVE', 'MEDIUM', NULL, '2025-07-03 13:32:21', '2025-07-03 13:32:21', NULL, NULL),
(10, 'SUBMISSION', '需求分析文档提交', '已完成在线学习平台的需求分析文档，包含功能需求和非功能需求', 1, 1, 4, NULL, NULL, 'ACTIVE', 'MEDIUM', NULL, '2025-07-03 13:32:21', '2025-07-03 13:32:21', NULL, NULL),
(11, 'SUBMISSION', '原型设计提交', '移动端购物应用的原型设计已完成，包含主要页面和交互流程', 2, 3, 6, NULL, NULL, 'ACTIVE', 'MEDIUM', NULL, '2025-07-03 13:32:21', '2025-07-03 13:32:21', NULL, NULL),
(20, 'TASK', '555', '1111111111', 1, 1, 1, NULL, NULL, 'COMPLETED', 'MEDIUM', '2025-08-26 16:00:00', '2025-07-12 11:43:14', '2025-07-13 21:25:27', '操作说明（必看）.docx;', NULL),
(36, 'DISCUSSION', '回复：项目启动会议通知', 'shhhhh\n', NULL, NULL, 4, 8, NULL, 'ACTIVE', 'MEDIUM', NULL, '2025-07-12 23:52:16', '2025-07-12 23:52:16', NULL, NULL),
(40, 'TASK', '1111111', '完成了', 1, 1, 1, NULL, NULL, 'SUBMITTED', 'MEDIUM', '2025-08-04 00:00:00', '2025-07-13 22:54:13', '2025-08-02 17:11:42', 'WallpaperEngineLockOverride_randomJDXGSC.jpg;', NULL),
(41, 'TASK', '用户界面设计', '设计系统的用户界面原型', 1, 1, 1, NULL, NULL, 'COMPLETED', 'HIGH', '2025-07-21 16:00:00', '2025-07-14 10:00:00', '2025-07-20 20:53:35', NULL, NULL),
(42, 'TASK', '功能测试', '已完成', 1, 1, 1, NULL, NULL, 'SUBMITTED', 'MEDIUM', '2025-07-25 00:00:00', '2025-07-14 11:00:00', '2025-08-02 17:10:03', 'WallpaperEngineLockOverride_randomJDXGSC.jpg;', NULL),
(43, 'SUBMISSION', '数据库设计文档', '提交完整的数据库设计文档', 1, 1, 4, NULL, NULL, 'ACTIVE', 'MEDIUM', NULL, '2025-07-14 12:00:00', '2025-07-14 12:00:00', NULL, NULL),
(44, 'SUBMISSION', '前端原型提交', '提交前端页面原型设计', 1, 1, 4, NULL, NULL, 'ACTIVE', 'MEDIUM', NULL, '2025-07-14 13:00:00', '2025-07-14 13:00:00', NULL, NULL),
(45, 'TASK', '2222222', '222222222222', 4, 7, 1, NULL, NULL, 'PUBLISHED', 'MEDIUM', '2025-07-30 00:00:00', '2025-07-20 20:54:24', '2025-07-20 20:54:24', '222222222222', NULL),
(47, 'DISCUSSION', '技术选型讨论', '关于数据分析项目的技术选型，建议使用pandas+matplotlib还是seaborn+plotly？', 7, 9, 36, NULL, NULL, 'ACTIVE', 'MEDIUM', NULL, '2025-07-23 15:15:48', '2025-07-23 15:15:48', NULL, NULL),
(48, 'SUBMISSION', '原型设计提交', '区块链DApp原型设计已完成，请查看附件中的设计文档和原型图。', 8, 10, 38, NULL, NULL, 'COMPLETED', 'MEDIUM', NULL, '2025-07-23 15:15:48', '2025-07-29 22:57:36', NULL, NULL),
(49, 'TASK', '11111111111111', '11111111111', 6, 16, 31, NULL, NULL, 'ACTIVE', 'MEDIUM', '2025-07-30 00:00:00', '2025-07-23 22:08:40', '2025-07-23 22:14:32', '111111111111111', NULL),
(54, 'DISCUSSION', '回复：项目启动会议通知', '2222222222222', NULL, NULL, 4, 8, NULL, 'ACTIVE', 'MEDIUM', NULL, '2025-08-02 11:40:07', '2025-08-02 11:40:07', NULL, NULL),
(56, 'DISCUSSION', '1111111111112', '111111111111122', NULL, 1, 4, NULL, NULL, 'ACTIVE', 'MEDIUM', NULL, '2025-08-02 11:42:20', '2025-08-02 11:42:29', NULL, NULL),
(57, 'DISCUSSION', '回复：1111111111112', '2222222222', NULL, NULL, 4, 56, NULL, 'ACTIVE', 'MEDIUM', NULL, '2025-08-02 11:42:37', '2025-08-02 11:42:37', NULL, NULL),
(58, 'DISCUSSION', '11', '11111111111111', NULL, 1, 4, NULL, NULL, 'ACTIVE', 'MEDIUM', NULL, '2025-08-02 11:44:59', '2025-08-02 11:44:59', NULL, NULL),
(59, 'DISCUSSION', '回复：11', '1111111111111', NULL, NULL, 4, 58, NULL, 'ACTIVE', 'MEDIUM', NULL, '2025-08-02 11:45:28', '2025-08-02 11:45:28', NULL, NULL),
(60, 'DISCUSSION', '回复：1111111111112', 'hello', NULL, NULL, 4, 56, NULL, 'ACTIVE', 'MEDIUM', NULL, '2025-08-02 11:46:06', '2025-08-02 11:46:06', NULL, NULL),
(61, 'DISCUSSION', '回复：11', '2222222222', NULL, NULL, 4, 58, NULL, 'ACTIVE', 'MEDIUM', NULL, '2025-08-02 11:53:31', '2025-08-02 11:53:31', NULL, NULL),
(64, 'SUBMISSION', '555', '11111111555', NULL, 1, 4, NULL, NULL, 'SUBMITTED', 'MEDIUM', NULL, '2025-08-02 13:06:03', '2025-08-02 13:06:03', NULL, NULL),
(72, 'SUBMISSION', '222222', '222222222222222', NULL, 1, 4, NULL, NULL, 'SUBMITTED', 'MEDIUM', NULL, '2025-08-02 16:42:36', '2025-08-02 16:42:36', NULL, NULL),
(77, 'DISCUSSION', '555', '555555555555555', 1, 1, 4, NULL, NULL, 'ACTIVE', 'MEDIUM', NULL, '2025-08-02 16:58:17', '2025-08-02 16:58:17', NULL, NULL);

-- --------------------------------------------------------

--
-- 表的结构 `teams`
--

CREATE TABLE `teams` (
  `id` bigint(20) NOT NULL,
  `name` varchar(100) NOT NULL COMMENT '团队名称',
  `description` text COMMENT '团队描述',
  `project_id` bigint(20) DEFAULT NULL COMMENT '关联项目ID',
  `leader_id` bigint(20) NOT NULL COMMENT '队长ID',
  `status` enum('FORMING','APPROVED','RECRUITING_STOPPED','PENDING','APPLIED','REJECTED','WORKING','COMPLETED') NOT NULL DEFAULT 'APPROVED' COMMENT '团队状态',
  `application_message` text COMMENT '申请信息',
  `teacher_feedback` text COMMENT '教师反馈',
  `current_members` int(11) DEFAULT '1' COMMENT '当前成员数',
  `max_members` int(11) DEFAULT '5' COMMENT '最大成员数',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='团队表';

--
-- 转存表中的数据 `teams`
--

INSERT INTO `teams` (`id`, `name`, `description`, `project_id`, `leader_id`, `status`, `application_message`, `teacher_feedback`, `current_members`, `max_members`, `create_time`, `update_time`) VALUES
(1, '前端精英队', '专注于前端技术的开发团队，致力于打造优秀的用户体验。团队成员在Vue.js、Element Plus、响应式设计等方面有丰富经验，注重代码质量和用户体验。12345', 1, 4, 'WORKING', '我们团队在前端开发方面有丰富经验，熟练掌握Vue3、TypeScript、Webpack等技术栈，希望能参与在线学习平台的开发，负责用户界面和交互体验的实现。', '团队前端技术实力强，项目经验丰富，批准参与项目。建议重点关注系统的响应式设计和用户体验优化。', 4, 5, '2025-07-03 13:32:21', '2025-08-08 11:46:54'),
(2, '全栈开发组', '全栈开发团队，前后端技术并重，具备完整的项目开发能力。团队成员技能互补，能够独立完成从需求分析到部署上线的全流程开发。', 1, 7, 'WORKING', '我们具备全栈开发能力，熟悉SpringBoot+Vue技术栈，有完整的项目开发经验，能够独立完成在线学习平台的设计、开发、测试和部署工作。', '团队技术能力全面，项目管理经验丰富，批准参与项目。期待你们能够交付高质量的学习平台系统。', 4, 5, '2025-07-03 13:32:21', '2025-07-03 13:32:21'),
(3, '移动先锋队', '专业的移动端开发团队，专注于移动应用的用户体验设计和性能优化。团队对电商业务有深入理解，注重产品的商业价值和用户粘性。', 2, 10, 'WORKING', '我们对移动端开发很有兴趣，团队成员熟悉React Native和Flutter框架，有电商类应用的开发经验，希望能参与购物应用的开发，打造优秀的移动购物体验。', NULL, 3, 4, '2025-07-03 13:32:21', '2025-07-11 15:59:36'),
(4, '数据分析团队', '专注于数据分析和AI技术的团队，在机器学习、数据挖掘、可视化等领域有扎实的理论基础和实践经验。致力于用数据驱动业务决策。', 3, 12, 'WORKING', '我们在数据科学和机器学习方面有扎实基础，熟悉Python、TensorFlow、Pandas等工具，有大数据处理和分析项目经验，希望参与智能数据分析系统的开发。', '团队专业背景符合项目需求，在数据分析和机器学习方面有良好基础，批准参与项目。建议重点关注系统的可扩展性和算法的实用性。', 3, 6, '2025-07-03 13:32:21', '2025-07-11 15:59:36'),
(5, '游戏创作室', '充满创意的游戏开发团队，专注于休闲游戏的设计和开发。团队成员在游戏策划、程序开发、美术设计等方面各有专长，注重游戏的趣味性和创新性。', 5, 18, 'WORKING', '我们热爱游戏开发，有创新的游戏设计理念，熟悉Unity引擎和C#编程，有2D游戏开发经验，希望能开发出有趣且具有商业价值的休闲游戏。', '团队创意能力强，游戏开发经验丰富，批准参与项目。期待你们能够设计出有创新性和可玩性的游戏作品。', 2, 4, '2025-07-03 13:32:21', '2025-07-11 15:59:36'),
(6, '创新实验室', '致力于技术创新和前沿技术探索的团队，关注新兴技术在实际业务中的应用。团队成员学习能力强，勇于挑战技术难题，正在寻找合适的创新项目。', NULL, 21, 'WORKING', NULL, NULL, 1, 5, '2025-07-03 13:32:21', '2025-07-17 20:08:55'),
(7, '新手练习队', '刚开始学习编程的新手团队，学习热情高涨，希望通过实际项目提升技术能力。虽然基础较薄弱，但团队成员学习积极性很高，愿意投入时间和精力。', 4, 23, 'WORKING', '我们虽然是编程新手，但学习热情很高，希望能通过参与校园信息管理系统项目来提升技术能力，我们会努力学习相关技术，保证项目质量。', '团队学习热情值得肯定，但当前技术基础较薄弱，建议先通过基础课程和小项目积累经验，提升技术能力后再申请参与复杂项目。', 2, 4, '2025-07-03 13:32:21', '2025-07-11 16:00:11'),
(9, '数据分析精英组', '专业数据分析团队', NULL, 36, 'RECRUITING_STOPPED', NULL, NULL, 3, 3, '2025-07-23 15:15:19', '2025-07-23 15:15:19'),
(10, '区块链创新团队', '区块链技术研究与应用团队', NULL, 38, 'APPROVED', NULL, NULL, 2, 5, '2025-07-23 15:15:19', '2025-07-23 15:15:19'),
(11, 'AI智能开发组', '人工智能应用开发团队', NULL, 40, 'FORMING', NULL, NULL, 1, 4, '2025-07-23 15:15:19', '2025-07-23 15:15:19'),
(16, '222222222222', '222222222222222', 6, 34, 'WORKING', '申请参与此项目', '申请通过', 1, 5, '2025-07-23 22:04:14', '2025-07-23 22:08:11');

-- --------------------------------------------------------

--
-- 表的结构 `team_applications`
--

CREATE TABLE `team_applications` (
  `id` bigint(20) NOT NULL,
  `team_id` bigint(20) NOT NULL COMMENT '团队ID',
  `user_id` bigint(20) NOT NULL COMMENT '申请用户ID',
  `status` enum('PENDING','APPROVED','REJECTED','CANCELLED') NOT NULL DEFAULT 'PENDING' COMMENT '申请状态',
  `application_message` text COMMENT '申请信息',
  `response_message` text COMMENT '回复信息',
  `apply_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '申请时间',
  `response_time` datetime DEFAULT NULL COMMENT '回复时间',
  `responder_id` bigint(20) DEFAULT NULL COMMENT '回复人ID'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='团队申请表';

--
-- 转存表中的数据 `team_applications`
--

INSERT INTO `team_applications` (`id`, `team_id`, `user_id`, `status`, `application_message`, `response_message`, `apply_time`, `response_time`, `responder_id`) VALUES
(1, 6, 24, 'PENDING', '我对技术创新很感兴趣，特别是新兴技术的应用。虽然我是新手，但学习能力强，希望能加入创新实验室团队，与大家一起探索前沿技术。', NULL, '2025-07-04 10:00:00', NULL, NULL),
(2, 4, 22, 'REJECTED', '我对数据分析很感兴趣，希望能参与智能数据分析系统项目，学习机器学习和数据处理技术。', '感谢你的申请！但我们团队目前需要有一定数据分析基础的成员。建议你先学习Python和基础统计学知识，提升技能后欢迎再次申请。', '2025-07-04 09:00:00', '2025-07-04 11:00:00', 12),
(3, 5, 22, 'APPROVED', '我对游戏开发很有兴趣，有一些Unity使用经验，希望能加入游戏创作室，参与2D休闲游戏的开发工作。', '欢迎加入！你的Unity经验正是我们需要的。请准备好投入游戏开发工作，我们一起创造有趣的游戏！', '2025-07-03 14:00:00', '2025-07-03 16:00:00', 18);

-- --------------------------------------------------------

--
-- 表的结构 `team_members`
--

CREATE TABLE `team_members` (
  `id` bigint(20) NOT NULL,
  `team_id` bigint(20) NOT NULL COMMENT '团队ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `role` enum('LEADER','MEMBER') NOT NULL DEFAULT 'MEMBER' COMMENT '成员角色',
  `status` enum('ACTIVE','INVITED','LEFT') NOT NULL DEFAULT 'ACTIVE' COMMENT '成员状态',
  `join_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '加入时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='团队成员表';

--
-- 转存表中的数据 `team_members`
--

INSERT INTO `team_members` (`id`, `team_id`, `user_id`, `role`, `status`, `join_time`) VALUES
(1, 1, 4, 'LEADER', 'ACTIVE', '2025-07-03 13:32:21'),
(2, 1, 5, 'MEMBER', 'ACTIVE', '2025-07-03 13:32:21'),
(3, 1, 6, 'MEMBER', 'ACTIVE', '2025-07-03 13:32:21'),
(4, 1, 8, 'MEMBER', 'LEFT', '2025-07-03 13:32:21'),
(5, 2, 7, 'LEADER', 'ACTIVE', '2025-07-03 13:32:21'),
(6, 2, 9, 'MEMBER', 'ACTIVE', '2025-07-03 13:32:21'),
(7, 2, 11, 'MEMBER', 'ACTIVE', '2025-07-03 13:32:21'),
(8, 2, 13, 'MEMBER', 'ACTIVE', '2025-07-03 13:32:21'),
(9, 3, 10, 'LEADER', 'ACTIVE', '2025-07-03 13:32:21'),
(10, 3, 17, 'MEMBER', 'ACTIVE', '2025-07-03 13:32:21'),
(11, 3, 19, 'MEMBER', 'ACTIVE', '2025-07-03 13:32:21'),
(12, 4, 12, 'LEADER', 'ACTIVE', '2025-07-03 13:32:21'),
(13, 4, 20, 'MEMBER', 'ACTIVE', '2025-07-03 13:32:21'),
(14, 4, 16, 'MEMBER', 'ACTIVE', '2025-07-03 13:32:21'),
(15, 5, 18, 'LEADER', 'ACTIVE', '2025-07-03 13:32:21'),
(16, 6, 21, 'LEADER', 'ACTIVE', '2025-07-03 13:32:21'),
(18, 7, 23, 'LEADER', 'ACTIVE', '2025-07-03 13:32:21'),
(19, 7, 24, 'MEMBER', 'ACTIVE', '2025-07-03 13:32:21'),
(20, 5, 22, 'MEMBER', 'ACTIVE', '2025-07-03 16:30:00'),
(22, 9, 36, 'LEADER', 'ACTIVE', '2025-07-23 15:15:19'),
(23, 9, 37, 'MEMBER', 'ACTIVE', '2025-07-23 15:15:19'),
(24, 9, 35, 'MEMBER', 'LEFT', '2025-07-23 15:15:19'),
(25, 10, 38, 'LEADER', 'ACTIVE', '2025-07-23 15:15:19'),
(26, 10, 39, 'MEMBER', 'ACTIVE', '2025-07-23 15:15:19'),
(27, 11, 40, 'LEADER', 'ACTIVE', '2025-07-23 15:15:19'),
(33, 16, 34, 'LEADER', 'ACTIVE', '2025-07-23 22:04:14');

-- --------------------------------------------------------

--
-- 表的结构 `users`
--

CREATE TABLE `users` (
  `id` bigint(20) NOT NULL,
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `password` varchar(100) NOT NULL COMMENT '密码',
  `email` varchar(100) NOT NULL COMMENT '邮箱',
  `real_name` varchar(50) NOT NULL COMMENT '真实姓名',
  `avatar` varchar(500) DEFAULT NULL COMMENT '头像URL',
  `role` enum('ADMIN','TEACHER','STUDENT') NOT NULL COMMENT '用户角色：ADMIN-管理员，TEACHER-教师，STUDENT-学生',
  `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
  `status` enum('ACTIVE','INACTIVE') NOT NULL DEFAULT 'ACTIVE' COMMENT '用户状态',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';

--
-- 转存表中的数据 `users`
--

INSERT INTO `users` (`id`, `username`, `password`, `email`, `real_name`, `avatar`, `role`, `phone`, `status`, `create_time`, `update_time`) VALUES
(1, 'teacher1', '123456', '<EMAIL>', '张教授', '/api/files/avatars/2025/07/31/avatar_35a77532-86b4-43a7-9a9b-e8a361d7bf62.jpg', 'TEACHER', '13800001001', 'ACTIVE', '2025-07-03 13:32:21', '2025-07-31 17:48:46'),
(2, 'teacher2', 'password123', '<EMAIL>', '李老师', '/api/files/avatars/2025/07/31/avatar_35a77532-86b4-43a7-9a9b-e8a361d7bf62.jpg', 'TEACHER', '13800001002', 'ACTIVE', '2025-07-03 13:32:21', '2025-07-31 17:48:58'),
(3, 'teacher3', 'password123', '<EMAIL>', '王教授', '/api/files/avatars/2025/07/31/avatar_35a77532-86b4-43a7-9a9b-e8a361d7bf62.jpg', 'TEACHER', '13800001003', 'ACTIVE', '2025-07-03 13:32:21', '2025-07-31 17:49:03'),
(4, 'student001', 'password123', '<EMAIL>', '陈小明', '/api/files/avatars/2025/07/31/avatar_35a77532-86b4-43a7-9a9b-e8a361d7bf62.jpg', 'STUDENT', '13900001001', 'ACTIVE', '2025-07-03 13:32:21', '2025-07-31 17:48:13'),
(5, 'student002', 'password123', '<EMAIL>', '刘小红', '/api/files/avatars/2025/07/31/avatar_35a77532-86b4-43a7-9a9b-e8a361d7bf62.jpg', 'STUDENT', '13900001002', 'ACTIVE', '2025-07-03 13:32:21', '2025-07-31 17:49:11'),
(6, 'student003', 'password123', '<EMAIL>', '张小强', '/api/files/avatars/2025/07/31/avatar_35a77532-86b4-43a7-9a9b-e8a361d7bf62.jpg', 'STUDENT', '13900001003', 'ACTIVE', '2025-07-03 13:32:21', '2025-07-31 17:49:22'),
(7, 'student004', 'password123', '<EMAIL>', '李小美', '/api/files/avatars/2025/07/31/avatar_35a77532-86b4-43a7-9a9b-e8a361d7bf62.jpg', 'STUDENT', '13900001004', 'ACTIVE', '2025-07-03 13:32:21', '2025-07-31 17:50:26'),
(8, 'student005', 'password123', '<EMAIL>', '王小华', '/api/files/avatars/2025/07/31/avatar_35a77532-86b4-43a7-9a9b-e8a361d7bf62.jpg', 'STUDENT', '13900001005', 'ACTIVE', '2025-07-03 13:32:21', '2025-07-31 17:50:31'),
(9, 'student006', 'password123', '<EMAIL>', '赵小龙', '/api/files/avatars/2025/07/31/avatar_35a77532-86b4-43a7-9a9b-e8a361d7bf62.jpg', 'STUDENT', '13900001006', 'ACTIVE', '2025-07-03 13:32:21', '2025-07-31 17:50:33'),
(10, 'student007', 'password123', '<EMAIL>', '孙小丽', '/api/files/avatars/2025/07/31/avatar_35a77532-86b4-43a7-9a9b-e8a361d7bf62.jpg', 'STUDENT', '13900001007', 'ACTIVE', '2025-07-03 13:32:21', '2025-07-31 17:50:35'),
(11, 'student008', 'password123', '<EMAIL>', '周小军', '/api/files/avatars/2025/07/31/avatar_35a77532-86b4-43a7-9a9b-e8a361d7bf62.jpg', 'STUDENT', '13900001008', 'ACTIVE', '2025-07-03 13:32:21', '2025-07-31 17:50:38'),
(12, 'student009', 'password123', '<EMAIL>', '吴小芳', '/api/files/avatars/2025/07/31/avatar_35a77532-86b4-43a7-9a9b-e8a361d7bf62.jpg', 'STUDENT', '13900001009', 'ACTIVE', '2025-07-03 13:32:21', '2025-07-31 17:50:40'),
(13, 'student010', 'password123', '<EMAIL>', '郑小伟', '/api/files/avatars/2025/07/31/avatar_35a77532-86b4-43a7-9a9b-e8a361d7bf62.jpg', 'STUDENT', '13900001010', 'ACTIVE', '2025-07-03 13:32:21', '2025-07-31 17:50:43'),
(14, 'admin', 'admin123', '<EMAIL>', '系统管理员', '/api/files/avatars/2025/07/19/avatar_fa2732f2-d7a1-449a-b622-3e4078951cf6.jpg', 'ADMIN', '13800000000', 'ACTIVE', '2025-07-03 13:32:21', '2025-07-19 21:08:11'),
(15, 'teacher4', 'password123', '<EMAIL>', '陈教授', '/api/files/avatars/2025/07/31/avatar_35a77532-86b4-43a7-9a9b-e8a361d7bf62.jpg', 'TEACHER', '13800001004', 'ACTIVE', '2025-07-03 13:32:21', '2025-07-31 17:50:46'),
(16, 'student011', 'password123', '<EMAIL>', '马小东', '/api/files/avatars/2025/07/31/avatar_35a77532-86b4-43a7-9a9b-e8a361d7bf62.jpg', 'STUDENT', '13900001011', 'ACTIVE', '2025-07-03 13:32:21', '2025-07-31 17:50:48'),
(17, 'student012', 'password123', '<EMAIL>', '黄小北', '/api/files/avatars/2025/07/31/avatar_35a77532-86b4-43a7-9a9b-e8a361d7bf62.jpg', 'STUDENT', '13900001012', 'ACTIVE', '2025-07-03 13:32:21', '2025-07-31 17:50:50'),
(18, 'student013', 'password123', '<EMAIL>', '徐小南', '/api/files/avatars/2025/07/31/avatar_35a77532-86b4-43a7-9a9b-e8a361d7bf62.jpg', 'STUDENT', '13900001013', 'ACTIVE', '2025-07-03 13:32:21', '2025-07-31 17:50:54'),
(19, 'student014', 'password123', '<EMAIL>', '林小中', '/api/files/avatars/2025/07/31/avatar_35a77532-86b4-43a7-9a9b-e8a361d7bf62.jpg', 'STUDENT', '13900001014', 'ACTIVE', '2025-07-03 13:32:21', '2025-07-31 17:50:57'),
(20, 'student015', 'password123', '<EMAIL>', '何小东', '/api/files/avatars/2025/07/31/avatar_35a77532-86b4-43a7-9a9b-e8a361d7bf62.jpg', 'STUDENT', '13900001015', 'ACTIVE', '2025-07-03 13:32:21', '2025-07-31 17:51:00'),
(21, 'student016', 'password123', '<EMAIL>', '高小杰', '/api/files/avatars/2025/07/31/avatar_35a77532-86b4-43a7-9a9b-e8a361d7bf62.jpg', 'STUDENT', '13900001016', 'ACTIVE', '2025-07-03 13:32:21', '2025-07-31 17:51:02'),
(22, 'student017', 'password123', '<EMAIL>', '冯小新', '/api/files/avatars/2025/07/31/avatar_35a77532-86b4-43a7-9a9b-e8a361d7bf62.jpg', 'STUDENT', '13900001017', 'ACTIVE', '2025-07-03 13:32:21', '2025-07-31 17:51:06'),
(23, 'student018', 'password123', '<EMAIL>', '邓小云', '/api/files/avatars/2025/07/31/avatar_35a77532-86b4-43a7-9a9b-e8a361d7bf62.jpg', 'STUDENT', '13900001018', 'ACTIVE', '2025-07-03 13:32:21', '2025-07-31 17:51:11'),
(24, 'student019', 'password123', '<EMAIL>', '唐小飞', '/api/files/avatars/2025/07/31/avatar_35a77532-86b4-43a7-9a9b-e8a361d7bf62.jpg', 'STUDENT', '13900001019', 'ACTIVE', '2025-07-03 13:32:21', '2025-07-31 17:51:14'),
(25, 'ggg', '123456', '<EMAIL>', 'ggg', '/api/files/avatars/2025/07/31/avatar_35a77532-86b4-43a7-9a9b-e8a361d7bf62.jpg', 'STUDENT', '15248957038', 'ACTIVE', '2025-07-11 15:49:34', '2025-07-31 17:51:17'),
(26, '111', 'temp39366', '<EMAIL>', 'gg', '/api/files/avatars/2025/07/31/avatar_35a77532-86b4-43a7-9a9b-e8a361d7bf62.jpg', 'STUDENT', NULL, 'ACTIVE', '2025-07-13 18:14:32', '2025-07-31 17:51:20'),
(27, '222', '123456', '<EMAIL>', '111', '/api/files/avatars/2025/07/31/avatar_35a77532-86b4-43a7-9a9b-e8a361d7bf62.jpg', 'STUDENT', NULL, 'ACTIVE', '2025-07-13 18:28:31', '2025-07-31 17:51:22'),
(28, '333', '123456', '<EMAIL>', '333', '/api/files/avatars/2025/07/31/avatar_35a77532-86b4-43a7-9a9b-e8a361d7bf62.jpg', 'STUDENT', NULL, 'ACTIVE', '2025-07-13 18:31:16', '2025-07-31 17:51:25'),
(29, '444', '123456', '<EMAIL>', '112', '/api/files/avatars/2025/07/31/avatar_35a77532-86b4-43a7-9a9b-e8a361d7bf62.jpg', 'STUDENT', NULL, 'ACTIVE', '2025-07-13 18:35:53', '2025-07-31 17:51:27'),
(30, 'testadmin', 'admin123', '<EMAIL>', '测试管理员', '/api/files/avatars/2025/07/31/avatar_35a77532-86b4-43a7-9a9b-e8a361d7bf62.jpg', 'ADMIN', '13900000001', 'ACTIVE', '2025-07-23 15:14:43', '2025-07-31 17:51:49'),
(31, 'testteacher1', 'password123', '<EMAIL>', '测试张老师', '/api/files/avatars/2025/07/31/avatar_35a77532-86b4-43a7-9a9b-e8a361d7bf62.jpg', 'TEACHER', '13900000002', 'ACTIVE', '2025-07-23 15:14:43', '2025-07-31 17:51:47'),
(32, 'testteacher2', 'password123', '<EMAIL>', '测试李老师', '/api/files/avatars/2025/07/31/avatar_35a77532-86b4-43a7-9a9b-e8a361d7bf62.jpg', 'TEACHER', '13900000003', 'ACTIVE', '2025-07-23 15:14:43', '2025-07-31 17:51:46'),
(33, 'testteacher3', 'password123', '<EMAIL>', '测试王老师', '/api/files/avatars/2025/07/31/avatar_35a77532-86b4-43a7-9a9b-e8a361d7bf62.jpg', 'TEACHER', '13900000004', 'ACTIVE', '2025-07-23 15:14:43', '2025-07-31 17:51:44'),
(34, 'teststudent01', '123456', '<EMAIL>', '测试学生某某', '/api/files/avatars/2025/07/23/avatar_56dffb7e-0d3a-416d-8ffe-505815fc7047.jpg', 'STUDENT', '13900000012', 'ACTIVE', '2025-07-23 15:14:43', '2025-07-23 15:28:24'),
(35, 'teststudent02', 'password123', '<EMAIL>', '测试学生乙', '/api/files/avatars/2025/07/31/avatar_35a77532-86b4-43a7-9a9b-e8a361d7bf62.jpg', 'STUDENT', '13900000012', 'ACTIVE', '2025-07-23 15:14:43', '2025-07-31 17:51:42'),
(36, 'teststudent03', 'password123', '<EMAIL>', '测试学生丙', '/api/files/avatars/2025/07/31/avatar_35a77532-86b4-43a7-9a9b-e8a361d7bf62.jpg', 'STUDENT', '13900000013', 'ACTIVE', '2025-07-23 15:14:43', '2025-07-31 17:51:41'),
(37, 'teststudent04', 'password123', '<EMAIL>', '测试学生丁', '/api/files/avatars/2025/07/31/avatar_35a77532-86b4-43a7-9a9b-e8a361d7bf62.jpg', 'STUDENT', '13900000014', 'ACTIVE', '2025-07-23 15:14:43', '2025-07-31 17:51:39'),
(38, 'teststudent05', 'password123', '<EMAIL>', '测试学生戊', '/api/files/avatars/2025/07/31/avatar_35a77532-86b4-43a7-9a9b-e8a361d7bf62.jpg', 'STUDENT', '13900000015', 'ACTIVE', '2025-07-23 15:14:43', '2025-07-31 17:51:37'),
(39, 'teststudent06', 'password123', '<EMAIL>', '测试学生己', '/api/files/avatars/2025/07/31/avatar_35a77532-86b4-43a7-9a9b-e8a361d7bf62.jpg', 'STUDENT', '13900000016', 'ACTIVE', '2025-07-23 15:14:43', '2025-07-31 17:51:35'),
(40, 'teststudent07', 'password123', '<EMAIL>', '测试学生庚', '/api/files/avatars/2025/07/31/avatar_35a77532-86b4-43a7-9a9b-e8a361d7bf62.jpg', 'STUDENT', '13900000017', 'ACTIVE', '2025-07-23 15:14:43', '2025-07-31 17:51:33'),
(41, 'teststudent08', 'password123', '<EMAIL>', '测试学生辛', '/api/files/avatars/2025/07/31/avatar_35a77532-86b4-43a7-9a9b-e8a361d7bf62.jpg', 'STUDENT', '13900000018', 'ACTIVE', '2025-07-23 15:14:43', '2025-07-31 17:51:31'),
(42, 'newteststudent01', 'password123', '<EMAIL>', '新测试学生01', '/api/files/avatars/2025/07/23/avatar_ecd8a68d-9ffb-4b43-93f2-ff07f3f4ddaf.jpg', 'STUDENT', '13900000101', 'ACTIVE', '2025-07-23 15:17:55', '2025-07-23 15:17:55');

--
-- 转储表的索引
--

--
-- 表的索引 `announcements`
--
ALTER TABLE `announcements`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_publisher_id` (`publisher_id`),
  ADD KEY `idx_status` (`status`),
  ADD KEY `idx_type` (`type`),
  ADD KEY `idx_target_audience` (`target_audience`),
  ADD KEY `idx_publish_time` (`publish_time`),
  ADD KEY `idx_is_pinned` (`is_pinned`);

--
-- 表的索引 `evaluations`
--
ALTER TABLE `evaluations`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_project_team` (`project_id`,`team_id`),
  ADD KEY `idx_project_id` (`project_id`),
  ADD KEY `idx_team_id` (`team_id`),
  ADD KEY `idx_evaluator_id` (`evaluator_id`),
  ADD KEY `idx_status` (`status`);

--
-- 表的索引 `file_info`
--
ALTER TABLE `file_info`
  ADD PRIMARY KEY (`id`),
  ADD KEY `FKjyfuhjyix6his6yrxr5glp1yb` (`project_id`),
  ADD KEY `FK6v9d0b30rmi8q607lff8uo8tw` (`record_id`),
  ADD KEY `FK54qlc8wk7sa0nqap6d9id10jk` (`team_id`),
  ADD KEY `FKlf2jdq1kkh5gy7v31e4l5qhob` (`uploader_id`);

--
-- 表的索引 `projects`
--
ALTER TABLE `projects`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_teacher_id` (`teacher_id`),
  ADD KEY `idx_status` (`status`);

--
-- 表的索引 `records`
--
ALTER TABLE `records`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_type` (`type`),
  ADD KEY `idx_project_id` (`project_id`),
  ADD KEY `idx_team_id` (`team_id`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_parent_id` (`parent_id`),
  ADD KEY `idx_target_id` (`target_id`);

--
-- 表的索引 `teams`
--
ALTER TABLE `teams`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_project_id` (`project_id`),
  ADD KEY `idx_leader_id` (`leader_id`),
  ADD KEY `idx_status` (`status`);

--
-- 表的索引 `team_applications`
--
ALTER TABLE `team_applications`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `uk_team_user_application` (`team_id`,`user_id`),
  ADD KEY `idx_team_id` (`team_id`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_status` (`status`),
  ADD KEY `idx_responder_id` (`responder_id`);

--
-- 表的索引 `team_members`
--
ALTER TABLE `team_members`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `uk_team_user` (`team_id`,`user_id`),
  ADD UNIQUE KEY `UKs8nuwsa7nvebc246ed822w68x` (`team_id`,`user_id`),
  ADD KEY `idx_team_id` (`team_id`),
  ADD KEY `idx_user_id` (`user_id`);

--
-- 表的索引 `users`
--
ALTER TABLE `users`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `username` (`username`),
  ADD UNIQUE KEY `email` (`email`),
  ADD KEY `idx_username` (`username`),
  ADD KEY `idx_email` (`email`),
  ADD KEY `idx_role` (`role`);

--
-- 在导出的表使用AUTO_INCREMENT
--

--
-- 使用表AUTO_INCREMENT `announcements`
--
ALTER TABLE `announcements`
  MODIFY `id` bigint(20) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=12;

--
-- 使用表AUTO_INCREMENT `evaluations`
--
ALTER TABLE `evaluations`
  MODIFY `id` bigint(20) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- 使用表AUTO_INCREMENT `file_info`
--
ALTER TABLE `file_info`
  MODIFY `id` bigint(20) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=21;

--
-- 使用表AUTO_INCREMENT `projects`
--
ALTER TABLE `projects`
  MODIFY `id` bigint(20) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=9;

--
-- 使用表AUTO_INCREMENT `records`
--
ALTER TABLE `records`
  MODIFY `id` bigint(20) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=78;

--
-- 使用表AUTO_INCREMENT `teams`
--
ALTER TABLE `teams`
  MODIFY `id` bigint(20) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=17;

--
-- 使用表AUTO_INCREMENT `team_applications`
--
ALTER TABLE `team_applications`
  MODIFY `id` bigint(20) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- 使用表AUTO_INCREMENT `team_members`
--
ALTER TABLE `team_members`
  MODIFY `id` bigint(20) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=34;

--
-- 使用表AUTO_INCREMENT `users`
--
ALTER TABLE `users`
  MODIFY `id` bigint(20) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=43;

--
-- 限制导出的表
--

--
-- 限制表 `announcements`
--
ALTER TABLE `announcements`
  ADD CONSTRAINT `announcements_ibfk_1` FOREIGN KEY (`publisher_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- 限制表 `evaluations`
--
ALTER TABLE `evaluations`
  ADD CONSTRAINT `evaluations_ibfk_1` FOREIGN KEY (`project_id`) REFERENCES `projects` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `evaluations_ibfk_2` FOREIGN KEY (`team_id`) REFERENCES `teams` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `evaluations_ibfk_3` FOREIGN KEY (`evaluator_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- 限制表 `file_info`
--
ALTER TABLE `file_info`
  ADD CONSTRAINT `FK54qlc8wk7sa0nqap6d9id10jk` FOREIGN KEY (`team_id`) REFERENCES `teams` (`id`),
  ADD CONSTRAINT `FK6v9d0b30rmi8q607lff8uo8tw` FOREIGN KEY (`record_id`) REFERENCES `records` (`id`),
  ADD CONSTRAINT `FKjyfuhjyix6his6yrxr5glp1yb` FOREIGN KEY (`project_id`) REFERENCES `projects` (`id`),
  ADD CONSTRAINT `FKlf2jdq1kkh5gy7v31e4l5qhob` FOREIGN KEY (`uploader_id`) REFERENCES `users` (`id`);

--
-- 限制表 `projects`
--
ALTER TABLE `projects`
  ADD CONSTRAINT `projects_ibfk_1` FOREIGN KEY (`teacher_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- 限制表 `records`
--
ALTER TABLE `records`
  ADD CONSTRAINT `records_ibfk_1` FOREIGN KEY (`project_id`) REFERENCES `projects` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `records_ibfk_2` FOREIGN KEY (`team_id`) REFERENCES `teams` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `records_ibfk_3` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `records_ibfk_4` FOREIGN KEY (`parent_id`) REFERENCES `records` (`id`) ON DELETE CASCADE;

--
-- 限制表 `teams`
--
ALTER TABLE `teams`
  ADD CONSTRAINT `teams_ibfk_1` FOREIGN KEY (`project_id`) REFERENCES `projects` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `teams_ibfk_2` FOREIGN KEY (`leader_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- 限制表 `team_applications`
--
ALTER TABLE `team_applications`
  ADD CONSTRAINT `team_applications_ibfk_1` FOREIGN KEY (`team_id`) REFERENCES `teams` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `team_applications_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `team_applications_ibfk_3` FOREIGN KEY (`responder_id`) REFERENCES `users` (`id`) ON DELETE SET NULL;

--
-- 限制表 `team_members`
--
ALTER TABLE `team_members`
  ADD CONSTRAINT `team_members_ibfk_1` FOREIGN KEY (`team_id`) REFERENCES `teams` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `team_members_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
