{"ast": null, "code": "import { createElementVNode as _createElementVNode, createCommentVNode as _createCommentVNode, resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, createBlock as _createBlock, createTextVNode as _createTextVNode, toDisplayString as _toDisplayString } from \"vue\";\nconst _hoisted_1 = {\n  class: \"task-review-container\"\n};\nconst _hoisted_2 = {\n  class: \"filter-section\"\n};\nconst _hoisted_3 = {\n  class: \"submissions-list\"\n};\nconst _hoisted_4 = {\n  key: 0,\n  class: \"loading-state\"\n};\nconst _hoisted_5 = {\n  key: 1,\n  class: \"empty-state\"\n};\nconst _hoisted_6 = {\n  key: 2,\n  class: \"submission-items\"\n};\nconst _hoisted_7 = {\n  class: \"submission-header\"\n};\nconst _hoisted_8 = {\n  class: \"submission-info\"\n};\nconst _hoisted_9 = {\n  class: \"task-title\"\n};\nconst _hoisted_10 = {\n  class: \"submission-meta\"\n};\nconst _hoisted_11 = {\n  class: \"submitter\"\n};\nconst _hoisted_12 = {\n  class: \"submit-time\"\n};\nconst _hoisted_13 = {\n  class: \"submission-actions\"\n};\nconst _hoisted_14 = {\n  class: \"submission-content\"\n};\nconst _hoisted_15 = {\n  class: \"content-preview\"\n};\nconst _hoisted_16 = {\n  key: 0,\n  class: \"attachments\"\n};\nconst _hoisted_17 = {\n  key: 0,\n  class: \"parent-task\"\n};\nconst _hoisted_18 = {\n  class: \"parent-task-header\"\n};\nconst _hoisted_19 = {\n  class: \"parent-task-content\"\n};\nconst _hoisted_20 = {\n  key: 0,\n  class: \"pagination\"\n};\nconst _hoisted_21 = {\n  key: 0,\n  class: \"submission-detail\"\n};\nconst _hoisted_22 = {\n  class: \"submission-content-detail\"\n};\nconst _hoisted_23 = {\n  class: \"attachment-list\"\n};\nconst _hoisted_24 = {\n  class: \"attachment-name\"\n};\nconst _hoisted_25 = {\n  key: 0,\n  class: \"original-task-section\"\n};\nconst _hoisted_26 = {\n  class: \"dialog-footer\"\n};\nconst _hoisted_27 = {\n  key: 0,\n  class: \"attachment-dialog\"\n};\nconst _hoisted_28 = {\n  class: \"attachment-info\"\n};\nconst _hoisted_29 = {\n  class: \"attachment-details\"\n};\nconst _hoisted_30 = {\n  class: \"attachment-name\"\n};\nconst _hoisted_31 = {\n  class: \"attachment-meta\"\n};\nconst _hoisted_32 = {\n  key: 0,\n  class: \"file-size\"\n};\nconst _hoisted_33 = {\n  key: 1,\n  class: \"file-type\"\n};\nconst _hoisted_34 = {\n  class: \"attachment-actions\"\n};\nconst _hoisted_35 = {\n  key: 1,\n  class: \"no-attachments\"\n};\nconst _hoisted_36 = {\n  class: \"image-preview\"\n};\nconst _hoisted_37 = [\"src\"];\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_el_option = _resolveComponent(\"el-option\");\n  const _component_el_select = _resolveComponent(\"el-select\");\n  const _component_el_col = _resolveComponent(\"el-col\");\n  const _component_Refresh = _resolveComponent(\"Refresh\");\n  const _component_el_icon = _resolveComponent(\"el-icon\");\n  const _component_el_button = _resolveComponent(\"el-button\");\n  const _component_el_row = _resolveComponent(\"el-row\");\n  const _component_el_skeleton = _resolveComponent(\"el-skeleton\");\n  const _component_el_empty = _resolveComponent(\"el-empty\");\n  const _component_el_tag = _resolveComponent(\"el-tag\");\n  const _component_Paperclip = _resolveComponent(\"Paperclip\");\n  const _component_Document = _resolveComponent(\"Document\");\n  const _component_el_card = _resolveComponent(\"el-card\");\n  const _component_el_pagination = _resolveComponent(\"el-pagination\");\n  const _component_el_descriptions_item = _resolveComponent(\"el-descriptions-item\");\n  const _component_el_descriptions = _resolveComponent(\"el-descriptions\");\n  const _component_el_dialog = _resolveComponent(\"el-dialog\");\n  const _component_Download = _resolveComponent(\"Download\");\n  const _component_View = _resolveComponent(\"View\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_el_card, {\n    class: \"page-card\"\n  }, {\n    header: _withCtx(() => _cache[13] || (_cache[13] = [_createElementVNode(\"div\", {\n      class: \"card-header\"\n    }, [_createElementVNode(\"h2\", null, \"任务审核\"), _createElementVNode(\"p\", {\n      class: \"subtitle\"\n    }, \"审核学生提交的任务\")], -1 /* CACHED */)])),\n    default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_2, [_createVNode(_component_el_row, {\n      gutter: 20\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_col, {\n        span: 6\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_select, {\n          modelValue: $setup.filterStatus,\n          \"onUpdate:modelValue\": _cache[0] || (_cache[0] = $event => $setup.filterStatus = $event),\n          placeholder: \"选择状态\",\n          onChange: $setup.loadSubmissions\n        }, {\n          default: _withCtx(() => [_createVNode(_component_el_option, {\n            label: \"全部\",\n            value: \"\"\n          }), _createVNode(_component_el_option, {\n            label: \"待审核\",\n            value: \"SUBMITTED\"\n          }), _createVNode(_component_el_option, {\n            label: \"已通过\",\n            value: \"COMPLETED\"\n          }), _createVNode(_component_el_option, {\n            label: \"已拒绝\",\n            value: \"REJECTED\"\n          })]),\n          _: 1 /* STABLE */\n        }, 8 /* PROPS */, [\"modelValue\", \"onChange\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_col, {\n        span: 6\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_select, {\n          modelValue: $setup.filterProject,\n          \"onUpdate:modelValue\": _cache[1] || (_cache[1] = $event => $setup.filterProject = $event),\n          placeholder: \"选择项目\",\n          onChange: $setup.loadSubmissions\n        }, {\n          default: _withCtx(() => [_createVNode(_component_el_option, {\n            label: \"全部项目\",\n            value: \"\"\n          }), (_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.myProjects, project => {\n            return _openBlock(), _createBlock(_component_el_option, {\n              key: project.id,\n              label: project.name || project.title,\n              value: project.id\n            }, null, 8 /* PROPS */, [\"label\", \"value\"]);\n          }), 128 /* KEYED_FRAGMENT */))]),\n          _: 1 /* STABLE */\n        }, 8 /* PROPS */, [\"modelValue\", \"onChange\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_col, {\n        span: 6\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_button, {\n          type: \"primary\",\n          onClick: $setup.loadSubmissions,\n          loading: $setup.loading\n        }, {\n          default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n            default: _withCtx(() => [_createVNode(_component_Refresh)]),\n            _: 1 /* STABLE */\n          }), _cache[14] || (_cache[14] = _createTextVNode(\" 刷新 \"))]),\n          _: 1 /* STABLE */,\n          __: [14]\n        }, 8 /* PROPS */, [\"onClick\", \"loading\"])]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    })]), _createElementVNode(\"div\", _hoisted_3, [$setup.loading ? (_openBlock(), _createElementBlock(\"div\", _hoisted_4, [_createVNode(_component_el_skeleton, {\n      rows: 5,\n      animated: \"\"\n    })])) : $setup.submissions.length === 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_5, [_createVNode(_component_el_empty, {\n      description: \"暂无任务提交\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_button, {\n        type: \"primary\",\n        onClick: $setup.loadSubmissions\n      }, {\n        default: _withCtx(() => _cache[15] || (_cache[15] = [_createTextVNode(\"刷新数据\")])),\n        _: 1 /* STABLE */,\n        __: [15]\n      }, 8 /* PROPS */, [\"onClick\"])]),\n      _: 1 /* STABLE */\n    })])) : (_openBlock(), _createElementBlock(\"div\", _hoisted_6, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.submissions, submission => {\n      return _openBlock(), _createElementBlock(\"div\", {\n        key: submission.id,\n        class: \"submission-item\"\n      }, [_createVNode(_component_el_card, {\n        class: \"submission-card\",\n        shadow: \"hover\"\n      }, {\n        default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_7, [_createElementVNode(\"div\", _hoisted_8, [_createElementVNode(\"h4\", _hoisted_9, _toDisplayString(submission.title), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_10, [_createVNode(_component_el_tag, {\n          type: $setup.getStatusColor(submission.status),\n          size: \"small\"\n        }, {\n          default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.getStatusText(submission.status)), 1 /* TEXT */)]),\n          _: 2 /* DYNAMIC */\n        }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"type\"]), _createElementVNode(\"span\", _hoisted_11, \"提交人：\" + _toDisplayString(submission.creator?.realName), 1 /* TEXT */), _createElementVNode(\"span\", _hoisted_12, \"提交时间：\" + _toDisplayString($setup.formatDate(submission.createTime)), 1 /* TEXT */)])]), _createElementVNode(\"div\", _hoisted_13, [_createVNode(_component_el_button, {\n          size: \"small\",\n          onClick: $event => $setup.viewSubmission(submission)\n        }, {\n          default: _withCtx(() => [...(_cache[16] || (_cache[16] = [_createTextVNode(\"查看详情\")]))]),\n          _: 2 /* DYNAMIC */,\n          __: [16]\n        }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\"]), submission.status === 'SUBMITTED' ? (_openBlock(), _createBlock(_component_el_button, {\n          key: 0,\n          type: \"success\",\n          size: \"small\",\n          onClick: $event => $setup.approveSubmission(submission)\n        }, {\n          default: _withCtx(() => [...(_cache[17] || (_cache[17] = [_createTextVNode(\" 通过 \")]))]),\n          _: 2 /* DYNAMIC */,\n          __: [17]\n        }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\"])) : _createCommentVNode(\"v-if\", true), submission.status === 'SUBMITTED' ? (_openBlock(), _createBlock(_component_el_button, {\n          key: 1,\n          type: \"danger\",\n          size: \"small\",\n          onClick: $event => $setup.rejectSubmission(submission)\n        }, {\n          default: _withCtx(() => [...(_cache[18] || (_cache[18] = [_createTextVNode(\" 拒绝 \")]))]),\n          _: 2 /* DYNAMIC */,\n          __: [18]\n        }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\"])) : _createCommentVNode(\"v-if\", true)])]), _createElementVNode(\"div\", _hoisted_14, [_createElementVNode(\"p\", _hoisted_15, _toDisplayString(submission.content), 1 /* TEXT */), $setup.hasAttachments(submission) ? (_openBlock(), _createElementBlock(\"div\", _hoisted_16, [_createVNode(_component_el_icon, null, {\n          default: _withCtx(() => [_createVNode(_component_Paperclip)]),\n          _: 1 /* STABLE */\n        }), _createElementVNode(\"span\", null, _toDisplayString($setup.getAttachmentCount(submission)) + \"个附件\", 1 /* TEXT */), _createVNode(_component_el_button, {\n          type: \"text\",\n          size: \"small\",\n          onClick: $event => $setup.viewAttachments(submission),\n          class: \"view-attachments-btn\"\n        }, {\n          default: _withCtx(() => [...(_cache[19] || (_cache[19] = [_createTextVNode(\" 查看附件 \")]))]),\n          _: 2 /* DYNAMIC */,\n          __: [19]\n        }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\"])])) : _createCommentVNode(\"v-if\", true)]), _createCommentVNode(\" 原任务信息 \"), submission.parentTask ? (_openBlock(), _createElementBlock(\"div\", _hoisted_17, [_createElementVNode(\"div\", _hoisted_18, [_createVNode(_component_el_icon, null, {\n          default: _withCtx(() => [_createVNode(_component_Document)]),\n          _: 1 /* STABLE */\n        }), _createElementVNode(\"span\", null, \"原任务：\" + _toDisplayString(submission.parentTask.title), 1 /* TEXT */)]), _createElementVNode(\"p\", _hoisted_19, _toDisplayString(submission.parentTask.content), 1 /* TEXT */)])) : _createCommentVNode(\"v-if\", true)]),\n        _: 2 /* DYNAMIC */\n      }, 1024 /* DYNAMIC_SLOTS */)]);\n    }), 128 /* KEYED_FRAGMENT */))]))]), $setup.total > 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_20, [_createVNode(_component_el_pagination, {\n      \"current-page\": $setup.currentPage,\n      \"onUpdate:currentPage\": _cache[2] || (_cache[2] = $event => $setup.currentPage = $event),\n      \"page-size\": $setup.pageSize,\n      \"onUpdate:pageSize\": _cache[3] || (_cache[3] = $event => $setup.pageSize = $event),\n      total: $setup.total,\n      \"page-sizes\": [10, 20, 50],\n      layout: \"total, sizes, prev, pager, next, jumper\",\n      onSizeChange: $setup.loadSubmissions,\n      onCurrentChange: $setup.loadSubmissions\n    }, null, 8 /* PROPS */, [\"current-page\", \"page-size\", \"total\", \"onSizeChange\", \"onCurrentChange\"])])) : _createCommentVNode(\"v-if\", true)]),\n    _: 1 /* STABLE */\n  }), _createCommentVNode(\" 提交详情对话框 \"), _createVNode(_component_el_dialog, {\n    modelValue: $setup.showDetailDialog,\n    \"onUpdate:modelValue\": _cache[7] || (_cache[7] = $event => $setup.showDetailDialog = $event),\n    title: \"提交详情\",\n    width: \"800px\"\n  }, {\n    footer: _withCtx(() => [_createElementVNode(\"div\", _hoisted_26, [_createVNode(_component_el_button, {\n      onClick: _cache[4] || (_cache[4] = $event => $setup.showDetailDialog = false)\n    }, {\n      default: _withCtx(() => _cache[22] || (_cache[22] = [_createTextVNode(\"关闭\")])),\n      _: 1 /* STABLE */,\n      __: [22]\n    }), $setup.selectedSubmission?.status === 'SUBMITTED' ? (_openBlock(), _createBlock(_component_el_button, {\n      key: 0,\n      type: \"success\",\n      onClick: _cache[5] || (_cache[5] = $event => $setup.approveSubmission($setup.selectedSubmission))\n    }, {\n      default: _withCtx(() => _cache[23] || (_cache[23] = [_createTextVNode(\" 通过审核 \")])),\n      _: 1 /* STABLE */,\n      __: [23]\n    })) : _createCommentVNode(\"v-if\", true), $setup.selectedSubmission?.status === 'SUBMITTED' ? (_openBlock(), _createBlock(_component_el_button, {\n      key: 1,\n      type: \"danger\",\n      onClick: _cache[6] || (_cache[6] = $event => $setup.rejectSubmission($setup.selectedSubmission))\n    }, {\n      default: _withCtx(() => _cache[24] || (_cache[24] = [_createTextVNode(\" 拒绝提交 \")])),\n      _: 1 /* STABLE */,\n      __: [24]\n    })) : _createCommentVNode(\"v-if\", true)])]),\n    default: _withCtx(() => [$setup.selectedSubmission ? (_openBlock(), _createElementBlock(\"div\", _hoisted_21, [_createVNode(_component_el_descriptions, {\n      column: 2,\n      border: \"\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_descriptions_item, {\n        label: \"任务标题\",\n        span: 2\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.selectedSubmission.parentTask?.title || '未知任务'), 1 /* TEXT */)]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_descriptions_item, {\n        label: \"提交标题\",\n        span: 2\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.selectedSubmission.title), 1 /* TEXT */)]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_descriptions_item, {\n        label: \"提交状态\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_tag, {\n          type: $setup.getStatusColor($setup.selectedSubmission.status)\n        }, {\n          default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.getStatusText($setup.selectedSubmission.status)), 1 /* TEXT */)]),\n          _: 1 /* STABLE */\n        }, 8 /* PROPS */, [\"type\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_descriptions_item, {\n        label: \"提交人\"\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.selectedSubmission.creator?.realName), 1 /* TEXT */)]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_descriptions_item, {\n        label: \"提交时间\",\n        span: 2\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.formatDate($setup.selectedSubmission.createTime)), 1 /* TEXT */)]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_descriptions_item, {\n        label: \"提交内容\",\n        span: 2\n      }, {\n        default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_22, _toDisplayString($setup.selectedSubmission.content), 1 /* TEXT */)]),\n        _: 1 /* STABLE */\n      }), $setup.hasAttachments($setup.selectedSubmission) ? (_openBlock(), _createBlock(_component_el_descriptions_item, {\n        key: 0,\n        label: \"附件列表\",\n        span: 2\n      }, {\n        default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_23, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.parseAttachments($setup.selectedSubmission.attachments), (attachment, index) => {\n          return _openBlock(), _createElementBlock(\"div\", {\n            key: index,\n            class: \"attachment-item\"\n          }, [_createVNode(_component_el_icon, null, {\n            default: _withCtx(() => [_createVNode(_component_Document)]),\n            _: 1 /* STABLE */\n          }), _createElementVNode(\"span\", _hoisted_24, _toDisplayString(attachment.name), 1 /* TEXT */), _createVNode(_component_el_button, {\n            type: \"text\",\n            size: \"small\",\n            onClick: $event => $setup.downloadAttachment(attachment),\n            class: \"download-btn\"\n          }, {\n            default: _withCtx(() => [...(_cache[20] || (_cache[20] = [_createTextVNode(\" 下载 \")]))]),\n            _: 2 /* DYNAMIC */,\n            __: [20]\n          }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\"])]);\n        }), 128 /* KEYED_FRAGMENT */))])]),\n        _: 1 /* STABLE */\n      })) : _createCommentVNode(\"v-if\", true)]),\n      _: 1 /* STABLE */\n    }), _createCommentVNode(\" 原任务详情 \"), $setup.selectedSubmission.parentTask ? (_openBlock(), _createElementBlock(\"div\", _hoisted_25, [_cache[21] || (_cache[21] = _createElementVNode(\"h4\", null, \"原任务详情\", -1 /* CACHED */)), _createVNode(_component_el_descriptions, {\n      column: 2,\n      border: \"\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_descriptions_item, {\n        label: \"任务标题\",\n        span: 2\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.selectedSubmission.parentTask.title), 1 /* TEXT */)]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_descriptions_item, {\n        label: \"任务内容\",\n        span: 2\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.selectedSubmission.parentTask.content), 1 /* TEXT */)]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_descriptions_item, {\n        label: \"优先级\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_tag, {\n          type: $setup.getPriorityColor($setup.selectedSubmission.parentTask.priority),\n          size: \"small\"\n        }, {\n          default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.getPriorityText($setup.selectedSubmission.parentTask.priority)), 1 /* TEXT */)]),\n          _: 1 /* STABLE */\n        }, 8 /* PROPS */, [\"type\"])]),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_descriptions_item, {\n        label: \"截止时间\"\n      }, {\n        default: _withCtx(() => [_createTextVNode(_toDisplayString($setup.formatDate($setup.selectedSubmission.parentTask.dueDate)), 1 /* TEXT */)]),\n        _: 1 /* STABLE */\n      })]),\n      _: 1 /* STABLE */\n    })])) : _createCommentVNode(\"v-if\", true)])) : _createCommentVNode(\"v-if\", true)]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\"]), _createCommentVNode(\" 附件查看对话框 \"), _createVNode(_component_el_dialog, {\n    modelValue: $setup.showAttachmentDialog,\n    \"onUpdate:modelValue\": _cache[9] || (_cache[9] = $event => $setup.showAttachmentDialog = $event),\n    title: \"附件列表\",\n    width: \"600px\"\n  }, {\n    footer: _withCtx(() => [_createVNode(_component_el_button, {\n      onClick: _cache[8] || (_cache[8] = $event => $setup.showAttachmentDialog = false)\n    }, {\n      default: _withCtx(() => _cache[27] || (_cache[27] = [_createTextVNode(\"关闭\")])),\n      _: 1 /* STABLE */,\n      __: [27]\n    }), $setup.selectedAttachments.length > 0 ? (_openBlock(), _createBlock(_component_el_button, {\n      key: 0,\n      type: \"primary\",\n      onClick: $setup.downloadAllAttachments\n    }, {\n      default: _withCtx(() => _cache[28] || (_cache[28] = [_createTextVNode(\" 下载全部 \")])),\n      _: 1 /* STABLE */,\n      __: [28]\n    }, 8 /* PROPS */, [\"onClick\"])) : _createCommentVNode(\"v-if\", true)]),\n    default: _withCtx(() => [$setup.selectedAttachments.length > 0 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_27, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.selectedAttachments, (attachment, index) => {\n      return _openBlock(), _createElementBlock(\"div\", {\n        key: index,\n        class: \"attachment-dialog-item\"\n      }, [_createElementVNode(\"div\", _hoisted_28, [_createVNode(_component_el_icon, {\n        size: \"20\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_Document)]),\n        _: 1 /* STABLE */\n      }), _createElementVNode(\"div\", _hoisted_29, [_createElementVNode(\"div\", _hoisted_30, _toDisplayString(attachment.name), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_31, [attachment.size ? (_openBlock(), _createElementBlock(\"span\", _hoisted_32, _toDisplayString($setup.formatFileSize(attachment.size)), 1 /* TEXT */)) : _createCommentVNode(\"v-if\", true), attachment.type ? (_openBlock(), _createElementBlock(\"span\", _hoisted_33, _toDisplayString(attachment.type), 1 /* TEXT */)) : _createCommentVNode(\"v-if\", true)])])]), _createElementVNode(\"div\", _hoisted_34, [_createVNode(_component_el_button, {\n        type: \"primary\",\n        size: \"small\",\n        onClick: $event => $setup.downloadAttachment(attachment)\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n          default: _withCtx(() => [_createVNode(_component_Download)]),\n          _: 1 /* STABLE */\n        }), _cache[25] || (_cache[25] = _createTextVNode(\" 下载 \"))]),\n        _: 2 /* DYNAMIC */,\n        __: [25]\n      }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\"]), $setup.isImageFile(attachment.name) ? (_openBlock(), _createBlock(_component_el_button, {\n        key: 0,\n        type: \"success\",\n        size: \"small\",\n        onClick: $event => $setup.previewAttachment(attachment)\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n          default: _withCtx(() => [_createVNode(_component_View)]),\n          _: 1 /* STABLE */\n        }), _cache[26] || (_cache[26] = _createTextVNode(\" 预览 \"))]),\n        _: 2 /* DYNAMIC */,\n        __: [26]\n      }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\"])) : _createCommentVNode(\"v-if\", true)])]);\n    }), 128 /* KEYED_FRAGMENT */))])) : (_openBlock(), _createElementBlock(\"div\", _hoisted_35, [_createVNode(_component_el_empty, {\n      description: \"暂无附件\"\n    })]))]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\"]), _createCommentVNode(\" 图片预览对话框 \"), _createVNode(_component_el_dialog, {\n    modelValue: $setup.showImagePreview,\n    \"onUpdate:modelValue\": _cache[12] || (_cache[12] = $event => $setup.showImagePreview = $event),\n    title: \"图片预览\",\n    width: \"80%\"\n  }, {\n    footer: _withCtx(() => [_createVNode(_component_el_button, {\n      onClick: _cache[10] || (_cache[10] = $event => $setup.showImagePreview = false)\n    }, {\n      default: _withCtx(() => _cache[29] || (_cache[29] = [_createTextVNode(\"关闭\")])),\n      _: 1 /* STABLE */,\n      __: [29]\n    }), _createVNode(_component_el_button, {\n      type: \"primary\",\n      onClick: _cache[11] || (_cache[11] = $event => $setup.downloadAttachment($setup.currentPreviewAttachment))\n    }, {\n      default: _withCtx(() => _cache[30] || (_cache[30] = [_createTextVNode(\" 下载原图 \")])),\n      _: 1 /* STABLE */,\n      __: [30]\n    })]),\n    default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_36, [$setup.previewImageUrl ? (_openBlock(), _createElementBlock(\"img\", {\n      key: 0,\n      src: $setup.previewImageUrl,\n      alt: \"预览图片\",\n      class: \"preview-image\"\n    }, null, 8 /* PROPS */, _hoisted_37)) : _createCommentVNode(\"v-if\", true)])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\"])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_el_card", "header", "_withCtx", "_cache", "_createElementVNode", "_hoisted_2", "_component_el_row", "gutter", "_component_el_col", "span", "_component_el_select", "$setup", "filterStatus", "$event", "placeholder", "onChange", "loadSubmissions", "_component_el_option", "label", "value", "filterProject", "_Fragment", "_renderList", "myProjects", "project", "_createBlock", "key", "id", "name", "title", "_component_el_button", "type", "onClick", "loading", "_component_el_icon", "_component_Refresh", "_hoisted_3", "_hoisted_4", "_component_el_skeleton", "rows", "animated", "submissions", "length", "_hoisted_5", "_component_el_empty", "description", "_hoisted_6", "submission", "shadow", "_hoisted_7", "_hoisted_8", "_hoisted_9", "_toDisplayString", "_hoisted_10", "_component_el_tag", "getStatusColor", "status", "size", "getStatusText", "_hoisted_11", "creator", "realName", "_hoisted_12", "formatDate", "createTime", "_hoisted_13", "viewSubmission", "approveSubmission", "rejectSubmission", "_hoisted_14", "_hoisted_15", "content", "hasAttachments", "_hoisted_16", "_component_Paperclip", "getAttachmentCount", "viewAttachments", "_createCommentVNode", "parentTask", "_hoisted_17", "_hoisted_18", "_component_Document", "_hoisted_19", "total", "_hoisted_20", "_component_el_pagination", "currentPage", "pageSize", "layout", "onSizeChange", "onCurrentChange", "_component_el_dialog", "showDetailDialog", "width", "footer", "_hoisted_26", "selectedSubmission", "_hoisted_21", "_component_el_descriptions", "column", "border", "_component_el_descriptions_item", "_hoisted_22", "_hoisted_23", "parseAttachments", "attachments", "attachment", "index", "_hoisted_24", "downloadAttachment", "_hoisted_25", "getPriorityColor", "priority", "getPriorityText", "dueDate", "showAttachmentDialog", "selectedAttachments", "downloadAllAttachments", "_hoisted_27", "_hoisted_28", "_hoisted_29", "_hoisted_30", "_hoisted_31", "_hoisted_32", "formatFileSize", "_hoisted_33", "_hoisted_34", "_component_Download", "isImageFile", "previewAttachment", "_component_View", "_hoisted_35", "showImagePreview", "currentPreviewAttachment", "_hoisted_36", "previewImageUrl", "src", "alt"], "sources": ["D:\\workspace\\idea\\worker\\work_cli\\src\\views\\task\\TaskReviewView.vue"], "sourcesContent": ["<template>\n  <div class=\"task-review-container\">\n    <el-card class=\"page-card\">\n      <template #header>\n        <div class=\"card-header\">\n          <h2>任务审核</h2>\n          <p class=\"subtitle\">审核学生提交的任务</p>\n        </div>\n      </template>\n\n      <!-- 筛选条件 -->\n      <div class=\"filter-section\">\n        <el-row :gutter=\"20\">\n          <el-col :span=\"6\">\n            <el-select v-model=\"filterStatus\" placeholder=\"选择状态\" @change=\"loadSubmissions\">\n              <el-option label=\"全部\" value=\"\" />\n              <el-option label=\"待审核\" value=\"SUBMITTED\" />\n              <el-option label=\"已通过\" value=\"COMPLETED\" />\n              <el-option label=\"已拒绝\" value=\"REJECTED\" />\n            </el-select>\n          </el-col>\n          <el-col :span=\"6\">\n            <el-select v-model=\"filterProject\" placeholder=\"选择项目\" @change=\"loadSubmissions\">\n              <el-option label=\"全部项目\" value=\"\" />\n              <el-option \n                v-for=\"project in myProjects\" \n                :key=\"project.id\" \n                :label=\"project.name || project.title\" \n                :value=\"project.id\" \n              />\n            </el-select>\n          </el-col>\n          <el-col :span=\"6\">\n            <el-button type=\"primary\" @click=\"loadSubmissions\" :loading=\"loading\">\n              <el-icon><Refresh /></el-icon>\n              刷新\n            </el-button>\n          </el-col>\n        </el-row>\n      </div>\n\n      <!-- 提交列表 -->\n      <div class=\"submissions-list\">\n        <div v-if=\"loading\" class=\"loading-state\">\n          <el-skeleton :rows=\"5\" animated />\n        </div>\n\n        <div v-else-if=\"submissions.length === 0\" class=\"empty-state\">\n          <el-empty description=\"暂无任务提交\">\n            <el-button type=\"primary\" @click=\"loadSubmissions\">刷新数据</el-button>\n          </el-empty>\n        </div>\n\n        <div v-else class=\"submission-items\">\n          <div v-for=\"submission in submissions\" :key=\"submission.id\" class=\"submission-item\">\n            <el-card class=\"submission-card\" shadow=\"hover\">\n              <div class=\"submission-header\">\n                <div class=\"submission-info\">\n                  <h4 class=\"task-title\">{{ submission.title }}</h4>\n                  <div class=\"submission-meta\">\n                    <el-tag :type=\"getStatusColor(submission.status)\" size=\"small\">\n                      {{ getStatusText(submission.status) }}\n                    </el-tag>\n                    <span class=\"submitter\">提交人：{{ submission.creator?.realName }}</span>\n                    <span class=\"submit-time\">提交时间：{{ formatDate(submission.createTime) }}</span>\n                  </div>\n                </div>\n                <div class=\"submission-actions\">\n                  <el-button size=\"small\" @click=\"viewSubmission(submission)\">查看详情</el-button>\n                  <el-button \n                    v-if=\"submission.status === 'SUBMITTED'\"\n                    type=\"success\" \n                    size=\"small\" \n                    @click=\"approveSubmission(submission)\"\n                  >\n                    通过\n                  </el-button>\n                  <el-button \n                    v-if=\"submission.status === 'SUBMITTED'\"\n                    type=\"danger\" \n                    size=\"small\" \n                    @click=\"rejectSubmission(submission)\"\n                  >\n                    拒绝\n                  </el-button>\n                </div>\n              </div>\n              \n              <div class=\"submission-content\">\n                <p class=\"content-preview\">{{ submission.content }}</p>\n                <div v-if=\"hasAttachments(submission)\" class=\"attachments\">\n                  <el-icon><Paperclip /></el-icon>\n                  <span>{{ getAttachmentCount(submission) }}个附件</span>\n                  <el-button\n                    type=\"text\"\n                    size=\"small\"\n                    @click=\"viewAttachments(submission)\"\n                    class=\"view-attachments-btn\"\n                  >\n                    查看附件\n                  </el-button>\n                </div>\n              </div>\n\n              <!-- 原任务信息 -->\n              <div v-if=\"submission.parentTask\" class=\"parent-task\">\n                <div class=\"parent-task-header\">\n                  <el-icon><Document /></el-icon>\n                  <span>原任务：{{ submission.parentTask.title }}</span>\n                </div>\n                <p class=\"parent-task-content\">{{ submission.parentTask.content }}</p>\n              </div>\n            </el-card>\n          </div>\n        </div>\n      </div>\n\n      <!-- 分页 -->\n      <div v-if=\"total > 0\" class=\"pagination\">\n        <el-pagination\n          v-model:current-page=\"currentPage\"\n          v-model:page-size=\"pageSize\"\n          :total=\"total\"\n          :page-sizes=\"[10, 20, 50]\"\n          layout=\"total, sizes, prev, pager, next, jumper\"\n          @size-change=\"loadSubmissions\"\n          @current-change=\"loadSubmissions\"\n        />\n      </div>\n    </el-card>\n\n    <!-- 提交详情对话框 -->\n    <el-dialog v-model=\"showDetailDialog\" title=\"提交详情\" width=\"800px\">\n      <div v-if=\"selectedSubmission\" class=\"submission-detail\">\n        <el-descriptions :column=\"2\" border>\n          <el-descriptions-item label=\"任务标题\" :span=\"2\">\n            {{ selectedSubmission.parentTask?.title || '未知任务' }}\n          </el-descriptions-item>\n          <el-descriptions-item label=\"提交标题\" :span=\"2\">\n            {{ selectedSubmission.title }}\n          </el-descriptions-item>\n          <el-descriptions-item label=\"提交状态\">\n            <el-tag :type=\"getStatusColor(selectedSubmission.status)\">\n              {{ getStatusText(selectedSubmission.status) }}\n            </el-tag>\n          </el-descriptions-item>\n          <el-descriptions-item label=\"提交人\">\n            {{ selectedSubmission.creator?.realName }}\n          </el-descriptions-item>\n          <el-descriptions-item label=\"提交时间\" :span=\"2\">\n            {{ formatDate(selectedSubmission.createTime) }}\n          </el-descriptions-item>\n          <el-descriptions-item label=\"提交内容\" :span=\"2\">\n            <div class=\"submission-content-detail\">\n              {{ selectedSubmission.content }}\n            </div>\n          </el-descriptions-item>\n          <el-descriptions-item v-if=\"hasAttachments(selectedSubmission)\" label=\"附件列表\" :span=\"2\">\n            <div class=\"attachment-list\">\n              <div\n                v-for=\"(attachment, index) in parseAttachments(selectedSubmission.attachments)\"\n                :key=\"index\"\n                class=\"attachment-item\"\n              >\n                <el-icon><Document /></el-icon>\n                <span class=\"attachment-name\">{{ attachment.name }}</span>\n                <el-button\n                  type=\"text\"\n                  size=\"small\"\n                  @click=\"downloadAttachment(attachment)\"\n                  class=\"download-btn\"\n                >\n                  下载\n                </el-button>\n              </div>\n            </div>\n          </el-descriptions-item>\n        </el-descriptions>\n\n        <!-- 原任务详情 -->\n        <div v-if=\"selectedSubmission.parentTask\" class=\"original-task-section\">\n          <h4>原任务详情</h4>\n          <el-descriptions :column=\"2\" border>\n            <el-descriptions-item label=\"任务标题\" :span=\"2\">\n              {{ selectedSubmission.parentTask.title }}\n            </el-descriptions-item>\n            <el-descriptions-item label=\"任务内容\" :span=\"2\">\n              {{ selectedSubmission.parentTask.content }}\n            </el-descriptions-item>\n            <el-descriptions-item label=\"优先级\">\n              <el-tag :type=\"getPriorityColor(selectedSubmission.parentTask.priority)\" size=\"small\">\n                {{ getPriorityText(selectedSubmission.parentTask.priority) }}\n              </el-tag>\n            </el-descriptions-item>\n            <el-descriptions-item label=\"截止时间\">\n              {{ formatDate(selectedSubmission.parentTask.dueDate) }}\n            </el-descriptions-item>\n          </el-descriptions>\n        </div>\n      </div>\n\n      <template #footer>\n        <div class=\"dialog-footer\">\n          <el-button @click=\"showDetailDialog = false\">关闭</el-button>\n          <el-button \n            v-if=\"selectedSubmission?.status === 'SUBMITTED'\"\n            type=\"success\" \n            @click=\"approveSubmission(selectedSubmission)\"\n          >\n            通过审核\n          </el-button>\n          <el-button \n            v-if=\"selectedSubmission?.status === 'SUBMITTED'\"\n            type=\"danger\" \n            @click=\"rejectSubmission(selectedSubmission)\"\n          >\n            拒绝提交\n          </el-button>\n        </div>\n      </template>\n    </el-dialog>\n\n    <!-- 附件查看对话框 -->\n    <el-dialog v-model=\"showAttachmentDialog\" title=\"附件列表\" width=\"600px\">\n      <div v-if=\"selectedAttachments.length > 0\" class=\"attachment-dialog\">\n        <div\n          v-for=\"(attachment, index) in selectedAttachments\"\n          :key=\"index\"\n          class=\"attachment-dialog-item\"\n        >\n          <div class=\"attachment-info\">\n            <el-icon size=\"20\"><Document /></el-icon>\n            <div class=\"attachment-details\">\n              <div class=\"attachment-name\">{{ attachment.name }}</div>\n              <div class=\"attachment-meta\">\n                <span v-if=\"attachment.size\" class=\"file-size\">{{ formatFileSize(attachment.size) }}</span>\n                <span v-if=\"attachment.type\" class=\"file-type\">{{ attachment.type }}</span>\n              </div>\n            </div>\n          </div>\n          <div class=\"attachment-actions\">\n            <el-button\n              type=\"primary\"\n              size=\"small\"\n              @click=\"downloadAttachment(attachment)\"\n            >\n              <el-icon><Download /></el-icon>\n              下载\n            </el-button>\n            <el-button\n              v-if=\"isImageFile(attachment.name)\"\n              type=\"success\"\n              size=\"small\"\n              @click=\"previewAttachment(attachment)\"\n            >\n              <el-icon><View /></el-icon>\n              预览\n            </el-button>\n          </div>\n        </div>\n      </div>\n      <div v-else class=\"no-attachments\">\n        <el-empty description=\"暂无附件\" />\n      </div>\n\n      <template #footer>\n        <el-button @click=\"showAttachmentDialog = false\">关闭</el-button>\n        <el-button\n          v-if=\"selectedAttachments.length > 0\"\n          type=\"primary\"\n          @click=\"downloadAllAttachments\"\n        >\n          下载全部\n        </el-button>\n      </template>\n    </el-dialog>\n\n    <!-- 图片预览对话框 -->\n    <el-dialog v-model=\"showImagePreview\" title=\"图片预览\" width=\"80%\">\n      <div class=\"image-preview\">\n        <img\n          v-if=\"previewImageUrl\"\n          :src=\"previewImageUrl\"\n          alt=\"预览图片\"\n          class=\"preview-image\"\n        />\n      </div>\n      <template #footer>\n        <el-button @click=\"showImagePreview = false\">关闭</el-button>\n        <el-button\n          type=\"primary\"\n          @click=\"downloadAttachment(currentPreviewAttachment)\"\n        >\n          下载原图\n        </el-button>\n      </template>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { ref, reactive, computed, onMounted } from 'vue'\nimport { ElMessage, ElMessageBox } from 'element-plus'\nimport { Refresh, Paperclip, Document, Download, View } from '@element-plus/icons-vue'\nimport { recordAPI, projectAPI, fileAPI } from '@/api'\nimport { useStore } from 'vuex'\n\nexport default {\n  name: 'TaskReviewView',\n  components: {\n    Refresh,\n    Paperclip,\n    Document,\n    Download,\n    View\n  },\n  setup() {\n    const store = useStore()\n    const currentUser = computed(() => store.getters.user)\n\n    // 响应式数据\n    const loading = ref(false)\n    const submissions = ref([])\n    const myProjects = ref([])\n    const total = ref(0)\n    const currentPage = ref(1)\n    const pageSize = ref(20)\n\n    // 筛选条件\n    const filterStatus = ref('SUBMITTED') // 默认显示待审核\n    const filterProject = ref('')\n\n    // 对话框\n    const showDetailDialog = ref(false)\n    const selectedSubmission = ref(null)\n\n    // 附件相关\n    const showAttachmentDialog = ref(false)\n    const selectedAttachments = ref([])\n    const showImagePreview = ref(false)\n    const previewImageUrl = ref('')\n    const currentPreviewAttachment = ref(null)\n\n    // 加载我的项目\n    const loadMyProjects = async () => {\n      try {\n        const response = await projectAPI.getMyProjects()\n        myProjects.value = response?.records || []\n      } catch (error) {\n        console.error('加载项目失败:', error)\n      }\n    }\n\n    // 加载提交列表\n    const loadSubmissions = async () => {\n      try {\n        loading.value = true\n        const params = {\n          page: currentPage.value,\n          size: pageSize.value,\n          type: 'TASK',  // 查询任务类型，而不是SUBMISSION\n          sortBy: 'updateTime',  // 按更新时间排序\n          sortDir: 'desc'\n        }\n\n        const response = await recordAPI.getRecords(params)\n        let allSubmissions = response?.records || []\n\n        // 筛选已提交的任务（状态为SUBMITTED、COMPLETED、REJECTED）\n        allSubmissions = allSubmissions.filter(task =>\n          ['SUBMITTED', 'COMPLETED', 'REJECTED'].includes(task.status)\n        )\n\n        // 前端筛选状态\n        if (filterStatus.value) {\n          allSubmissions = allSubmissions.filter(submission =>\n            submission.status === filterStatus.value\n          )\n        }\n\n        // 前端筛选项目\n        if (filterProject.value) {\n          allSubmissions = allSubmissions.filter(submission =>\n            submission.projectId === filterProject.value\n          )\n        }\n\n        submissions.value = allSubmissions\n        total.value = allSubmissions.length\n\n        console.log('加载的提交任务:', allSubmissions.map(task => ({\n          id: task.id,\n          title: task.title,\n          status: task.status,\n          updateTime: task.updateTime\n        })))\n      } catch (error) {\n        console.error('加载提交列表失败:', error)\n        ElMessage.error('加载提交列表失败')\n      } finally {\n        loading.value = false\n      }\n    }\n\n    // 加载原任务信息和附件信息\n    const loadParentTasks = async () => {\n      for (const submission of submissions.value) {\n        // 加载原任务信息\n        if (submission.parentId) {\n          try {\n            const parentTask = await recordAPI.getRecord(submission.parentId)\n            submission.parentTask = parentTask\n          } catch (error) {\n            console.error('加载原任务失败:', error)\n          }\n        }\n\n        // 加载附件信息\n        if (submission.id) {\n          try {\n            console.log(`🔍 正在加载提交 ${submission.id} 的附件信息...`)\n            const response = await fileAPI.getRecordFiles(submission.id)\n            console.log(`📎 提交 ${submission.id} 的附件响应:`, response)\n\n            const attachmentFiles = response?.data || response || []\n            if (attachmentFiles && attachmentFiles.length > 0) {\n              // 将真实的文件信息存储到submission中\n              submission.attachmentFiles = attachmentFiles\n              // 更新attachments字段为文件数量提示\n              submission.attachments = `${attachmentFiles.length}个文件`\n              console.log(`✅ 提交 ${submission.id} 加载了 ${attachmentFiles.length} 个附件`)\n\n              // 验证文件信息完整性\n              attachmentFiles.forEach((file, index) => {\n                if (!file.id) {\n                  console.warn(`⚠️ 文件 ${index} 缺少ID:`, file)\n                }\n              })\n            } else {\n              console.log(`ℹ️ 提交 ${submission.id} 没有附件`)\n            }\n          } catch (error) {\n            console.error(`❌ 加载提交 ${submission.id} 的附件信息失败:`, error)\n            console.error('❌ 错误详情:', {\n              status: error.response?.status,\n              message: error.message,\n              data: error.response?.data\n            })\n          }\n        }\n      }\n    }\n\n    // 查看提交详情\n    const viewSubmission = (submission) => {\n      selectedSubmission.value = submission\n      showDetailDialog.value = true\n    }\n\n    // 通过提交\n    const approveSubmission = async (submission) => {\n      try {\n        await ElMessageBox.confirm(\n          `确定要通过\"${submission.title}\"的提交吗？`,\n          '通过审核',\n          {\n            confirmButtonText: '通过',\n            cancelButtonText: '取消',\n            type: 'success'\n          }\n        )\n\n        await recordAPI.updateTaskStatus(submission.id, 'COMPLETED')\n        ElMessage.success('审核通过')\n        showDetailDialog.value = false\n        loadSubmissions()\n      } catch (error) {\n        if (error !== 'cancel') {\n          console.error('审核失败:', error)\n          ElMessage.error('审核失败')\n        }\n      }\n    }\n\n    // 拒绝提交\n    const rejectSubmission = async (submission) => {\n      try {\n        const { value: feedback } = await ElMessageBox.prompt(\n          '请输入拒绝理由：',\n          '拒绝提交',\n          {\n            confirmButtonText: '确定',\n            cancelButtonText: '取消',\n            inputType: 'textarea',\n            inputPlaceholder: '请详细说明拒绝的原因...'\n          }\n        )\n\n        await recordAPI.updateTaskStatus(submission.id, 'REJECTED')\n        ElMessage.success('已拒绝提交')\n        showDetailDialog.value = false\n        loadSubmissions()\n      } catch (error) {\n        if (error !== 'cancel') {\n          console.error('拒绝失败:', error)\n          ElMessage.error('拒绝失败')\n        }\n      }\n    }\n\n    // 状态相关方法\n    const getStatusColor = (status) => {\n      const colorMap = {\n        'SUBMITTED': 'warning',\n        'COMPLETED': 'success',\n        'REJECTED': 'danger'\n      }\n      return colorMap[status] || 'info'\n    }\n\n    const getStatusText = (status) => {\n      const textMap = {\n        'SUBMITTED': '待审核',\n        'COMPLETED': '已通过',\n        'REJECTED': '已拒绝'\n      }\n      return textMap[status] || status\n    }\n\n    const getPriorityColor = (priority) => {\n      if (typeof priority === 'number') {\n        const numberToEnum = {\n          1: 'LOW',\n          2: 'MEDIUM',\n          3: 'HIGH',\n          4: 'URGENT',\n          5: 'URGENT'\n        }\n        priority = numberToEnum[priority] || 'MEDIUM'\n      }\n      \n      const colorMap = {\n        'LOW': 'info',\n        'MEDIUM': 'primary',\n        'HIGH': 'warning',\n        'URGENT': 'danger'\n      }\n      return colorMap[priority] || 'info'\n    }\n\n    const getPriorityText = (priority) => {\n      if (typeof priority === 'number') {\n        const numberToEnum = {\n          1: 'LOW',\n          2: 'MEDIUM',\n          3: 'HIGH',\n          4: 'URGENT',\n          5: 'URGENT'\n        }\n        priority = numberToEnum[priority] || 'MEDIUM'\n      }\n      \n      const textMap = {\n        'LOW': '低',\n        'MEDIUM': '中',\n        'HIGH': '高',\n        'URGENT': '紧急'\n      }\n      return textMap[priority] || '中'\n    }\n\n    // 格式化日期\n    const formatDate = (dateString) => {\n      if (!dateString) return '-'\n      const date = new Date(dateString)\n      return date.toLocaleString('zh-CN')\n    }\n\n    // 附件相关方法\n    const hasAttachments = (submission) => {\n      // 优先检查真实的文件信息\n      if (submission.attachmentFiles && submission.attachmentFiles.length > 0) {\n        return true\n      }\n\n      // 检查attachments字段\n      const attachments = submission.attachments\n      if (!attachments || attachments.trim() === '') {\n        return false\n      }\n\n      try {\n        // 如果是JSON格式\n        if (attachments.startsWith('{') || attachments.startsWith('[')) {\n          const parsed = JSON.parse(attachments)\n          if (Array.isArray(parsed)) {\n            return parsed.length > 0\n          }\n          return true\n        }\n        // 如果是分号分隔的文件名，检查是否有非空的文件名\n        const fileNames = attachments.split(';').filter(name => name.trim())\n        return fileNames.length > 0\n      } catch (error) {\n        // 如果解析失败，按分号分隔检查\n        const fileNames = attachments.split(';').filter(name => name.trim())\n        return fileNames.length > 0\n      }\n    }\n\n    const getAttachmentCount = (submission) => {\n      // 优先使用真实的文件信息\n      if (submission.attachmentFiles && submission.attachmentFiles.length > 0) {\n        return submission.attachmentFiles.length\n      }\n\n      // 回退到解析attachments字段\n      const attachments = submission.attachments\n      if (!attachments) return 0\n\n      try {\n        // 如果是JSON格式\n        if (attachments.startsWith('{') || attachments.startsWith('[')) {\n          const parsed = JSON.parse(attachments)\n          if (Array.isArray(parsed)) {\n            return parsed.length\n          }\n          return 1\n        }\n        // 如果是分号分隔的文件名\n        return attachments.split(';').filter(name => name.trim()).length\n      } catch (error) {\n        // 如果解析失败，按分号分隔计算\n        return attachments.split(';').filter(name => name.trim()).length\n      }\n    }\n\n    const parseAttachments = (attachments) => {\n      if (!attachments) return []\n\n      try {\n        // 尝试解析JSON格式\n        if (attachments.startsWith('{') || attachments.startsWith('[')) {\n          const parsed = JSON.parse(attachments)\n          if (Array.isArray(parsed)) {\n            return parsed.map(item => ({\n              name: item.name || item.filename || item,\n              id: item.id,\n              url: item.url || item.path,\n              size: item.size,\n              type: item.type || item.contentType\n            }))\n          }\n          return [{\n            name: parsed.name || parsed.filename || '未知文件',\n            id: parsed.id,\n            url: parsed.url || parsed.path,\n            size: parsed.size,\n            type: parsed.type || parsed.contentType\n          }]\n        }\n\n        // 按分号分隔的简单格式 - 需要查找文件ID\n        return attachments.split(';')\n          .filter(name => name.trim())\n          .map(name => ({\n            name: name.trim(),\n            id: null,  // 需要通过API查找\n            url: null,\n            size: null,\n            type: null,\n            needsLookup: true  // 标记需要查找文件ID\n          }))\n      } catch (error) {\n        console.error('解析附件信息失败:', error)\n        return [{\n          name: '附件解析失败',\n          url: null,\n          size: null,\n          type: null\n        }]\n      }\n    }\n\n    const viewAttachments = async (submission) => {\n      console.log('🔍 查看附件 - 提交记录:', submission)\n\n      // 优先使用真实的文件信息\n      if (submission.attachmentFiles && submission.attachmentFiles.length > 0) {\n        console.log('✅ 使用已加载的附件文件信息:', submission.attachmentFiles)\n        selectedAttachments.value = submission.attachmentFiles.map(file => ({\n          id: file.id,\n          name: file.originalName || file.fileName || file.name,\n          url: `/api/files/${file.id}/download`,\n          previewUrl: `/api/files/${file.id}/preview`,\n          size: file.fileSize || file.size,\n          type: file.fileType || file.contentType || file.type,\n          uploadTime: file.uploadTime || file.createTime\n        }))\n      } else {\n        console.log('🔄 需要查找附件文件信息...')\n\n        // 首先尝试直接通过记录ID获取文件\n        try {\n          console.log(`📡 调用 fileAPI.getRecordFiles(${submission.id})`)\n          const response = await fileAPI.getRecordFiles(submission.id)\n          console.log('📡 API响应:', response)\n\n          const files = response?.data || response || []\n          if (files && files.length > 0) {\n            console.log('✅ 成功获取文件列表:', files)\n            selectedAttachments.value = files.map(file => ({\n              id: file.id,\n              name: file.originalName || file.fileName || file.name,\n              url: `/api/files/${file.id}/download`,\n              previewUrl: `/api/files/${file.id}/preview`,\n              size: file.fileSize || file.size,\n              type: file.fileType || file.contentType || file.type,\n              uploadTime: file.uploadTime || file.createTime\n            }))\n          } else {\n            console.log('⚠️ 未找到文件，尝试解析attachments字段')\n            // 回退到解析attachments字段\n            const parsedAttachments = parseAttachments(submission.attachments)\n            console.log('📋 解析的附件信息:', parsedAttachments)\n\n            // 查找需要查找ID的附件\n            const attachmentsWithIds = await Promise.all(\n              parsedAttachments.map(async (attachment) => {\n                if (attachment.needsLookup) {\n                  try {\n                    console.log(`🔍 查找文件: ${attachment.name}`)\n                    // 根据记录ID查找关联的文件\n                    const files = await fileAPI.getFilesByRecord(submission.id)\n                    console.log('🔍 查找结果:', files)\n\n                    const fileList = files?.data || files || []\n                    console.log('🔍 文件列表详情:', fileList)\n                    console.log('🔍 文件列表长度:', fileList.length)\n\n                    if (fileList.length > 0) {\n                      console.log('🔍 可用文件名列表:')\n                      fileList.forEach((file, index) => {\n                        console.log(`  ${index}: originalName=\"${file.originalName}\", fileName=\"${file.fileName}\", name=\"${file.name}\"`)\n                      })\n                    }\n\n                    // 尝试多种匹配方式\n                    let matchedFile = fileList.find(file =>\n                      file.originalName === attachment.name ||\n                      file.fileName === attachment.name ||\n                      file.name === attachment.name\n                    )\n\n                    // 如果精确匹配失败，尝试模糊匹配\n                    if (!matchedFile && fileList.length > 0) {\n                      console.log('🔍 精确匹配失败，尝试模糊匹配...')\n                      matchedFile = fileList.find(file => {\n                        const fileName = file.originalName || file.fileName || file.name || ''\n                        const attachmentName = attachment.name || ''\n                        return fileName.toLowerCase().includes(attachmentName.toLowerCase()) ||\n                               attachmentName.toLowerCase().includes(fileName.toLowerCase())\n                      })\n\n                      if (matchedFile) {\n                        console.log('✅ 模糊匹配成功:', matchedFile)\n                      }\n                    }\n\n                    // 如果还是没找到，尝试使用第一个文件（如果只有一个文件的话）\n                    if (!matchedFile && fileList.length === 1) {\n                      console.log('🔍 使用唯一文件作为匹配结果')\n                      matchedFile = fileList[0]\n                    }\n\n                    if (matchedFile) {\n                      console.log('✅ 找到匹配文件:', matchedFile)\n                      return {\n                        id: matchedFile.id,\n                        name: matchedFile.originalName || matchedFile.fileName || matchedFile.name,\n                        url: `/api/files/${matchedFile.id}/download`,\n                        previewUrl: `/api/files/${matchedFile.id}/preview`,\n                        size: matchedFile.fileSize || matchedFile.size,\n                        type: matchedFile.fileType || matchedFile.contentType || matchedFile.type,\n                        uploadTime: matchedFile.uploadTime || matchedFile.createTime\n                      }\n                    } else {\n                      console.log('❌ 未找到匹配文件:', attachment.name)\n                      console.log('❌ 目标文件名:', attachment.name)\n                      console.log('❌ 可用文件:', fileList.map(f => f.originalName || f.fileName || f.name))\n                    }\n                  } catch (error) {\n                    console.error('❌ 查找文件ID失败:', error)\n                    console.error('❌ 错误详情:', {\n                      message: error.message,\n                      status: error.response?.status,\n                      data: error.response?.data\n                    })\n                  }\n                }\n                return attachment\n              })\n            )\n\n            selectedAttachments.value = attachmentsWithIds\n          }\n        } catch (error) {\n          console.error('❌ 获取记录文件失败:', error)\n          ElMessage.error('获取附件信息失败: ' + (error.message || '未知错误'))\n          selectedAttachments.value = []\n        }\n      }\n\n      console.log('📎 最终附件列表:', selectedAttachments.value)\n      showAttachmentDialog.value = true\n    }\n\n    const downloadAttachment = async (attachment) => {\n      console.log('📥 开始下载附件:', attachment)\n\n      try {\n        if (attachment.id) {\n          console.log(`📡 使用文件ID下载: ${attachment.id}`)\n          // 使用文件API下载\n          const response = await fileAPI.downloadFile(attachment.id)\n\n          console.log('📡 下载响应:', response)\n          console.log('📡 响应类型:', typeof response)\n          console.log('📡 是否为Blob:', response instanceof Blob)\n\n          // 处理响应数据\n          let blob\n          if (response.data && response.data instanceof Blob) {\n            // 如果response.data是Blob\n            blob = response.data\n          } else if (response instanceof Blob) {\n            // 如果response本身是Blob\n            blob = response\n          } else {\n            // 如果都不是，尝试创建Blob\n            console.error('❌ 响应不是Blob格式:', response)\n            ElMessage.error('文件下载失败：响应格式错误')\n            return\n          }\n\n          // 创建下载链接\n          const url = window.URL.createObjectURL(blob)\n          const link = document.createElement('a')\n          link.href = url\n          link.download = attachment.name\n          document.body.appendChild(link)\n          link.click()\n          document.body.removeChild(link)\n          window.URL.revokeObjectURL(url)\n\n          console.log('✅ 文件下载成功')\n          ElMessage.success('文件下载成功')\n        } else if (attachment.url) {\n          console.log(`🔗 使用URL下载: ${attachment.url}`)\n          // 如果有URL，直接下载\n          const link = document.createElement('a')\n          link.href = attachment.url\n          link.download = attachment.name\n          link.target = '_blank'  // 在新窗口打开，避免跨域问题\n          document.body.appendChild(link)\n          link.click()\n          document.body.removeChild(link)\n\n          console.log('✅ URL下载触发成功')\n          ElMessage.success('文件下载已开始')\n        } else {\n          console.log('❌ 附件缺少ID和URL信息:', attachment)\n          // 如果没有ID和URL，尝试通过文件名查找\n          ElMessage.warning(`附件\"${attachment.name}\"暂无下载链接。请尝试刷新页面或联系管理员。`)\n\n          // 提供调试信息\n          console.log('🔍 调试信息:')\n          console.log('- 附件对象:', attachment)\n          console.log('- 是否有ID:', !!attachment.id)\n          console.log('- 是否有URL:', !!attachment.url)\n          console.log('- 是否需要查找:', attachment.needsLookup)\n        }\n      } catch (error) {\n        console.error('❌ 下载附件失败:', error)\n        console.error('❌ 错误详情:', {\n          message: error.message,\n          status: error.response?.status,\n          data: error.response?.data\n        })\n\n        // 根据错误类型提供不同的提示\n        if (error.response?.status === 404) {\n          ElMessage.error('文件不存在或已被删除')\n        } else if (error.response?.status === 403) {\n          ElMessage.error('没有权限下载此文件')\n        } else if (error.response?.status === 401) {\n          ElMessage.error('请先登录')\n        } else {\n          ElMessage.error('下载附件失败: ' + (error.message || '未知错误'))\n        }\n      }\n    }\n\n    const downloadAllAttachments = async () => {\n      for (const attachment of selectedAttachments.value) {\n        await downloadAttachment(attachment)\n        // 添加延迟避免浏览器阻止多个下载\n        await new Promise(resolve => setTimeout(resolve, 500))\n      }\n    }\n\n    const isImageFile = (filename) => {\n      if (!filename) return false\n      const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg']\n      const ext = filename.toLowerCase().substring(filename.lastIndexOf('.'))\n      return imageExtensions.includes(ext)\n    }\n\n    const previewAttachment = async (attachment) => {\n      try {\n        if (attachment.id) {\n          // 使用文件API预览\n          const response = await fileAPI.previewFile(attachment.id)\n          const blob = new Blob([response])\n          previewImageUrl.value = window.URL.createObjectURL(blob)\n          currentPreviewAttachment.value = attachment\n          showImagePreview.value = true\n        } else if (attachment.previewUrl || attachment.url) {\n          previewImageUrl.value = attachment.previewUrl || attachment.url\n          currentPreviewAttachment.value = attachment\n          showImagePreview.value = true\n        } else {\n          ElMessage.warning('该图片暂无预览链接')\n        }\n      } catch (error) {\n        console.error('预览图片失败:', error)\n        ElMessage.error('预览图片失败')\n      }\n    }\n\n    const formatFileSize = (bytes) => {\n      if (!bytes) return '-'\n      const sizes = ['B', 'KB', 'MB', 'GB']\n      const i = Math.floor(Math.log(bytes) / Math.log(1024))\n      return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i]\n    }\n\n    // 生命周期\n    onMounted(() => {\n      loadMyProjects()\n      loadSubmissions()\n    })\n\n    return {\n      // 数据\n      loading,\n      submissions,\n      myProjects,\n      total,\n      currentPage,\n      pageSize,\n      filterStatus,\n      filterProject,\n      showDetailDialog,\n      selectedSubmission,\n      showAttachmentDialog,\n      selectedAttachments,\n      showImagePreview,\n      previewImageUrl,\n      currentPreviewAttachment,\n      currentUser,\n\n      // 方法\n      loadSubmissions,\n      viewSubmission,\n      approveSubmission,\n      rejectSubmission,\n      getStatusColor,\n      getStatusText,\n      getPriorityColor,\n      getPriorityText,\n      formatDate,\n\n      // 附件方法\n      hasAttachments,\n      getAttachmentCount,\n      parseAttachments,\n      viewAttachments,\n      downloadAttachment,\n      downloadAllAttachments,\n      isImageFile,\n      previewAttachment,\n      formatFileSize\n    }\n  }\n}\n</script>\n\n<style scoped>\n.task-review-container {\n  padding: 20px;\n}\n\n.page-card {\n  min-height: calc(100vh - 120px);\n}\n\n.card-header {\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n}\n\n.card-header h2 {\n  margin: 0;\n  color: #303133;\n  font-size: 24px;\n  font-weight: 600;\n}\n\n.subtitle {\n  margin: 0;\n  color: #909399;\n  font-size: 14px;\n}\n\n.filter-section {\n  margin-bottom: 20px;\n  padding: 20px;\n  background: #f8f9fa;\n  border-radius: 8px;\n}\n\n.submissions-list {\n  margin-bottom: 20px;\n}\n\n.submission-items {\n  display: flex;\n  flex-direction: column;\n  gap: 16px;\n}\n\n.submission-card {\n  transition: all 0.3s ease;\n}\n\n.submission-card:hover {\n  transform: translateY(-2px);\n}\n\n.submission-header {\n  display: flex;\n  justify-content: between;\n  align-items: flex-start;\n  margin-bottom: 12px;\n}\n\n.submission-info {\n  flex: 1;\n}\n\n.task-title {\n  margin: 0 0 8px 0;\n  color: #303133;\n  font-size: 16px;\n  font-weight: 600;\n}\n\n.submission-meta {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  font-size: 12px;\n  color: #909399;\n}\n\n.submission-actions {\n  display: flex;\n  gap: 8px;\n}\n\n.submission-content {\n  margin-bottom: 12px;\n}\n\n.content-preview {\n  margin: 0 0 8px 0;\n  color: #606266;\n  font-size: 14px;\n  line-height: 1.5;\n  display: -webkit-box;\n  -webkit-line-clamp: 2;\n  -webkit-box-orient: vertical;\n  overflow: hidden;\n}\n\n.attachments {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  color: #409eff;\n  font-size: 12px;\n}\n\n.view-attachments-btn {\n  padding: 0 4px;\n  font-size: 12px;\n  color: #409eff;\n}\n\n.view-attachments-btn:hover {\n  color: #66b1ff;\n}\n\n.parent-task {\n  padding: 12px;\n  background: #f8f9fa;\n  border-radius: 6px;\n  border-left: 3px solid #409eff;\n}\n\n.parent-task-header {\n  display: flex;\n  align-items: center;\n  gap: 6px;\n  margin-bottom: 8px;\n  font-weight: 600;\n  color: #303133;\n  font-size: 14px;\n}\n\n.parent-task-content {\n  margin: 0;\n  color: #606266;\n  font-size: 13px;\n  line-height: 1.4;\n  display: -webkit-box;\n  -webkit-line-clamp: 2;\n  -webkit-box-orient: vertical;\n  overflow: hidden;\n}\n\n.pagination {\n  display: flex;\n  justify-content: center;\n  margin-top: 20px;\n}\n\n.submission-detail {\n  max-height: 60vh;\n  overflow-y: auto;\n}\n\n.submission-content-detail {\n  max-height: 200px;\n  overflow-y: auto;\n  padding: 8px;\n  background: #f8f9fa;\n  border-radius: 4px;\n  white-space: pre-wrap;\n  line-height: 1.5;\n}\n\n.original-task-section {\n  margin-top: 20px;\n  padding-top: 20px;\n  border-top: 1px solid #ebeef5;\n}\n\n.original-task-section h4 {\n  margin: 0 0 12px 0;\n  color: #303133;\n  font-size: 16px;\n}\n\n.dialog-footer {\n  display: flex;\n  justify-content: flex-end;\n  gap: 12px;\n}\n\n.loading-state,\n.empty-state {\n  padding: 40px 20px;\n  text-align: center;\n}\n\n/* 附件相关样式 */\n.attachment-list {\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n}\n\n.attachment-item {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  padding: 8px 12px;\n  background: #f8f9fa;\n  border-radius: 6px;\n  border: 1px solid #e4e7ed;\n}\n\n.attachment-name {\n  flex: 1;\n  font-size: 14px;\n  color: #303133;\n}\n\n.download-btn {\n  padding: 0 8px;\n  font-size: 12px;\n}\n\n.attachment-dialog {\n  max-height: 400px;\n  overflow-y: auto;\n}\n\n.attachment-dialog-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 16px;\n  border: 1px solid #e4e7ed;\n  border-radius: 8px;\n  margin-bottom: 12px;\n  transition: all 0.3s ease;\n}\n\n.attachment-dialog-item:hover {\n  border-color: #409eff;\n  background: #f0f9ff;\n}\n\n.attachment-info {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  flex: 1;\n}\n\n.attachment-details {\n  flex: 1;\n}\n\n.attachment-name {\n  font-size: 14px;\n  font-weight: 500;\n  color: #303133;\n  margin-bottom: 4px;\n}\n\n.attachment-meta {\n  display: flex;\n  gap: 12px;\n  font-size: 12px;\n  color: #909399;\n}\n\n.file-size,\n.file-type {\n  padding: 2px 6px;\n  background: #f0f2f5;\n  border-radius: 4px;\n}\n\n.attachment-actions {\n  display: flex;\n  gap: 8px;\n}\n\n.no-attachments {\n  padding: 40px 20px;\n  text-align: center;\n}\n\n.image-preview {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  min-height: 300px;\n}\n\n.preview-image {\n  max-width: 100%;\n  max-height: 70vh;\n  object-fit: contain;\n  border-radius: 8px;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n}\n</style>\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAuB;;EAUzBA,KAAK,EAAC;AAAgB;;EA+BtBA,KAAK,EAAC;AAAkB;;;EACPA,KAAK,EAAC;;;;EAIgBA,KAAK,EAAC;;;;EAMpCA,KAAK,EAAC;;;EAGPA,KAAK,EAAC;AAAmB;;EACvBA,KAAK,EAAC;AAAiB;;EACtBA,KAAK,EAAC;AAAY;;EACjBA,KAAK,EAAC;AAAiB;;EAIpBA,KAAK,EAAC;AAAW;;EACjBA,KAAK,EAAC;AAAa;;EAGxBA,KAAK,EAAC;AAAoB;;EAqB5BA,KAAK,EAAC;AAAoB;;EAC1BA,KAAK,EAAC;AAAiB;;;EACaA,KAAK,EAAC;;;;EAebA,KAAK,EAAC;;;EACjCA,KAAK,EAAC;AAAoB;;EAI5BA,KAAK,EAAC;AAAqB;;;EAQlBA,KAAK,EAAC;;;;EAeGA,KAAK,EAAC;;;EAoB1BA,KAAK,EAAC;AAA2B;;EAKjCA,KAAK,EAAC;AAAiB;;EAOlBA,KAAK,EAAC;AAAiB;;;EAeKA,KAAK,EAAC;;;EAsB3CA,KAAK,EAAC;AAAe;;;EAsBeA,KAAK,EAAC;;;EAMxCA,KAAK,EAAC;AAAiB;;EAErBA,KAAK,EAAC;AAAoB;;EACxBA,KAAK,EAAC;AAAiB;;EACvBA,KAAK,EAAC;AAAiB;;;EACGA,KAAK,EAAC;;;;EACNA,KAAK,EAAC;;;EAIpCA,KAAK,EAAC;AAAoB;;;EAqBvBA,KAAK,EAAC;;;EAkBbA,KAAK,EAAC;AAAe;;;;;;;;;;;;;;;;;;;;;;uBAtR9BC,mBAAA,CAwSM,OAxSNC,UAwSM,GAvSJC,YAAA,CA+HUC,kBAAA;IA/HDJ,KAAK,EAAC;EAAW;IACbK,MAAM,EAAAC,QAAA,CACf,MAGMC,MAAA,SAAAA,MAAA,QAHNC,mBAAA,CAGM;MAHDR,KAAK,EAAC;IAAa,IACtBQ,mBAAA,CAAa,YAAT,MAAI,GACRA,mBAAA,CAAiC;MAA9BR,KAAK,EAAC;IAAU,GAAC,WAAS,E;sBAKjC,MA4BM,CA5BNQ,mBAAA,CA4BM,OA5BNC,UA4BM,GA3BJN,YAAA,CA0BSO,iBAAA;MA1BAC,MAAM,EAAE;IAAE;wBACjB,MAOS,CAPTR,YAAA,CAOSS,iBAAA;QAPAC,IAAI,EAAE;MAAC;0BACd,MAKY,CALZV,YAAA,CAKYW,oBAAA;sBALQC,MAAA,CAAAC,YAAY;qEAAZD,MAAA,CAAAC,YAAY,GAAAC,MAAA;UAAEC,WAAW,EAAC,MAAM;UAAEC,QAAM,EAAEJ,MAAA,CAAAK;;4BAC5D,MAAiC,CAAjCjB,YAAA,CAAiCkB,oBAAA;YAAtBC,KAAK,EAAC,IAAI;YAACC,KAAK,EAAC;cAC5BpB,YAAA,CAA2CkB,oBAAA;YAAhCC,KAAK,EAAC,KAAK;YAACC,KAAK,EAAC;cAC7BpB,YAAA,CAA2CkB,oBAAA;YAAhCC,KAAK,EAAC,KAAK;YAACC,KAAK,EAAC;cAC7BpB,YAAA,CAA0CkB,oBAAA;YAA/BC,KAAK,EAAC,KAAK;YAACC,KAAK,EAAC;;;;;UAGjCpB,YAAA,CAUSS,iBAAA;QAVAC,IAAI,EAAE;MAAC;0BACd,MAQY,CARZV,YAAA,CAQYW,oBAAA;sBARQC,MAAA,CAAAS,aAAa;qEAAbT,MAAA,CAAAS,aAAa,GAAAP,MAAA;UAAEC,WAAW,EAAC,MAAM;UAAEC,QAAM,EAAEJ,MAAA,CAAAK;;4BAC7D,MAAmC,CAAnCjB,YAAA,CAAmCkB,oBAAA;YAAxBC,KAAK,EAAC,MAAM;YAACC,KAAK,EAAC;iCAC9BtB,mBAAA,CAKEwB,SAAA,QAAAC,WAAA,CAJkBX,MAAA,CAAAY,UAAU,EAArBC,OAAO;iCADhBC,YAAA,CAKER,oBAAA;cAHCS,GAAG,EAAEF,OAAO,CAACG,EAAE;cACfT,KAAK,EAAEM,OAAO,CAACI,IAAI,IAAIJ,OAAO,CAACK,KAAK;cACpCV,KAAK,EAAEK,OAAO,CAACG;;;;;;UAItB5B,YAAA,CAKSS,iBAAA;QALAC,IAAI,EAAE;MAAC;0BACd,MAGY,CAHZV,YAAA,CAGY+B,oBAAA;UAHDC,IAAI,EAAC,SAAS;UAAEC,OAAK,EAAErB,MAAA,CAAAK,eAAe;UAAGiB,OAAO,EAAEtB,MAAA,CAAAsB;;4BAC3D,MAA8B,CAA9BlC,YAAA,CAA8BmC,kBAAA;8BAArB,MAAW,CAAXnC,YAAA,CAAWoC,kBAAA,E;;2DAAU,MAEhC,G;;;;;;;UAMN/B,mBAAA,CAyEM,OAzENgC,UAyEM,GAxEOzB,MAAA,CAAAsB,OAAO,I,cAAlBpC,mBAAA,CAEM,OAFNwC,UAEM,GADJtC,YAAA,CAAkCuC,sBAAA;MAApBC,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAR;YAGT7B,MAAA,CAAA8B,WAAW,CAACC,MAAM,U,cAAlC7C,mBAAA,CAIM,OAJN8C,UAIM,GAHJ5C,YAAA,CAEW6C,mBAAA;MAFDC,WAAW,EAAC;IAAQ;wBAC5B,MAAmE,CAAnE9C,YAAA,CAAmE+B,oBAAA;QAAxDC,IAAI,EAAC,SAAS;QAAEC,OAAK,EAAErB,MAAA,CAAAK;;0BAAiB,MAAIb,MAAA,SAAAA,MAAA,Q,iBAAJ,MAAI,E;;;;;2BAI3DN,mBAAA,CA6DM,OA7DNiD,UA6DM,I,kBA5DJjD,mBAAA,CA2DMwB,SAAA,QAAAC,WAAA,CA3DoBX,MAAA,CAAA8B,WAAW,EAAzBM,UAAU;2BAAtBlD,mBAAA,CA2DM;QA3DkC6B,GAAG,EAAEqB,UAAU,CAACpB,EAAE;QAAE/B,KAAK,EAAC;UAChEG,YAAA,CAyDUC,kBAAA;QAzDDJ,KAAK,EAAC,iBAAiB;QAACoD,MAAM,EAAC;;0BACtC,MA8BM,CA9BN5C,mBAAA,CA8BM,OA9BN6C,UA8BM,GA7BJ7C,mBAAA,CASM,OATN8C,UASM,GARJ9C,mBAAA,CAAkD,MAAlD+C,UAAkD,EAAAC,gBAAA,CAAxBL,UAAU,CAAClB,KAAK,kBAC1CzB,mBAAA,CAMM,OANNiD,WAMM,GALJtD,YAAA,CAESuD,iBAAA;UAFAvB,IAAI,EAAEpB,MAAA,CAAA4C,cAAc,CAACR,UAAU,CAACS,MAAM;UAAGC,IAAI,EAAC;;4BACrD,MAAsC,C,kCAAnC9C,MAAA,CAAA+C,aAAa,CAACX,UAAU,CAACS,MAAM,kB;;uDAEpCpD,mBAAA,CAAqE,QAArEuD,WAAqE,EAA7C,MAAI,GAAAP,gBAAA,CAAGL,UAAU,CAACa,OAAO,EAAEC,QAAQ,kBAC3DzD,mBAAA,CAA6E,QAA7E0D,WAA6E,EAAnD,OAAK,GAAAV,gBAAA,CAAGzC,MAAA,CAAAoD,UAAU,CAAChB,UAAU,CAACiB,UAAU,kB,KAGtE5D,mBAAA,CAkBM,OAlBN6D,WAkBM,GAjBJlE,YAAA,CAA4E+B,oBAAA;UAAjE2B,IAAI,EAAC,OAAO;UAAEzB,OAAK,EAAAnB,MAAA,IAAEF,MAAA,CAAAuD,cAAc,CAACnB,UAAU;;4BAAG,MAAI,KAAA5C,MAAA,SAAAA,MAAA,Q,iBAAJ,MAAI,E;;;0DAExD4C,UAAU,CAACS,MAAM,oB,cADzB/B,YAAA,CAOYK,oBAAA;;UALVC,IAAI,EAAC,SAAS;UACd0B,IAAI,EAAC,OAAO;UACXzB,OAAK,EAAAnB,MAAA,IAAEF,MAAA,CAAAwD,iBAAiB,CAACpB,UAAU;;4BACrC,MAED,KAAA5C,MAAA,SAAAA,MAAA,Q,iBAFC,MAED,E;;;+FAEQ4C,UAAU,CAACS,MAAM,oB,cADzB/B,YAAA,CAOYK,oBAAA;;UALVC,IAAI,EAAC,QAAQ;UACb0B,IAAI,EAAC,OAAO;UACXzB,OAAK,EAAAnB,MAAA,IAAEF,MAAA,CAAAyD,gBAAgB,CAACrB,UAAU;;4BACpC,MAED,KAAA5C,MAAA,SAAAA,MAAA,Q,iBAFC,MAED,E;;;mGAIJC,mBAAA,CAcM,OAdNiE,WAcM,GAbJjE,mBAAA,CAAuD,KAAvDkE,WAAuD,EAAAlB,gBAAA,CAAzBL,UAAU,CAACwB,OAAO,kBACrC5D,MAAA,CAAA6D,cAAc,CAACzB,UAAU,K,cAApClD,mBAAA,CAWM,OAXN4E,WAWM,GAVJ1E,YAAA,CAAgCmC,kBAAA;4BAAvB,MAAa,CAAbnC,YAAA,CAAa2E,oBAAA,E;;YACtBtE,mBAAA,CAAoD,cAAAgD,gBAAA,CAA3CzC,MAAA,CAAAgE,kBAAkB,CAAC5B,UAAU,KAAI,KAAG,iBAC7ChD,YAAA,CAOY+B,oBAAA;UANVC,IAAI,EAAC,MAAM;UACX0B,IAAI,EAAC,OAAO;UACXzB,OAAK,EAAAnB,MAAA,IAAEF,MAAA,CAAAiE,eAAe,CAAC7B,UAAU;UAClCnD,KAAK,EAAC;;4BACP,MAED,KAAAO,MAAA,SAAAA,MAAA,Q,iBAFC,QAED,E;;;mGAIJ0E,mBAAA,WAAc,EACH9B,UAAU,CAAC+B,UAAU,I,cAAhCjF,mBAAA,CAMM,OANNkF,WAMM,GALJ3E,mBAAA,CAGM,OAHN4E,WAGM,GAFJjF,YAAA,CAA+BmC,kBAAA;4BAAtB,MAAY,CAAZnC,YAAA,CAAYkF,mBAAA,E;;YACrB7E,mBAAA,CAAkD,cAA5C,MAAI,GAAAgD,gBAAA,CAAGL,UAAU,CAAC+B,UAAU,CAACjD,KAAK,iB,GAE1CzB,mBAAA,CAAsE,KAAtE8E,WAAsE,EAAA9B,gBAAA,CAApCL,UAAU,CAAC+B,UAAU,CAACP,OAAO,iB;;;yCAQ9D5D,MAAA,CAAAwE,KAAK,Q,cAAhBtF,mBAAA,CAUM,OAVNuF,WAUM,GATJrF,YAAA,CAQEsF,wBAAA;MAPQ,cAAY,EAAE1E,MAAA,CAAA2E,WAAW;kEAAX3E,MAAA,CAAA2E,WAAW,GAAAzE,MAAA;MACzB,WAAS,EAAEF,MAAA,CAAA4E,QAAQ;+DAAR5E,MAAA,CAAA4E,QAAQ,GAAA1E,MAAA;MAC1BsE,KAAK,EAAExE,MAAA,CAAAwE,KAAK;MACZ,YAAU,EAAE,YAAY;MACzBK,MAAM,EAAC,yCAAyC;MAC/CC,YAAW,EAAE9E,MAAA,CAAAK,eAAe;MAC5B0E,eAAc,EAAE/E,MAAA,CAAAK;;;MAKvB6D,mBAAA,aAAgB,EAChB9E,YAAA,CAwFY4F,oBAAA;gBAxFQhF,MAAA,CAAAiF,gBAAgB;+DAAhBjF,MAAA,CAAAiF,gBAAgB,GAAA/E,MAAA;IAAEgB,KAAK,EAAC,MAAM;IAACgE,KAAK,EAAC;;IAqE5CC,MAAM,EAAA5F,QAAA,CACf,MAgBM,CAhBNE,mBAAA,CAgBM,OAhBN2F,WAgBM,GAfJhG,YAAA,CAA2D+B,oBAAA;MAA/CE,OAAK,EAAA7B,MAAA,QAAAA,MAAA,MAAAU,MAAA,IAAEF,MAAA,CAAAiF,gBAAgB;;wBAAU,MAAEzF,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;QAEvCQ,MAAA,CAAAqF,kBAAkB,EAAExC,MAAM,oB,cADlC/B,YAAA,CAMYK,oBAAA;;MAJVC,IAAI,EAAC,SAAS;MACbC,OAAK,EAAA7B,MAAA,QAAAA,MAAA,MAAAU,MAAA,IAAEF,MAAA,CAAAwD,iBAAiB,CAACxD,MAAA,CAAAqF,kBAAkB;;wBAC7C,MAED7F,MAAA,SAAAA,MAAA,Q,iBAFC,QAED,E;;;6CAEQQ,MAAA,CAAAqF,kBAAkB,EAAExC,MAAM,oB,cADlC/B,YAAA,CAMYK,oBAAA;;MAJVC,IAAI,EAAC,QAAQ;MACZC,OAAK,EAAA7B,MAAA,QAAAA,MAAA,MAAAU,MAAA,IAAEF,MAAA,CAAAyD,gBAAgB,CAACzD,MAAA,CAAAqF,kBAAkB;;wBAC5C,MAED7F,MAAA,SAAAA,MAAA,Q,iBAFC,QAED,E;;;;sBApFJ,MAkEM,CAlEKQ,MAAA,CAAAqF,kBAAkB,I,cAA7BnG,mBAAA,CAkEM,OAlENoG,WAkEM,GAjEJlG,YAAA,CA2CkBmG,0BAAA;MA3CAC,MAAM,EAAE,CAAC;MAAEC,MAAM,EAAN;;wBAC3B,MAEuB,CAFvBrG,YAAA,CAEuBsG,+BAAA;QAFDnF,KAAK,EAAC,MAAM;QAAET,IAAI,EAAE;;0BACxC,MAAoD,C,kCAAjDE,MAAA,CAAAqF,kBAAkB,CAAClB,UAAU,EAAEjD,KAAK,2B;;UAEzC9B,YAAA,CAEuBsG,+BAAA;QAFDnF,KAAK,EAAC,MAAM;QAAET,IAAI,EAAE;;0BACxC,MAA8B,C,kCAA3BE,MAAA,CAAAqF,kBAAkB,CAACnE,KAAK,iB;;UAE7B9B,YAAA,CAIuBsG,+BAAA;QAJDnF,KAAK,EAAC;MAAM;0BAChC,MAES,CAFTnB,YAAA,CAESuD,iBAAA;UAFAvB,IAAI,EAAEpB,MAAA,CAAA4C,cAAc,CAAC5C,MAAA,CAAAqF,kBAAkB,CAACxC,MAAM;;4BACrD,MAA8C,C,kCAA3C7C,MAAA,CAAA+C,aAAa,CAAC/C,MAAA,CAAAqF,kBAAkB,CAACxC,MAAM,kB;;;;UAG9CzD,YAAA,CAEuBsG,+BAAA;QAFDnF,KAAK,EAAC;MAAK;0BAC/B,MAA0C,C,kCAAvCP,MAAA,CAAAqF,kBAAkB,CAACpC,OAAO,EAAEC,QAAQ,iB;;UAEzC9D,YAAA,CAEuBsG,+BAAA;QAFDnF,KAAK,EAAC,MAAM;QAAET,IAAI,EAAE;;0BACxC,MAA+C,C,kCAA5CE,MAAA,CAAAoD,UAAU,CAACpD,MAAA,CAAAqF,kBAAkB,CAAChC,UAAU,kB;;UAE7CjE,YAAA,CAIuBsG,+BAAA;QAJDnF,KAAK,EAAC,MAAM;QAAET,IAAI,EAAE;;0BACxC,MAEM,CAFNL,mBAAA,CAEM,OAFNkG,WAEM,EAAAlD,gBAAA,CADDzC,MAAA,CAAAqF,kBAAkB,CAACzB,OAAO,iB;;UAGL5D,MAAA,CAAA6D,cAAc,CAAC7D,MAAA,CAAAqF,kBAAkB,K,cAA7DvE,YAAA,CAmBuB4E,+BAAA;;QAnByCnF,KAAK,EAAC,MAAM;QAAET,IAAI,EAAE;;0BAClF,MAiBM,CAjBNL,mBAAA,CAiBM,OAjBNmG,WAiBM,I,kBAhBJ1G,mBAAA,CAeMwB,SAAA,QAAAC,WAAA,CAd0BX,MAAA,CAAA6F,gBAAgB,CAAC7F,MAAA,CAAAqF,kBAAkB,CAACS,WAAW,IAArEC,UAAU,EAAEC,KAAK;+BAD3B9G,mBAAA,CAeM;YAbH6B,GAAG,EAAEiF,KAAK;YACX/G,KAAK,EAAC;cAENG,YAAA,CAA+BmC,kBAAA;8BAAtB,MAAY,CAAZnC,YAAA,CAAYkF,mBAAA,E;;cACrB7E,mBAAA,CAA0D,QAA1DwG,WAA0D,EAAAxD,gBAAA,CAAzBsD,UAAU,CAAC9E,IAAI,kBAChD7B,YAAA,CAOY+B,oBAAA;YANVC,IAAI,EAAC,MAAM;YACX0B,IAAI,EAAC,OAAO;YACXzB,OAAK,EAAAnB,MAAA,IAAEF,MAAA,CAAAkG,kBAAkB,CAACH,UAAU;YACrC9G,KAAK,EAAC;;8BACP,MAED,KAAAO,MAAA,SAAAA,MAAA,Q,iBAFC,MAED,E;;;;;;;;QAMR0E,mBAAA,WAAc,EACHlE,MAAA,CAAAqF,kBAAkB,CAAClB,UAAU,I,cAAxCjF,mBAAA,CAkBM,OAlBNiH,WAkBM,G,4BAjBJ1G,mBAAA,CAAc,YAAV,OAAK,qBACTL,YAAA,CAekBmG,0BAAA;MAfAC,MAAM,EAAE,CAAC;MAAEC,MAAM,EAAN;;wBAC3B,MAEuB,CAFvBrG,YAAA,CAEuBsG,+BAAA;QAFDnF,KAAK,EAAC,MAAM;QAAET,IAAI,EAAE;;0BACxC,MAAyC,C,kCAAtCE,MAAA,CAAAqF,kBAAkB,CAAClB,UAAU,CAACjD,KAAK,iB;;UAExC9B,YAAA,CAEuBsG,+BAAA;QAFDnF,KAAK,EAAC,MAAM;QAAET,IAAI,EAAE;;0BACxC,MAA2C,C,kCAAxCE,MAAA,CAAAqF,kBAAkB,CAAClB,UAAU,CAACP,OAAO,iB;;UAE1CxE,YAAA,CAIuBsG,+BAAA;QAJDnF,KAAK,EAAC;MAAK;0BAC/B,MAES,CAFTnB,YAAA,CAESuD,iBAAA;UAFAvB,IAAI,EAAEpB,MAAA,CAAAoG,gBAAgB,CAACpG,MAAA,CAAAqF,kBAAkB,CAAClB,UAAU,CAACkC,QAAQ;UAAGvD,IAAI,EAAC;;4BAC5E,MAA6D,C,kCAA1D9C,MAAA,CAAAsG,eAAe,CAACtG,MAAA,CAAAqF,kBAAkB,CAAClB,UAAU,CAACkC,QAAQ,kB;;;;UAG7DjH,YAAA,CAEuBsG,+BAAA;QAFDnF,KAAK,EAAC;MAAM;0BAChC,MAAuD,C,kCAApDP,MAAA,CAAAoD,UAAU,CAACpD,MAAA,CAAAqF,kBAAkB,CAAClB,UAAU,CAACoC,OAAO,kB;;;;;;qCA2B7DrC,mBAAA,aAAgB,EAChB9E,YAAA,CAoDY4F,oBAAA;gBApDQhF,MAAA,CAAAwG,oBAAoB;+DAApBxG,MAAA,CAAAwG,oBAAoB,GAAAtG,MAAA;IAAEgB,KAAK,EAAC,MAAM;IAACgE,KAAK,EAAC;;IA0ChDC,MAAM,EAAA5F,QAAA,CACf,MAA+D,CAA/DH,YAAA,CAA+D+B,oBAAA;MAAnDE,OAAK,EAAA7B,MAAA,QAAAA,MAAA,MAAAU,MAAA,IAAEF,MAAA,CAAAwG,oBAAoB;;wBAAU,MAAEhH,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;QAE3CQ,MAAA,CAAAyG,mBAAmB,CAAC1E,MAAM,Q,cADlCjB,YAAA,CAMYK,oBAAA;;MAJVC,IAAI,EAAC,SAAS;MACbC,OAAK,EAAErB,MAAA,CAAA0G;;wBACT,MAEDlH,MAAA,SAAAA,MAAA,Q,iBAFC,QAED,E;;;;sBAjDF,MAoCM,CApCKQ,MAAA,CAAAyG,mBAAmB,CAAC1E,MAAM,Q,cAArC7C,mBAAA,CAoCM,OApCNyH,WAoCM,I,kBAnCJzH,mBAAA,CAkCMwB,SAAA,QAAAC,WAAA,CAjC0BX,MAAA,CAAAyG,mBAAmB,GAAzCV,UAAU,EAAEC,KAAK;2BAD3B9G,mBAAA,CAkCM;QAhCH6B,GAAG,EAAEiF,KAAK;QACX/G,KAAK,EAAC;UAENQ,mBAAA,CASM,OATNmH,WASM,GARJxH,YAAA,CAAyCmC,kBAAA;QAAhCuB,IAAI,EAAC;MAAI;0BAAC,MAAY,CAAZ1D,YAAA,CAAYkF,mBAAA,E;;UAC/B7E,mBAAA,CAMM,OANNoH,WAMM,GALJpH,mBAAA,CAAwD,OAAxDqH,WAAwD,EAAArE,gBAAA,CAAxBsD,UAAU,CAAC9E,IAAI,kBAC/CxB,mBAAA,CAGM,OAHNsH,WAGM,GAFQhB,UAAU,CAACjD,IAAI,I,cAA3B5D,mBAAA,CAA2F,QAA3F8H,WAA2F,EAAAvE,gBAAA,CAAzCzC,MAAA,CAAAiH,cAAc,CAAClB,UAAU,CAACjD,IAAI,qB,mCACpEiD,UAAU,CAAC3E,IAAI,I,cAA3BlC,mBAAA,CAA2E,QAA3EgI,WAA2E,EAAAzE,gBAAA,CAAzBsD,UAAU,CAAC3E,IAAI,oB,yCAIvE3B,mBAAA,CAkBM,OAlBN0H,WAkBM,GAjBJ/H,YAAA,CAOY+B,oBAAA;QANVC,IAAI,EAAC,SAAS;QACd0B,IAAI,EAAC,OAAO;QACXzB,OAAK,EAAAnB,MAAA,IAAEF,MAAA,CAAAkG,kBAAkB,CAACH,UAAU;;0BAErC,MAA+B,CAA/B3G,YAAA,CAA+BmC,kBAAA;4BAAtB,MAAY,CAAZnC,YAAA,CAAYgI,mBAAA,E;;yDAAU,MAEjC,G;;;wDAEQpH,MAAA,CAAAqH,WAAW,CAACtB,UAAU,CAAC9E,IAAI,K,cADnCH,YAAA,CAQYK,oBAAA;;QANVC,IAAI,EAAC,SAAS;QACd0B,IAAI,EAAC,OAAO;QACXzB,OAAK,EAAAnB,MAAA,IAAEF,MAAA,CAAAsH,iBAAiB,CAACvB,UAAU;;0BAEpC,MAA2B,CAA3B3G,YAAA,CAA2BmC,kBAAA;4BAAlB,MAAQ,CAARnC,YAAA,CAAQmI,eAAA,E;;yDAAU,MAE7B,G;;;;uDAINrI,mBAAA,CAEM,OAFNsI,WAEM,GADJpI,YAAA,CAA+B6C,mBAAA;MAArBC,WAAW,EAAC;IAAM,G;;qCAehCgC,mBAAA,aAAgB,EAChB9E,YAAA,CAkBY4F,oBAAA;gBAlBQhF,MAAA,CAAAyH,gBAAgB;iEAAhBzH,MAAA,CAAAyH,gBAAgB,GAAAvH,MAAA;IAAEgB,KAAK,EAAC,MAAM;IAACgE,KAAK,EAAC;;IAS5CC,MAAM,EAAA5F,QAAA,CACf,MAA2D,CAA3DH,YAAA,CAA2D+B,oBAAA;MAA/CE,OAAK,EAAA7B,MAAA,SAAAA,MAAA,OAAAU,MAAA,IAAEF,MAAA,CAAAyH,gBAAgB;;wBAAU,MAAEjI,MAAA,SAAAA,MAAA,Q,iBAAF,IAAE,E;;;QAC/CJ,YAAA,CAKY+B,oBAAA;MAJVC,IAAI,EAAC,SAAS;MACbC,OAAK,EAAA7B,MAAA,SAAAA,MAAA,OAAAU,MAAA,IAAEF,MAAA,CAAAkG,kBAAkB,CAAClG,MAAA,CAAA0H,wBAAwB;;wBACpD,MAEDlI,MAAA,SAAAA,MAAA,Q,iBAFC,QAED,E;;;;sBAfF,MAOM,CAPNC,mBAAA,CAOM,OAPNkI,WAOM,GALI3H,MAAA,CAAA4H,eAAe,I,cADvB1I,mBAAA,CAKE;;MAHC2I,GAAG,EAAE7H,MAAA,CAAA4H,eAAe;MACrBE,GAAG,EAAC,MAAM;MACV7I,KAAK,EAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}