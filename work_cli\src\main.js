import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import store from './store'

// Element Plus 导入 - 暂时使用全量导入以避免构建问题
import ElementPlus, { ElMessage, ElMessageBox, ElNotification, ElLoading } from 'element-plus'
import 'element-plus/dist/index.css'

// 图标导入
import * as ElementPlusIconsVue from '@element-plus/icons-vue'

import request from './utils/request'

// 导入全局组件
import ErrorBoundary from './components/ErrorBoundary.vue'
import OfflineIndicator from './components/OfflineIndicator.vue'

// 导入服务
// import notificationService from './services/notificationService'
// import performanceMonitor from './utils/performance'

const app = createApp(App)

// 注册Element Plus图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

// 注册全局组件
app.component('ErrorBoundary', ErrorBoundary)
app.component('OfflineIndicator', OfflineIndicator)

// 全局错误处理
app.config.errorHandler = (error, instance, info) => {
  // 忽略 ResizeObserver 错误，这是一个常见的浏览器警告，不影响功能
  if (error.message && error.message.includes('ResizeObserver loop completed with undelivered notifications')) {
    return
  }

  console.error('Global error:', error, info)
  ElMessage.error('应用程序出现错误，请刷新页面重试')
}

// 忽略 ResizeObserver 错误
window.addEventListener('error', (event) => {
  if (event.message && event.message.includes('ResizeObserver loop completed with undelivered notifications')) {
    event.preventDefault()
    return false
  }
})

// 全局警告处理
app.config.warnHandler = (msg, instance, trace) => {
  console.warn('Global warning:', msg, trace)
}

// 全局配置
app.config.globalProperties.$http = request
app.config.globalProperties.$message = ElMessage
app.config.globalProperties.$msgbox = ElMessageBox
app.config.globalProperties.$alert = ElMessageBox.alert
app.config.globalProperties.$confirm = ElMessageBox.confirm
app.config.globalProperties.$prompt = ElMessageBox.prompt
app.config.globalProperties.$notify = ElNotification
app.config.globalProperties.$loading = ElLoading.service

// 挂载应用
app.use(store)
   .use(router)
   .use(ElementPlus, {
     // 设置Element Plus的初始z-index
     zIndex: 10000
   })
   .mount('#app')

// 启动性能监控
// performanceMonitor.startMonitoring()

// 初始化通知服务
// router.isReady().then(() => {
//   // 等待路由准备就绪后再初始化通知服务
//   if (store.getters.isAuthenticated) {
//     notificationService.initialize()
//   }

//   // 生成页面加载性能报告
//   // setTimeout(() => {
//   //   performanceMonitor.generateReport()
//   // }, 2000)
// })

// 监听登录状态变化
// store.watch(
//   (state) => state.isAuthenticated,
//   (isAuthenticated) => {
//     if (isAuthenticated) {
//       notificationService.initialize()
//     } else {
//       notificationService.destroy()
//     }
//   }
// )
