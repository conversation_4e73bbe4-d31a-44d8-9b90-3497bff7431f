-- ========================================
-- 完整的数据库重构脚本
-- 将records表分离为tasks表和records表
-- ========================================

-- 开始事务
START TRANSACTION;

-- ========================================
-- 第一步：备份原始数据
-- ========================================
CREATE TABLE records_backup AS SELECT * FROM records WHERE type = 'TASK';

-- ========================================
-- 第二步：创建tasks表
-- ========================================
CREATE TABLE `tasks` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `title` varchar(200) NOT NULL COMMENT '任务标题',
  `description` text COMMENT '任务描述',
  `project_id` bigint(20) DEFAULT NULL COMMENT '关联项目ID',
  `team_id` bigint(20) DEFAULT NULL COMMENT '关联团队ID',
  `creator_id` bigint(20) NOT NULL COMMENT '创建者ID',
  `assignee_id` bigint(20) DEFAULT NULL COMMENT '分配给的用户ID',
  `status` enum('PUBLISHED','IN_PROGRESS','SUBMITTED','COMPLETED','CANCELLED') NOT NULL DEFAULT 'PUBLISHED' COMMENT '任务状态',
  `priority` enum('LOW','MEDIUM','HIGH','URGENT') NOT NULL DEFAULT 'MEDIUM' COMMENT '优先级',
  `due_date` datetime DEFAULT NULL COMMENT '截止时间',
  `start_date` datetime DEFAULT NULL COMMENT '开始时间',
  `completion_date` datetime DEFAULT NULL COMMENT '完成时间',
  `estimated_hours` decimal(5,2) DEFAULT NULL COMMENT '预估工时',
  `actual_hours` decimal(5,2) DEFAULT NULL COMMENT '实际工时',
  `progress` int(11) DEFAULT 0 COMMENT '完成进度(0-100)',
  `attachments` text COMMENT '附件',
  `extra_data` json DEFAULT NULL COMMENT '扩展数据',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_project_id` (`project_id`),
  KEY `idx_team_id` (`team_id`),
  KEY `idx_creator_id` (`creator_id`),
  KEY `idx_assignee_id` (`assignee_id`),
  KEY `idx_status` (`status`),
  KEY `idx_priority` (`priority`),
  KEY `idx_due_date` (`due_date`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='任务表';

-- ========================================
-- 第三步：为关联表添加task_id字段
-- ========================================
ALTER TABLE `records` ADD COLUMN `task_id` bigint(20) DEFAULT NULL COMMENT '关联任务ID';
ALTER TABLE `records` ADD KEY `idx_task_id` (`task_id`);

ALTER TABLE `file_info` ADD COLUMN `task_id` bigint(20) DEFAULT NULL COMMENT '关联任务ID';
ALTER TABLE `file_info` ADD KEY `idx_task_id` (`task_id`);

-- ========================================
-- 第四步：数据迁移
-- ========================================

-- 迁移任务数据到tasks表
INSERT INTO `tasks` (
    `title`,
    `description`,
    `project_id`,
    `team_id`,
    `creator_id`,
    `assignee_id`,
    `status`,
    `priority`,
    `due_date`,
    `start_date`,
    `completion_date`,
    `estimated_hours`,
    `actual_hours`,
    `progress`,
    `attachments`,
    `extra_data`,
    `create_time`,
    `update_time`
)
SELECT 
    r.title,
    r.content as description,
    r.project_id,
    r.team_id,
    r.user_id as creator_id,
    NULL as assignee_id,
    CASE 
        WHEN r.status = 'ACTIVE' THEN 'PUBLISHED'
        WHEN r.status = 'COMPLETED' THEN 'COMPLETED'
        WHEN r.status = 'SUBMITTED' THEN 'SUBMITTED'
        WHEN r.status = 'PUBLISHED' THEN 'PUBLISHED'
        ELSE 'PUBLISHED'
    END as status,
    r.priority,
    r.due_date,
    r.create_time as start_date,
    CASE 
        WHEN r.status = 'COMPLETED' THEN r.update_time
        ELSE NULL
    END as completion_date,
    NULL as estimated_hours,
    NULL as actual_hours,
    CASE 
        WHEN r.status = 'COMPLETED' THEN 100
        WHEN r.status = 'SUBMITTED' THEN 90
        WHEN r.status = 'ACTIVE' OR r.status = 'PUBLISHED' THEN 0
        ELSE 0
    END as progress,
    r.attachments,
    r.extra_data,
    r.create_time,
    r.update_time
FROM records r
WHERE r.type = 'TASK';

-- ========================================
-- 第五步：建立关联关系
-- ========================================

-- 更新SUBMISSION类型记录的task_id（基于项目、团队和时间匹配）
UPDATE records r
JOIN tasks t ON (
    r.project_id = t.project_id 
    AND r.team_id = t.team_id 
    AND r.create_time >= t.create_time
    AND r.create_time <= DATE_ADD(t.due_date, INTERVAL 1 DAY)
)
SET r.task_id = t.id
WHERE r.type = 'SUBMISSION'
AND r.task_id IS NULL;

-- 更新file_info表中关联到任务的文件
UPDATE file_info fi
JOIN records r ON fi.record_id = r.id
JOIN tasks t ON (
    t.project_id = r.project_id 
    AND t.team_id = r.team_id 
    AND t.title = r.title
)
SET fi.task_id = t.id
WHERE r.type = 'TASK';

-- ========================================
-- 第六步：添加外键约束
-- ========================================
ALTER TABLE `tasks` 
ADD CONSTRAINT `fk_tasks_project` FOREIGN KEY (`project_id`) REFERENCES `projects` (`id`) ON DELETE CASCADE,
ADD CONSTRAINT `fk_tasks_team` FOREIGN KEY (`team_id`) REFERENCES `teams` (`id`) ON DELETE CASCADE,
ADD CONSTRAINT `fk_tasks_creator` FOREIGN KEY (`creator_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
ADD CONSTRAINT `fk_tasks_assignee` FOREIGN KEY (`assignee_id`) REFERENCES `users` (`id`) ON DELETE SET NULL;

ALTER TABLE `records` 
ADD CONSTRAINT `fk_records_task` FOREIGN KEY (`task_id`) REFERENCES `tasks` (`id`) ON DELETE CASCADE;

ALTER TABLE `file_info` 
ADD CONSTRAINT `fk_file_info_task` FOREIGN KEY (`task_id`) REFERENCES `tasks` (`id`) ON DELETE SET NULL;

-- ========================================
-- 第七步：验证数据迁移
-- ========================================

-- 检查迁移的任务数量
SELECT 
    '原records表中的任务数量' as description,
    COUNT(*) as count
FROM records 
WHERE type = 'TASK'
UNION ALL
SELECT 
    '新tasks表中的任务数量' as description,
    COUNT(*) as count
FROM tasks;

-- 检查关联关系
SELECT 
    '关联到任务的提交记录数量' as description,
    COUNT(*) as count
FROM records 
WHERE type = 'SUBMISSION' AND task_id IS NOT NULL
UNION ALL
SELECT 
    '关联到任务的文件数量' as description,
    COUNT(*) as count
FROM file_info 
WHERE task_id IS NOT NULL;

-- ========================================
-- 第八步：创建任务ID映射表（用于前端兼容）
-- ========================================
CREATE TABLE `task_record_mapping` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `old_record_id` bigint(20) NOT NULL COMMENT '原records表中的任务ID',
  `new_task_id` bigint(20) NOT NULL COMMENT '新tasks表中的任务ID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_old_record_id` (`old_record_id`),
  UNIQUE KEY `uk_new_task_id` (`new_task_id`),
  KEY `idx_old_record_id` (`old_record_id`),
  KEY `idx_new_task_id` (`new_task_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='任务ID映射表';

-- 填充映射表
INSERT INTO task_record_mapping (old_record_id, new_task_id)
SELECT r.id as old_record_id, t.id as new_task_id
FROM records r
JOIN tasks t ON (
    r.title = t.title 
    AND r.project_id = t.project_id 
    AND r.team_id = t.team_id 
    AND r.user_id = t.creator_id
    AND r.create_time = t.create_time
)
WHERE r.type = 'TASK';

-- ========================================
-- 第九步：创建兼容性视图（临时解决方案）
-- ========================================
-- 这个视图让前端可以继续使用原有的records API，同时获取tasks表的数据
CREATE VIEW `records_with_tasks` AS
SELECT
    r.id,
    r.type,
    r.title,
    r.content,
    r.project_id,
    r.team_id,
    r.user_id,
    r.parent_id,
    r.task_id,
    r.target_id,
    r.status,
    r.priority,
    r.due_date,
    r.create_time,
    r.update_time,
    r.attachments,
    r.extra_data
FROM records r
WHERE r.type != 'TASK'

UNION ALL

-- 将tasks表的数据映射为records格式，使用原始record_id
SELECT
    COALESCE(trm.old_record_id, t.id + 100000) as id,  -- 使用映射表中的原始ID，如果没有则使用偏移ID
    'TASK' as type,
    t.title,
    t.description as content,
    t.project_id,
    t.team_id,
    t.creator_id as user_id,
    NULL as parent_id,
    NULL as task_id,
    NULL as target_id,
    t.status,
    t.priority,
    t.due_date,
    t.create_time,
    t.update_time,
    t.attachments,
    t.extra_data
FROM tasks t
LEFT JOIN task_record_mapping trm ON t.id = trm.new_task_id;

-- 提交事务
COMMIT;

-- ========================================
-- 验证脚本执行结果
-- ========================================
SELECT '数据库重构完成' as message;
SELECT 'tasks表记录数' as description, COUNT(*) as count FROM tasks;
SELECT 'records表记录数' as description, COUNT(*) as count FROM records;
SELECT '映射表记录数' as description, COUNT(*) as count FROM task_record_mapping;
