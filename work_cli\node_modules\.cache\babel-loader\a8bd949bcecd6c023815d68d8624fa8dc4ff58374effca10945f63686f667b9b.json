{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.iterator.find.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport { ref, reactive, computed, onMounted } from 'vue';\nimport { ElMessage, ElMessageBox } from 'element-plus';\nimport { Refresh, Paperclip, Document, Download, View } from '@element-plus/icons-vue';\nimport { recordAPI, projectAPI, fileAPI } from '@/api';\nimport { useStore } from 'vuex';\nexport default {\n  name: 'TaskReviewView',\n  components: {\n    Refresh,\n    Paperclip,\n    Document,\n    Download,\n    View\n  },\n  setup() {\n    const store = useStore();\n    const currentUser = computed(() => store.getters.user);\n\n    // 响应式数据\n    const loading = ref(false);\n    const submissions = ref([]);\n    const myProjects = ref([]);\n    const total = ref(0);\n    const currentPage = ref(1);\n    const pageSize = ref(20);\n\n    // 筛选条件\n    const filterStatus = ref('SUBMITTED'); // 默认显示待审核\n    const filterProject = ref('');\n\n    // 对话框\n    const showDetailDialog = ref(false);\n    const selectedSubmission = ref(null);\n\n    // 附件相关\n    const showAttachmentDialog = ref(false);\n    const selectedAttachments = ref([]);\n    const showImagePreview = ref(false);\n    const previewImageUrl = ref('');\n    const currentPreviewAttachment = ref(null);\n\n    // 加载我的项目\n    const loadMyProjects = async () => {\n      try {\n        const response = await projectAPI.getMyProjects();\n        myProjects.value = response?.records || [];\n      } catch (error) {\n        console.error('加载项目失败:', error);\n      }\n    };\n\n    // 加载提交列表\n    const loadSubmissions = async () => {\n      try {\n        loading.value = true;\n        const params = {\n          page: currentPage.value,\n          size: pageSize.value,\n          type: 'TASK',\n          // 查询任务类型，而不是SUBMISSION\n          sortBy: 'updateTime',\n          // 按更新时间排序\n          sortDir: 'desc'\n        };\n        const response = await recordAPI.getRecords(params);\n        let allSubmissions = response?.records || [];\n\n        // 筛选已提交的任务（状态为SUBMITTED、COMPLETED、REJECTED）\n        allSubmissions = allSubmissions.filter(task => ['SUBMITTED', 'COMPLETED', 'REJECTED'].includes(task.status));\n\n        // 前端筛选状态\n        if (filterStatus.value) {\n          allSubmissions = allSubmissions.filter(submission => submission.status === filterStatus.value);\n        }\n\n        // 前端筛选项目\n        if (filterProject.value) {\n          allSubmissions = allSubmissions.filter(submission => submission.projectId === filterProject.value);\n        }\n        submissions.value = allSubmissions;\n        total.value = allSubmissions.length;\n        console.log('加载的提交任务:', allSubmissions.map(task => ({\n          id: task.id,\n          title: task.title,\n          status: task.status,\n          updateTime: task.updateTime\n        })));\n      } catch (error) {\n        console.error('加载提交列表失败:', error);\n        ElMessage.error('加载提交列表失败');\n      } finally {\n        loading.value = false;\n      }\n    };\n\n    // 加载原任务信息和附件信息\n    const loadParentTasks = async () => {\n      for (const submission of submissions.value) {\n        // 加载原任务信息\n        if (submission.parentId) {\n          try {\n            const parentTask = await recordAPI.getRecord(submission.parentId);\n            submission.parentTask = parentTask;\n          } catch (error) {\n            console.error('加载原任务失败:', error);\n          }\n        }\n\n        // 加载附件信息\n        if (submission.id) {\n          try {\n            console.log(`🔍 正在加载提交 ${submission.id} 的附件信息...`);\n            const response = await fileAPI.getRecordFiles(submission.id);\n            console.log(`📎 提交 ${submission.id} 的附件响应:`, response);\n            const attachmentFiles = response?.data || response || [];\n            if (attachmentFiles && attachmentFiles.length > 0) {\n              // 将真实的文件信息存储到submission中\n              submission.attachmentFiles = attachmentFiles;\n              // 更新attachments字段为文件数量提示\n              submission.attachments = `${attachmentFiles.length}个文件`;\n              console.log(`✅ 提交 ${submission.id} 加载了 ${attachmentFiles.length} 个附件`);\n\n              // 验证文件信息完整性\n              attachmentFiles.forEach((file, index) => {\n                if (!file.id) {\n                  console.warn(`⚠️ 文件 ${index} 缺少ID:`, file);\n                }\n              });\n            } else {\n              console.log(`ℹ️ 提交 ${submission.id} 没有附件`);\n            }\n          } catch (error) {\n            console.error(`❌ 加载提交 ${submission.id} 的附件信息失败:`, error);\n            console.error('❌ 错误详情:', {\n              status: error.response?.status,\n              message: error.message,\n              data: error.response?.data\n            });\n          }\n        }\n      }\n    };\n\n    // 查看提交详情\n    const viewSubmission = submission => {\n      selectedSubmission.value = submission;\n      showDetailDialog.value = true;\n    };\n\n    // 通过提交\n    const approveSubmission = async submission => {\n      try {\n        await ElMessageBox.confirm(`确定要通过\"${submission.title}\"的提交吗？`, '通过审核', {\n          confirmButtonText: '通过',\n          cancelButtonText: '取消',\n          type: 'success'\n        });\n        await recordAPI.updateTaskStatus(submission.id, 'COMPLETED');\n        ElMessage.success('审核通过');\n        showDetailDialog.value = false;\n        loadSubmissions();\n      } catch (error) {\n        if (error !== 'cancel') {\n          console.error('审核失败:', error);\n          ElMessage.error('审核失败');\n        }\n      }\n    };\n\n    // 拒绝提交\n    const rejectSubmission = async submission => {\n      try {\n        const {\n          value: feedback\n        } = await ElMessageBox.prompt('请输入拒绝理由：', '拒绝提交', {\n          confirmButtonText: '确定',\n          cancelButtonText: '取消',\n          inputType: 'textarea',\n          inputPlaceholder: '请详细说明拒绝的原因...'\n        });\n        await recordAPI.updateTaskStatus(submission.id, 'REJECTED');\n        ElMessage.success('已拒绝提交');\n        showDetailDialog.value = false;\n        loadSubmissions();\n      } catch (error) {\n        if (error !== 'cancel') {\n          console.error('拒绝失败:', error);\n          ElMessage.error('拒绝失败');\n        }\n      }\n    };\n\n    // 状态相关方法\n    const getStatusColor = status => {\n      const colorMap = {\n        'SUBMITTED': 'warning',\n        'COMPLETED': 'success',\n        'REJECTED': 'danger'\n      };\n      return colorMap[status] || 'info';\n    };\n    const getStatusText = status => {\n      const textMap = {\n        'SUBMITTED': '待审核',\n        'COMPLETED': '已通过',\n        'REJECTED': '已拒绝'\n      };\n      return textMap[status] || status;\n    };\n    const getPriorityColor = priority => {\n      if (typeof priority === 'number') {\n        const numberToEnum = {\n          1: 'LOW',\n          2: 'MEDIUM',\n          3: 'HIGH',\n          4: 'URGENT',\n          5: 'URGENT'\n        };\n        priority = numberToEnum[priority] || 'MEDIUM';\n      }\n      const colorMap = {\n        'LOW': 'info',\n        'MEDIUM': 'primary',\n        'HIGH': 'warning',\n        'URGENT': 'danger'\n      };\n      return colorMap[priority] || 'info';\n    };\n    const getPriorityText = priority => {\n      if (typeof priority === 'number') {\n        const numberToEnum = {\n          1: 'LOW',\n          2: 'MEDIUM',\n          3: 'HIGH',\n          4: 'URGENT',\n          5: 'URGENT'\n        };\n        priority = numberToEnum[priority] || 'MEDIUM';\n      }\n      const textMap = {\n        'LOW': '低',\n        'MEDIUM': '中',\n        'HIGH': '高',\n        'URGENT': '紧急'\n      };\n      return textMap[priority] || '中';\n    };\n\n    // 格式化日期\n    const formatDate = dateString => {\n      if (!dateString) return '-';\n      const date = new Date(dateString);\n      return date.toLocaleString('zh-CN');\n    };\n\n    // 附件相关方法\n    const hasAttachments = submission => {\n      // 优先检查真实的文件信息\n      if (submission.attachmentFiles && submission.attachmentFiles.length > 0) {\n        return true;\n      }\n\n      // 检查attachments字段\n      const attachments = submission.attachments;\n      if (!attachments || attachments.trim() === '') {\n        return false;\n      }\n      try {\n        // 如果是JSON格式\n        if (attachments.startsWith('{') || attachments.startsWith('[')) {\n          const parsed = JSON.parse(attachments);\n          if (Array.isArray(parsed)) {\n            return parsed.length > 0;\n          }\n          return true;\n        }\n        // 如果是分号分隔的文件名，检查是否有非空的文件名\n        const fileNames = attachments.split(';').filter(name => name.trim());\n        return fileNames.length > 0;\n      } catch (error) {\n        // 如果解析失败，按分号分隔检查\n        const fileNames = attachments.split(';').filter(name => name.trim());\n        return fileNames.length > 0;\n      }\n    };\n    const getAttachmentCount = submission => {\n      // 优先使用真实的文件信息\n      if (submission.attachmentFiles && submission.attachmentFiles.length > 0) {\n        return submission.attachmentFiles.length;\n      }\n\n      // 回退到解析attachments字段\n      const attachments = submission.attachments;\n      if (!attachments) return 0;\n      try {\n        // 如果是JSON格式\n        if (attachments.startsWith('{') || attachments.startsWith('[')) {\n          const parsed = JSON.parse(attachments);\n          if (Array.isArray(parsed)) {\n            return parsed.length;\n          }\n          return 1;\n        }\n        // 如果是分号分隔的文件名\n        return attachments.split(';').filter(name => name.trim()).length;\n      } catch (error) {\n        // 如果解析失败，按分号分隔计算\n        return attachments.split(';').filter(name => name.trim()).length;\n      }\n    };\n    const parseAttachments = attachments => {\n      if (!attachments) return [];\n      try {\n        // 尝试解析JSON格式\n        if (attachments.startsWith('{') || attachments.startsWith('[')) {\n          const parsed = JSON.parse(attachments);\n          if (Array.isArray(parsed)) {\n            return parsed.map(item => ({\n              name: item.name || item.filename || item,\n              id: item.id,\n              url: item.url || item.path,\n              size: item.size,\n              type: item.type || item.contentType\n            }));\n          }\n          return [{\n            name: parsed.name || parsed.filename || '未知文件',\n            id: parsed.id,\n            url: parsed.url || parsed.path,\n            size: parsed.size,\n            type: parsed.type || parsed.contentType\n          }];\n        }\n\n        // 按分号分隔的简单格式 - 需要查找文件ID\n        return attachments.split(';').filter(name => name.trim()).map(name => ({\n          name: name.trim(),\n          id: null,\n          // 需要通过API查找\n          url: null,\n          size: null,\n          type: null,\n          needsLookup: true // 标记需要查找文件ID\n        }));\n      } catch (error) {\n        console.error('解析附件信息失败:', error);\n        return [{\n          name: '附件解析失败',\n          url: null,\n          size: null,\n          type: null\n        }];\n      }\n    };\n    const viewAttachments = async submission => {\n      console.log('🔍 查看附件 - 提交记录:', submission);\n\n      // 优先使用真实的文件信息\n      if (submission.attachmentFiles && submission.attachmentFiles.length > 0) {\n        console.log('✅ 使用已加载的附件文件信息:', submission.attachmentFiles);\n        selectedAttachments.value = submission.attachmentFiles.map(file => ({\n          id: file.id,\n          name: file.originalName || file.fileName || file.name,\n          url: `/api/files/${file.id}/download`,\n          previewUrl: `/api/files/${file.id}/preview`,\n          size: file.fileSize || file.size,\n          type: file.fileType || file.contentType || file.type,\n          uploadTime: file.uploadTime || file.createTime\n        }));\n      } else {\n        console.log('🔄 需要查找附件文件信息...');\n\n        // 首先尝试直接通过记录ID获取文件\n        try {\n          console.log(`📡 调用 fileAPI.getRecordFiles(${submission.id})`);\n          const response = await fileAPI.getRecordFiles(submission.id);\n          console.log('📡 API响应:', response);\n          const files = response?.data || response || [];\n          if (files && files.length > 0) {\n            console.log('✅ 成功获取文件列表:', files);\n            selectedAttachments.value = files.map(file => ({\n              id: file.id,\n              name: file.originalName || file.fileName || file.name,\n              url: `/api/files/${file.id}/download`,\n              previewUrl: `/api/files/${file.id}/preview`,\n              size: file.fileSize || file.size,\n              type: file.fileType || file.contentType || file.type,\n              uploadTime: file.uploadTime || file.createTime\n            }));\n          } else {\n            console.log('⚠️ 未找到文件，尝试解析attachments字段');\n            // 回退到解析attachments字段\n            const parsedAttachments = parseAttachments(submission.attachments);\n            console.log('📋 解析的附件信息:', parsedAttachments);\n\n            // 查找需要查找ID的附件\n            const attachmentsWithIds = await Promise.all(parsedAttachments.map(async attachment => {\n              if (attachment.needsLookup) {\n                try {\n                  console.log(`🔍 查找文件: ${attachment.name}`);\n                  // 根据记录ID查找关联的文件\n                  const files = await fileAPI.getFilesByRecord(submission.id);\n                  console.log('🔍 查找结果:', files);\n                  const fileList = files?.data || files || [];\n                  const matchedFile = fileList.find(file => file.originalName === attachment.name || file.fileName === attachment.name || file.name === attachment.name);\n                  if (matchedFile) {\n                    console.log('✅ 找到匹配文件:', matchedFile);\n                    return {\n                      id: matchedFile.id,\n                      name: matchedFile.originalName || matchedFile.fileName || matchedFile.name,\n                      url: `/api/files/${matchedFile.id}/download`,\n                      previewUrl: `/api/files/${matchedFile.id}/preview`,\n                      size: matchedFile.fileSize || matchedFile.size,\n                      type: matchedFile.fileType || matchedFile.contentType || matchedFile.type,\n                      uploadTime: matchedFile.uploadTime || matchedFile.createTime\n                    };\n                  } else {\n                    console.log('❌ 未找到匹配文件:', attachment.name);\n                  }\n                } catch (error) {\n                  console.error('❌ 查找文件ID失败:', error);\n                }\n              }\n              return attachment;\n            }));\n            selectedAttachments.value = attachmentsWithIds;\n          }\n        } catch (error) {\n          console.error('❌ 获取记录文件失败:', error);\n          ElMessage.error('获取附件信息失败: ' + (error.message || '未知错误'));\n          selectedAttachments.value = [];\n        }\n      }\n      console.log('📎 最终附件列表:', selectedAttachments.value);\n      showAttachmentDialog.value = true;\n    };\n    const downloadAttachment = async attachment => {\n      console.log('📥 开始下载附件:', attachment);\n      try {\n        if (attachment.id) {\n          console.log(`📡 使用文件ID下载: ${attachment.id}`);\n          // 使用文件API下载\n          const response = await fileAPI.downloadFile(attachment.id);\n          console.log('📡 下载响应:', response);\n          console.log('📡 响应类型:', typeof response);\n          console.log('📡 是否为Blob:', response instanceof Blob);\n\n          // 处理响应数据\n          let blob;\n          if (response.data && response.data instanceof Blob) {\n            // 如果response.data是Blob\n            blob = response.data;\n          } else if (response instanceof Blob) {\n            // 如果response本身是Blob\n            blob = response;\n          } else {\n            // 如果都不是，尝试创建Blob\n            console.error('❌ 响应不是Blob格式:', response);\n            ElMessage.error('文件下载失败：响应格式错误');\n            return;\n          }\n\n          // 创建下载链接\n          const url = window.URL.createObjectURL(blob);\n          const link = document.createElement('a');\n          link.href = url;\n          link.download = attachment.name;\n          document.body.appendChild(link);\n          link.click();\n          document.body.removeChild(link);\n          window.URL.revokeObjectURL(url);\n          console.log('✅ 文件下载成功');\n          ElMessage.success('文件下载成功');\n        } else if (attachment.url) {\n          console.log(`🔗 使用URL下载: ${attachment.url}`);\n          // 如果有URL，直接下载\n          const link = document.createElement('a');\n          link.href = attachment.url;\n          link.download = attachment.name;\n          link.target = '_blank'; // 在新窗口打开，避免跨域问题\n          document.body.appendChild(link);\n          link.click();\n          document.body.removeChild(link);\n          console.log('✅ URL下载触发成功');\n          ElMessage.success('文件下载已开始');\n        } else {\n          console.log('❌ 附件缺少ID和URL信息:', attachment);\n          // 如果没有ID和URL，尝试通过文件名查找\n          ElMessage.warning(`附件\"${attachment.name}\"暂无下载链接。请尝试刷新页面或联系管理员。`);\n\n          // 提供调试信息\n          console.log('🔍 调试信息:');\n          console.log('- 附件对象:', attachment);\n          console.log('- 是否有ID:', !!attachment.id);\n          console.log('- 是否有URL:', !!attachment.url);\n          console.log('- 是否需要查找:', attachment.needsLookup);\n        }\n      } catch (error) {\n        console.error('❌ 下载附件失败:', error);\n        console.error('❌ 错误详情:', {\n          message: error.message,\n          status: error.response?.status,\n          data: error.response?.data\n        });\n\n        // 根据错误类型提供不同的提示\n        if (error.response?.status === 404) {\n          ElMessage.error('文件不存在或已被删除');\n        } else if (error.response?.status === 403) {\n          ElMessage.error('没有权限下载此文件');\n        } else if (error.response?.status === 401) {\n          ElMessage.error('请先登录');\n        } else {\n          ElMessage.error('下载附件失败: ' + (error.message || '未知错误'));\n        }\n      }\n    };\n    const downloadAllAttachments = async () => {\n      for (const attachment of selectedAttachments.value) {\n        await downloadAttachment(attachment);\n        // 添加延迟避免浏览器阻止多个下载\n        await new Promise(resolve => setTimeout(resolve, 500));\n      }\n    };\n    const isImageFile = filename => {\n      if (!filename) return false;\n      const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg'];\n      const ext = filename.toLowerCase().substring(filename.lastIndexOf('.'));\n      return imageExtensions.includes(ext);\n    };\n    const previewAttachment = async attachment => {\n      try {\n        if (attachment.id) {\n          // 使用文件API预览\n          const response = await fileAPI.previewFile(attachment.id);\n          const blob = new Blob([response]);\n          previewImageUrl.value = window.URL.createObjectURL(blob);\n          currentPreviewAttachment.value = attachment;\n          showImagePreview.value = true;\n        } else if (attachment.previewUrl || attachment.url) {\n          previewImageUrl.value = attachment.previewUrl || attachment.url;\n          currentPreviewAttachment.value = attachment;\n          showImagePreview.value = true;\n        } else {\n          ElMessage.warning('该图片暂无预览链接');\n        }\n      } catch (error) {\n        console.error('预览图片失败:', error);\n        ElMessage.error('预览图片失败');\n      }\n    };\n    const formatFileSize = bytes => {\n      if (!bytes) return '-';\n      const sizes = ['B', 'KB', 'MB', 'GB'];\n      const i = Math.floor(Math.log(bytes) / Math.log(1024));\n      return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];\n    };\n\n    // 生命周期\n    onMounted(() => {\n      loadMyProjects();\n      loadSubmissions();\n    });\n    return {\n      // 数据\n      loading,\n      submissions,\n      myProjects,\n      total,\n      currentPage,\n      pageSize,\n      filterStatus,\n      filterProject,\n      showDetailDialog,\n      selectedSubmission,\n      showAttachmentDialog,\n      selectedAttachments,\n      showImagePreview,\n      previewImageUrl,\n      currentPreviewAttachment,\n      currentUser,\n      // 方法\n      loadSubmissions,\n      viewSubmission,\n      approveSubmission,\n      rejectSubmission,\n      getStatusColor,\n      getStatusText,\n      getPriorityColor,\n      getPriorityText,\n      formatDate,\n      // 附件方法\n      hasAttachments,\n      getAttachmentCount,\n      parseAttachments,\n      viewAttachments,\n      downloadAttachment,\n      downloadAllAttachments,\n      isImageFile,\n      previewAttachment,\n      formatFileSize\n    };\n  }\n};", "map": {"version": 3, "names": ["ref", "reactive", "computed", "onMounted", "ElMessage", "ElMessageBox", "Refresh", "Paperclip", "Document", "Download", "View", "recordAPI", "projectAPI", "fileAPI", "useStore", "name", "components", "setup", "store", "currentUser", "getters", "user", "loading", "submissions", "myProjects", "total", "currentPage", "pageSize", "filterStatus", "filterProject", "showDetailDialog", "selectedSubmission", "showAttachmentDialog", "selectedAttachments", "showImagePreview", "previewImageUrl", "currentPreviewAttachment", "loadMyProjects", "response", "getMyProjects", "value", "records", "error", "console", "loadSubmissions", "params", "page", "size", "type", "sortBy", "sortDir", "getRecords", "allSubmissions", "filter", "task", "includes", "status", "submission", "projectId", "length", "log", "map", "id", "title", "updateTime", "loadParentTasks", "parentId", "parentTask", "getRecord", "getRecordFiles", "attachmentFiles", "data", "attachments", "for<PERSON>ach", "file", "index", "warn", "message", "viewSubmission", "approveSubmission", "confirm", "confirmButtonText", "cancelButtonText", "updateTaskStatus", "success", "rejectSubmission", "feedback", "prompt", "inputType", "inputPlaceholder", "getStatusColor", "colorMap", "getStatusText", "textMap", "getPriorityColor", "priority", "numberToEnum", "getPriorityText", "formatDate", "dateString", "date", "Date", "toLocaleString", "hasAttachments", "trim", "startsWith", "parsed", "JSON", "parse", "Array", "isArray", "fileNames", "split", "getAttachmentCount", "parseAttachments", "item", "filename", "url", "path", "contentType", "needsLookup", "viewAttachments", "originalName", "fileName", "previewUrl", "fileSize", "fileType", "uploadTime", "createTime", "files", "parsedAttachments", "attachmentsWithIds", "Promise", "all", "attachment", "getFilesByRecord", "fileList", "matchedFile", "find", "downloadAttachment", "downloadFile", "Blob", "blob", "window", "URL", "createObjectURL", "link", "document", "createElement", "href", "download", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "target", "warning", "downloadAllAttachments", "resolve", "setTimeout", "isImageFile", "imageExtensions", "ext", "toLowerCase", "substring", "lastIndexOf", "previewAttachment", "previewFile", "formatFileSize", "bytes", "sizes", "i", "Math", "floor", "round", "pow"], "sources": ["D:\\workspace\\idea\\worker\\work_cli\\src\\views\\task\\TaskReviewView.vue"], "sourcesContent": ["<template>\n  <div class=\"task-review-container\">\n    <el-card class=\"page-card\">\n      <template #header>\n        <div class=\"card-header\">\n          <h2>任务审核</h2>\n          <p class=\"subtitle\">审核学生提交的任务</p>\n        </div>\n      </template>\n\n      <!-- 筛选条件 -->\n      <div class=\"filter-section\">\n        <el-row :gutter=\"20\">\n          <el-col :span=\"6\">\n            <el-select v-model=\"filterStatus\" placeholder=\"选择状态\" @change=\"loadSubmissions\">\n              <el-option label=\"全部\" value=\"\" />\n              <el-option label=\"待审核\" value=\"SUBMITTED\" />\n              <el-option label=\"已通过\" value=\"COMPLETED\" />\n              <el-option label=\"已拒绝\" value=\"REJECTED\" />\n            </el-select>\n          </el-col>\n          <el-col :span=\"6\">\n            <el-select v-model=\"filterProject\" placeholder=\"选择项目\" @change=\"loadSubmissions\">\n              <el-option label=\"全部项目\" value=\"\" />\n              <el-option \n                v-for=\"project in myProjects\" \n                :key=\"project.id\" \n                :label=\"project.name || project.title\" \n                :value=\"project.id\" \n              />\n            </el-select>\n          </el-col>\n          <el-col :span=\"6\">\n            <el-button type=\"primary\" @click=\"loadSubmissions\" :loading=\"loading\">\n              <el-icon><Refresh /></el-icon>\n              刷新\n            </el-button>\n          </el-col>\n        </el-row>\n      </div>\n\n      <!-- 提交列表 -->\n      <div class=\"submissions-list\">\n        <div v-if=\"loading\" class=\"loading-state\">\n          <el-skeleton :rows=\"5\" animated />\n        </div>\n\n        <div v-else-if=\"submissions.length === 0\" class=\"empty-state\">\n          <el-empty description=\"暂无任务提交\">\n            <el-button type=\"primary\" @click=\"loadSubmissions\">刷新数据</el-button>\n          </el-empty>\n        </div>\n\n        <div v-else class=\"submission-items\">\n          <div v-for=\"submission in submissions\" :key=\"submission.id\" class=\"submission-item\">\n            <el-card class=\"submission-card\" shadow=\"hover\">\n              <div class=\"submission-header\">\n                <div class=\"submission-info\">\n                  <h4 class=\"task-title\">{{ submission.title }}</h4>\n                  <div class=\"submission-meta\">\n                    <el-tag :type=\"getStatusColor(submission.status)\" size=\"small\">\n                      {{ getStatusText(submission.status) }}\n                    </el-tag>\n                    <span class=\"submitter\">提交人：{{ submission.creator?.realName }}</span>\n                    <span class=\"submit-time\">提交时间：{{ formatDate(submission.createTime) }}</span>\n                  </div>\n                </div>\n                <div class=\"submission-actions\">\n                  <el-button size=\"small\" @click=\"viewSubmission(submission)\">查看详情</el-button>\n                  <el-button \n                    v-if=\"submission.status === 'SUBMITTED'\"\n                    type=\"success\" \n                    size=\"small\" \n                    @click=\"approveSubmission(submission)\"\n                  >\n                    通过\n                  </el-button>\n                  <el-button \n                    v-if=\"submission.status === 'SUBMITTED'\"\n                    type=\"danger\" \n                    size=\"small\" \n                    @click=\"rejectSubmission(submission)\"\n                  >\n                    拒绝\n                  </el-button>\n                </div>\n              </div>\n              \n              <div class=\"submission-content\">\n                <p class=\"content-preview\">{{ submission.content }}</p>\n                <div v-if=\"hasAttachments(submission)\" class=\"attachments\">\n                  <el-icon><Paperclip /></el-icon>\n                  <span>{{ getAttachmentCount(submission) }}个附件</span>\n                  <el-button\n                    type=\"text\"\n                    size=\"small\"\n                    @click=\"viewAttachments(submission)\"\n                    class=\"view-attachments-btn\"\n                  >\n                    查看附件\n                  </el-button>\n                </div>\n              </div>\n\n              <!-- 原任务信息 -->\n              <div v-if=\"submission.parentTask\" class=\"parent-task\">\n                <div class=\"parent-task-header\">\n                  <el-icon><Document /></el-icon>\n                  <span>原任务：{{ submission.parentTask.title }}</span>\n                </div>\n                <p class=\"parent-task-content\">{{ submission.parentTask.content }}</p>\n              </div>\n            </el-card>\n          </div>\n        </div>\n      </div>\n\n      <!-- 分页 -->\n      <div v-if=\"total > 0\" class=\"pagination\">\n        <el-pagination\n          v-model:current-page=\"currentPage\"\n          v-model:page-size=\"pageSize\"\n          :total=\"total\"\n          :page-sizes=\"[10, 20, 50]\"\n          layout=\"total, sizes, prev, pager, next, jumper\"\n          @size-change=\"loadSubmissions\"\n          @current-change=\"loadSubmissions\"\n        />\n      </div>\n    </el-card>\n\n    <!-- 提交详情对话框 -->\n    <el-dialog v-model=\"showDetailDialog\" title=\"提交详情\" width=\"800px\">\n      <div v-if=\"selectedSubmission\" class=\"submission-detail\">\n        <el-descriptions :column=\"2\" border>\n          <el-descriptions-item label=\"任务标题\" :span=\"2\">\n            {{ selectedSubmission.parentTask?.title || '未知任务' }}\n          </el-descriptions-item>\n          <el-descriptions-item label=\"提交标题\" :span=\"2\">\n            {{ selectedSubmission.title }}\n          </el-descriptions-item>\n          <el-descriptions-item label=\"提交状态\">\n            <el-tag :type=\"getStatusColor(selectedSubmission.status)\">\n              {{ getStatusText(selectedSubmission.status) }}\n            </el-tag>\n          </el-descriptions-item>\n          <el-descriptions-item label=\"提交人\">\n            {{ selectedSubmission.creator?.realName }}\n          </el-descriptions-item>\n          <el-descriptions-item label=\"提交时间\" :span=\"2\">\n            {{ formatDate(selectedSubmission.createTime) }}\n          </el-descriptions-item>\n          <el-descriptions-item label=\"提交内容\" :span=\"2\">\n            <div class=\"submission-content-detail\">\n              {{ selectedSubmission.content }}\n            </div>\n          </el-descriptions-item>\n          <el-descriptions-item v-if=\"hasAttachments(selectedSubmission)\" label=\"附件列表\" :span=\"2\">\n            <div class=\"attachment-list\">\n              <div\n                v-for=\"(attachment, index) in parseAttachments(selectedSubmission.attachments)\"\n                :key=\"index\"\n                class=\"attachment-item\"\n              >\n                <el-icon><Document /></el-icon>\n                <span class=\"attachment-name\">{{ attachment.name }}</span>\n                <el-button\n                  type=\"text\"\n                  size=\"small\"\n                  @click=\"downloadAttachment(attachment)\"\n                  class=\"download-btn\"\n                >\n                  下载\n                </el-button>\n              </div>\n            </div>\n          </el-descriptions-item>\n        </el-descriptions>\n\n        <!-- 原任务详情 -->\n        <div v-if=\"selectedSubmission.parentTask\" class=\"original-task-section\">\n          <h4>原任务详情</h4>\n          <el-descriptions :column=\"2\" border>\n            <el-descriptions-item label=\"任务标题\" :span=\"2\">\n              {{ selectedSubmission.parentTask.title }}\n            </el-descriptions-item>\n            <el-descriptions-item label=\"任务内容\" :span=\"2\">\n              {{ selectedSubmission.parentTask.content }}\n            </el-descriptions-item>\n            <el-descriptions-item label=\"优先级\">\n              <el-tag :type=\"getPriorityColor(selectedSubmission.parentTask.priority)\" size=\"small\">\n                {{ getPriorityText(selectedSubmission.parentTask.priority) }}\n              </el-tag>\n            </el-descriptions-item>\n            <el-descriptions-item label=\"截止时间\">\n              {{ formatDate(selectedSubmission.parentTask.dueDate) }}\n            </el-descriptions-item>\n          </el-descriptions>\n        </div>\n      </div>\n\n      <template #footer>\n        <div class=\"dialog-footer\">\n          <el-button @click=\"showDetailDialog = false\">关闭</el-button>\n          <el-button \n            v-if=\"selectedSubmission?.status === 'SUBMITTED'\"\n            type=\"success\" \n            @click=\"approveSubmission(selectedSubmission)\"\n          >\n            通过审核\n          </el-button>\n          <el-button \n            v-if=\"selectedSubmission?.status === 'SUBMITTED'\"\n            type=\"danger\" \n            @click=\"rejectSubmission(selectedSubmission)\"\n          >\n            拒绝提交\n          </el-button>\n        </div>\n      </template>\n    </el-dialog>\n\n    <!-- 附件查看对话框 -->\n    <el-dialog v-model=\"showAttachmentDialog\" title=\"附件列表\" width=\"600px\">\n      <div v-if=\"selectedAttachments.length > 0\" class=\"attachment-dialog\">\n        <div\n          v-for=\"(attachment, index) in selectedAttachments\"\n          :key=\"index\"\n          class=\"attachment-dialog-item\"\n        >\n          <div class=\"attachment-info\">\n            <el-icon size=\"20\"><Document /></el-icon>\n            <div class=\"attachment-details\">\n              <div class=\"attachment-name\">{{ attachment.name }}</div>\n              <div class=\"attachment-meta\">\n                <span v-if=\"attachment.size\" class=\"file-size\">{{ formatFileSize(attachment.size) }}</span>\n                <span v-if=\"attachment.type\" class=\"file-type\">{{ attachment.type }}</span>\n              </div>\n            </div>\n          </div>\n          <div class=\"attachment-actions\">\n            <el-button\n              type=\"primary\"\n              size=\"small\"\n              @click=\"downloadAttachment(attachment)\"\n            >\n              <el-icon><Download /></el-icon>\n              下载\n            </el-button>\n            <el-button\n              v-if=\"isImageFile(attachment.name)\"\n              type=\"success\"\n              size=\"small\"\n              @click=\"previewAttachment(attachment)\"\n            >\n              <el-icon><View /></el-icon>\n              预览\n            </el-button>\n          </div>\n        </div>\n      </div>\n      <div v-else class=\"no-attachments\">\n        <el-empty description=\"暂无附件\" />\n      </div>\n\n      <template #footer>\n        <el-button @click=\"showAttachmentDialog = false\">关闭</el-button>\n        <el-button\n          v-if=\"selectedAttachments.length > 0\"\n          type=\"primary\"\n          @click=\"downloadAllAttachments\"\n        >\n          下载全部\n        </el-button>\n      </template>\n    </el-dialog>\n\n    <!-- 图片预览对话框 -->\n    <el-dialog v-model=\"showImagePreview\" title=\"图片预览\" width=\"80%\">\n      <div class=\"image-preview\">\n        <img\n          v-if=\"previewImageUrl\"\n          :src=\"previewImageUrl\"\n          alt=\"预览图片\"\n          class=\"preview-image\"\n        />\n      </div>\n      <template #footer>\n        <el-button @click=\"showImagePreview = false\">关闭</el-button>\n        <el-button\n          type=\"primary\"\n          @click=\"downloadAttachment(currentPreviewAttachment)\"\n        >\n          下载原图\n        </el-button>\n      </template>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { ref, reactive, computed, onMounted } from 'vue'\nimport { ElMessage, ElMessageBox } from 'element-plus'\nimport { Refresh, Paperclip, Document, Download, View } from '@element-plus/icons-vue'\nimport { recordAPI, projectAPI, fileAPI } from '@/api'\nimport { useStore } from 'vuex'\n\nexport default {\n  name: 'TaskReviewView',\n  components: {\n    Refresh,\n    Paperclip,\n    Document,\n    Download,\n    View\n  },\n  setup() {\n    const store = useStore()\n    const currentUser = computed(() => store.getters.user)\n\n    // 响应式数据\n    const loading = ref(false)\n    const submissions = ref([])\n    const myProjects = ref([])\n    const total = ref(0)\n    const currentPage = ref(1)\n    const pageSize = ref(20)\n\n    // 筛选条件\n    const filterStatus = ref('SUBMITTED') // 默认显示待审核\n    const filterProject = ref('')\n\n    // 对话框\n    const showDetailDialog = ref(false)\n    const selectedSubmission = ref(null)\n\n    // 附件相关\n    const showAttachmentDialog = ref(false)\n    const selectedAttachments = ref([])\n    const showImagePreview = ref(false)\n    const previewImageUrl = ref('')\n    const currentPreviewAttachment = ref(null)\n\n    // 加载我的项目\n    const loadMyProjects = async () => {\n      try {\n        const response = await projectAPI.getMyProjects()\n        myProjects.value = response?.records || []\n      } catch (error) {\n        console.error('加载项目失败:', error)\n      }\n    }\n\n    // 加载提交列表\n    const loadSubmissions = async () => {\n      try {\n        loading.value = true\n        const params = {\n          page: currentPage.value,\n          size: pageSize.value,\n          type: 'TASK',  // 查询任务类型，而不是SUBMISSION\n          sortBy: 'updateTime',  // 按更新时间排序\n          sortDir: 'desc'\n        }\n\n        const response = await recordAPI.getRecords(params)\n        let allSubmissions = response?.records || []\n\n        // 筛选已提交的任务（状态为SUBMITTED、COMPLETED、REJECTED）\n        allSubmissions = allSubmissions.filter(task =>\n          ['SUBMITTED', 'COMPLETED', 'REJECTED'].includes(task.status)\n        )\n\n        // 前端筛选状态\n        if (filterStatus.value) {\n          allSubmissions = allSubmissions.filter(submission =>\n            submission.status === filterStatus.value\n          )\n        }\n\n        // 前端筛选项目\n        if (filterProject.value) {\n          allSubmissions = allSubmissions.filter(submission =>\n            submission.projectId === filterProject.value\n          )\n        }\n\n        submissions.value = allSubmissions\n        total.value = allSubmissions.length\n\n        console.log('加载的提交任务:', allSubmissions.map(task => ({\n          id: task.id,\n          title: task.title,\n          status: task.status,\n          updateTime: task.updateTime\n        })))\n      } catch (error) {\n        console.error('加载提交列表失败:', error)\n        ElMessage.error('加载提交列表失败')\n      } finally {\n        loading.value = false\n      }\n    }\n\n    // 加载原任务信息和附件信息\n    const loadParentTasks = async () => {\n      for (const submission of submissions.value) {\n        // 加载原任务信息\n        if (submission.parentId) {\n          try {\n            const parentTask = await recordAPI.getRecord(submission.parentId)\n            submission.parentTask = parentTask\n          } catch (error) {\n            console.error('加载原任务失败:', error)\n          }\n        }\n\n        // 加载附件信息\n        if (submission.id) {\n          try {\n            console.log(`🔍 正在加载提交 ${submission.id} 的附件信息...`)\n            const response = await fileAPI.getRecordFiles(submission.id)\n            console.log(`📎 提交 ${submission.id} 的附件响应:`, response)\n\n            const attachmentFiles = response?.data || response || []\n            if (attachmentFiles && attachmentFiles.length > 0) {\n              // 将真实的文件信息存储到submission中\n              submission.attachmentFiles = attachmentFiles\n              // 更新attachments字段为文件数量提示\n              submission.attachments = `${attachmentFiles.length}个文件`\n              console.log(`✅ 提交 ${submission.id} 加载了 ${attachmentFiles.length} 个附件`)\n\n              // 验证文件信息完整性\n              attachmentFiles.forEach((file, index) => {\n                if (!file.id) {\n                  console.warn(`⚠️ 文件 ${index} 缺少ID:`, file)\n                }\n              })\n            } else {\n              console.log(`ℹ️ 提交 ${submission.id} 没有附件`)\n            }\n          } catch (error) {\n            console.error(`❌ 加载提交 ${submission.id} 的附件信息失败:`, error)\n            console.error('❌ 错误详情:', {\n              status: error.response?.status,\n              message: error.message,\n              data: error.response?.data\n            })\n          }\n        }\n      }\n    }\n\n    // 查看提交详情\n    const viewSubmission = (submission) => {\n      selectedSubmission.value = submission\n      showDetailDialog.value = true\n    }\n\n    // 通过提交\n    const approveSubmission = async (submission) => {\n      try {\n        await ElMessageBox.confirm(\n          `确定要通过\"${submission.title}\"的提交吗？`,\n          '通过审核',\n          {\n            confirmButtonText: '通过',\n            cancelButtonText: '取消',\n            type: 'success'\n          }\n        )\n\n        await recordAPI.updateTaskStatus(submission.id, 'COMPLETED')\n        ElMessage.success('审核通过')\n        showDetailDialog.value = false\n        loadSubmissions()\n      } catch (error) {\n        if (error !== 'cancel') {\n          console.error('审核失败:', error)\n          ElMessage.error('审核失败')\n        }\n      }\n    }\n\n    // 拒绝提交\n    const rejectSubmission = async (submission) => {\n      try {\n        const { value: feedback } = await ElMessageBox.prompt(\n          '请输入拒绝理由：',\n          '拒绝提交',\n          {\n            confirmButtonText: '确定',\n            cancelButtonText: '取消',\n            inputType: 'textarea',\n            inputPlaceholder: '请详细说明拒绝的原因...'\n          }\n        )\n\n        await recordAPI.updateTaskStatus(submission.id, 'REJECTED')\n        ElMessage.success('已拒绝提交')\n        showDetailDialog.value = false\n        loadSubmissions()\n      } catch (error) {\n        if (error !== 'cancel') {\n          console.error('拒绝失败:', error)\n          ElMessage.error('拒绝失败')\n        }\n      }\n    }\n\n    // 状态相关方法\n    const getStatusColor = (status) => {\n      const colorMap = {\n        'SUBMITTED': 'warning',\n        'COMPLETED': 'success',\n        'REJECTED': 'danger'\n      }\n      return colorMap[status] || 'info'\n    }\n\n    const getStatusText = (status) => {\n      const textMap = {\n        'SUBMITTED': '待审核',\n        'COMPLETED': '已通过',\n        'REJECTED': '已拒绝'\n      }\n      return textMap[status] || status\n    }\n\n    const getPriorityColor = (priority) => {\n      if (typeof priority === 'number') {\n        const numberToEnum = {\n          1: 'LOW',\n          2: 'MEDIUM',\n          3: 'HIGH',\n          4: 'URGENT',\n          5: 'URGENT'\n        }\n        priority = numberToEnum[priority] || 'MEDIUM'\n      }\n      \n      const colorMap = {\n        'LOW': 'info',\n        'MEDIUM': 'primary',\n        'HIGH': 'warning',\n        'URGENT': 'danger'\n      }\n      return colorMap[priority] || 'info'\n    }\n\n    const getPriorityText = (priority) => {\n      if (typeof priority === 'number') {\n        const numberToEnum = {\n          1: 'LOW',\n          2: 'MEDIUM',\n          3: 'HIGH',\n          4: 'URGENT',\n          5: 'URGENT'\n        }\n        priority = numberToEnum[priority] || 'MEDIUM'\n      }\n      \n      const textMap = {\n        'LOW': '低',\n        'MEDIUM': '中',\n        'HIGH': '高',\n        'URGENT': '紧急'\n      }\n      return textMap[priority] || '中'\n    }\n\n    // 格式化日期\n    const formatDate = (dateString) => {\n      if (!dateString) return '-'\n      const date = new Date(dateString)\n      return date.toLocaleString('zh-CN')\n    }\n\n    // 附件相关方法\n    const hasAttachments = (submission) => {\n      // 优先检查真实的文件信息\n      if (submission.attachmentFiles && submission.attachmentFiles.length > 0) {\n        return true\n      }\n\n      // 检查attachments字段\n      const attachments = submission.attachments\n      if (!attachments || attachments.trim() === '') {\n        return false\n      }\n\n      try {\n        // 如果是JSON格式\n        if (attachments.startsWith('{') || attachments.startsWith('[')) {\n          const parsed = JSON.parse(attachments)\n          if (Array.isArray(parsed)) {\n            return parsed.length > 0\n          }\n          return true\n        }\n        // 如果是分号分隔的文件名，检查是否有非空的文件名\n        const fileNames = attachments.split(';').filter(name => name.trim())\n        return fileNames.length > 0\n      } catch (error) {\n        // 如果解析失败，按分号分隔检查\n        const fileNames = attachments.split(';').filter(name => name.trim())\n        return fileNames.length > 0\n      }\n    }\n\n    const getAttachmentCount = (submission) => {\n      // 优先使用真实的文件信息\n      if (submission.attachmentFiles && submission.attachmentFiles.length > 0) {\n        return submission.attachmentFiles.length\n      }\n\n      // 回退到解析attachments字段\n      const attachments = submission.attachments\n      if (!attachments) return 0\n\n      try {\n        // 如果是JSON格式\n        if (attachments.startsWith('{') || attachments.startsWith('[')) {\n          const parsed = JSON.parse(attachments)\n          if (Array.isArray(parsed)) {\n            return parsed.length\n          }\n          return 1\n        }\n        // 如果是分号分隔的文件名\n        return attachments.split(';').filter(name => name.trim()).length\n      } catch (error) {\n        // 如果解析失败，按分号分隔计算\n        return attachments.split(';').filter(name => name.trim()).length\n      }\n    }\n\n    const parseAttachments = (attachments) => {\n      if (!attachments) return []\n\n      try {\n        // 尝试解析JSON格式\n        if (attachments.startsWith('{') || attachments.startsWith('[')) {\n          const parsed = JSON.parse(attachments)\n          if (Array.isArray(parsed)) {\n            return parsed.map(item => ({\n              name: item.name || item.filename || item,\n              id: item.id,\n              url: item.url || item.path,\n              size: item.size,\n              type: item.type || item.contentType\n            }))\n          }\n          return [{\n            name: parsed.name || parsed.filename || '未知文件',\n            id: parsed.id,\n            url: parsed.url || parsed.path,\n            size: parsed.size,\n            type: parsed.type || parsed.contentType\n          }]\n        }\n\n        // 按分号分隔的简单格式 - 需要查找文件ID\n        return attachments.split(';')\n          .filter(name => name.trim())\n          .map(name => ({\n            name: name.trim(),\n            id: null,  // 需要通过API查找\n            url: null,\n            size: null,\n            type: null,\n            needsLookup: true  // 标记需要查找文件ID\n          }))\n      } catch (error) {\n        console.error('解析附件信息失败:', error)\n        return [{\n          name: '附件解析失败',\n          url: null,\n          size: null,\n          type: null\n        }]\n      }\n    }\n\n    const viewAttachments = async (submission) => {\n      console.log('🔍 查看附件 - 提交记录:', submission)\n\n      // 优先使用真实的文件信息\n      if (submission.attachmentFiles && submission.attachmentFiles.length > 0) {\n        console.log('✅ 使用已加载的附件文件信息:', submission.attachmentFiles)\n        selectedAttachments.value = submission.attachmentFiles.map(file => ({\n          id: file.id,\n          name: file.originalName || file.fileName || file.name,\n          url: `/api/files/${file.id}/download`,\n          previewUrl: `/api/files/${file.id}/preview`,\n          size: file.fileSize || file.size,\n          type: file.fileType || file.contentType || file.type,\n          uploadTime: file.uploadTime || file.createTime\n        }))\n      } else {\n        console.log('🔄 需要查找附件文件信息...')\n\n        // 首先尝试直接通过记录ID获取文件\n        try {\n          console.log(`📡 调用 fileAPI.getRecordFiles(${submission.id})`)\n          const response = await fileAPI.getRecordFiles(submission.id)\n          console.log('📡 API响应:', response)\n\n          const files = response?.data || response || []\n          if (files && files.length > 0) {\n            console.log('✅ 成功获取文件列表:', files)\n            selectedAttachments.value = files.map(file => ({\n              id: file.id,\n              name: file.originalName || file.fileName || file.name,\n              url: `/api/files/${file.id}/download`,\n              previewUrl: `/api/files/${file.id}/preview`,\n              size: file.fileSize || file.size,\n              type: file.fileType || file.contentType || file.type,\n              uploadTime: file.uploadTime || file.createTime\n            }))\n          } else {\n            console.log('⚠️ 未找到文件，尝试解析attachments字段')\n            // 回退到解析attachments字段\n            const parsedAttachments = parseAttachments(submission.attachments)\n            console.log('📋 解析的附件信息:', parsedAttachments)\n\n            // 查找需要查找ID的附件\n            const attachmentsWithIds = await Promise.all(\n              parsedAttachments.map(async (attachment) => {\n                if (attachment.needsLookup) {\n                  try {\n                    console.log(`🔍 查找文件: ${attachment.name}`)\n                    // 根据记录ID查找关联的文件\n                    const files = await fileAPI.getFilesByRecord(submission.id)\n                    console.log('🔍 查找结果:', files)\n\n                    const fileList = files?.data || files || []\n                    const matchedFile = fileList.find(file =>\n                      file.originalName === attachment.name ||\n                      file.fileName === attachment.name ||\n                      file.name === attachment.name\n                    )\n\n                    if (matchedFile) {\n                      console.log('✅ 找到匹配文件:', matchedFile)\n                      return {\n                        id: matchedFile.id,\n                        name: matchedFile.originalName || matchedFile.fileName || matchedFile.name,\n                        url: `/api/files/${matchedFile.id}/download`,\n                        previewUrl: `/api/files/${matchedFile.id}/preview`,\n                        size: matchedFile.fileSize || matchedFile.size,\n                        type: matchedFile.fileType || matchedFile.contentType || matchedFile.type,\n                        uploadTime: matchedFile.uploadTime || matchedFile.createTime\n                      }\n                    } else {\n                      console.log('❌ 未找到匹配文件:', attachment.name)\n                    }\n                  } catch (error) {\n                    console.error('❌ 查找文件ID失败:', error)\n                  }\n                }\n                return attachment\n              })\n            )\n\n            selectedAttachments.value = attachmentsWithIds\n          }\n        } catch (error) {\n          console.error('❌ 获取记录文件失败:', error)\n          ElMessage.error('获取附件信息失败: ' + (error.message || '未知错误'))\n          selectedAttachments.value = []\n        }\n      }\n\n      console.log('📎 最终附件列表:', selectedAttachments.value)\n      showAttachmentDialog.value = true\n    }\n\n    const downloadAttachment = async (attachment) => {\n      console.log('📥 开始下载附件:', attachment)\n\n      try {\n        if (attachment.id) {\n          console.log(`📡 使用文件ID下载: ${attachment.id}`)\n          // 使用文件API下载\n          const response = await fileAPI.downloadFile(attachment.id)\n\n          console.log('📡 下载响应:', response)\n          console.log('📡 响应类型:', typeof response)\n          console.log('📡 是否为Blob:', response instanceof Blob)\n\n          // 处理响应数据\n          let blob\n          if (response.data && response.data instanceof Blob) {\n            // 如果response.data是Blob\n            blob = response.data\n          } else if (response instanceof Blob) {\n            // 如果response本身是Blob\n            blob = response\n          } else {\n            // 如果都不是，尝试创建Blob\n            console.error('❌ 响应不是Blob格式:', response)\n            ElMessage.error('文件下载失败：响应格式错误')\n            return\n          }\n\n          // 创建下载链接\n          const url = window.URL.createObjectURL(blob)\n          const link = document.createElement('a')\n          link.href = url\n          link.download = attachment.name\n          document.body.appendChild(link)\n          link.click()\n          document.body.removeChild(link)\n          window.URL.revokeObjectURL(url)\n\n          console.log('✅ 文件下载成功')\n          ElMessage.success('文件下载成功')\n        } else if (attachment.url) {\n          console.log(`🔗 使用URL下载: ${attachment.url}`)\n          // 如果有URL，直接下载\n          const link = document.createElement('a')\n          link.href = attachment.url\n          link.download = attachment.name\n          link.target = '_blank'  // 在新窗口打开，避免跨域问题\n          document.body.appendChild(link)\n          link.click()\n          document.body.removeChild(link)\n\n          console.log('✅ URL下载触发成功')\n          ElMessage.success('文件下载已开始')\n        } else {\n          console.log('❌ 附件缺少ID和URL信息:', attachment)\n          // 如果没有ID和URL，尝试通过文件名查找\n          ElMessage.warning(`附件\"${attachment.name}\"暂无下载链接。请尝试刷新页面或联系管理员。`)\n\n          // 提供调试信息\n          console.log('🔍 调试信息:')\n          console.log('- 附件对象:', attachment)\n          console.log('- 是否有ID:', !!attachment.id)\n          console.log('- 是否有URL:', !!attachment.url)\n          console.log('- 是否需要查找:', attachment.needsLookup)\n        }\n      } catch (error) {\n        console.error('❌ 下载附件失败:', error)\n        console.error('❌ 错误详情:', {\n          message: error.message,\n          status: error.response?.status,\n          data: error.response?.data\n        })\n\n        // 根据错误类型提供不同的提示\n        if (error.response?.status === 404) {\n          ElMessage.error('文件不存在或已被删除')\n        } else if (error.response?.status === 403) {\n          ElMessage.error('没有权限下载此文件')\n        } else if (error.response?.status === 401) {\n          ElMessage.error('请先登录')\n        } else {\n          ElMessage.error('下载附件失败: ' + (error.message || '未知错误'))\n        }\n      }\n    }\n\n    const downloadAllAttachments = async () => {\n      for (const attachment of selectedAttachments.value) {\n        await downloadAttachment(attachment)\n        // 添加延迟避免浏览器阻止多个下载\n        await new Promise(resolve => setTimeout(resolve, 500))\n      }\n    }\n\n    const isImageFile = (filename) => {\n      if (!filename) return false\n      const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg']\n      const ext = filename.toLowerCase().substring(filename.lastIndexOf('.'))\n      return imageExtensions.includes(ext)\n    }\n\n    const previewAttachment = async (attachment) => {\n      try {\n        if (attachment.id) {\n          // 使用文件API预览\n          const response = await fileAPI.previewFile(attachment.id)\n          const blob = new Blob([response])\n          previewImageUrl.value = window.URL.createObjectURL(blob)\n          currentPreviewAttachment.value = attachment\n          showImagePreview.value = true\n        } else if (attachment.previewUrl || attachment.url) {\n          previewImageUrl.value = attachment.previewUrl || attachment.url\n          currentPreviewAttachment.value = attachment\n          showImagePreview.value = true\n        } else {\n          ElMessage.warning('该图片暂无预览链接')\n        }\n      } catch (error) {\n        console.error('预览图片失败:', error)\n        ElMessage.error('预览图片失败')\n      }\n    }\n\n    const formatFileSize = (bytes) => {\n      if (!bytes) return '-'\n      const sizes = ['B', 'KB', 'MB', 'GB']\n      const i = Math.floor(Math.log(bytes) / Math.log(1024))\n      return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i]\n    }\n\n    // 生命周期\n    onMounted(() => {\n      loadMyProjects()\n      loadSubmissions()\n    })\n\n    return {\n      // 数据\n      loading,\n      submissions,\n      myProjects,\n      total,\n      currentPage,\n      pageSize,\n      filterStatus,\n      filterProject,\n      showDetailDialog,\n      selectedSubmission,\n      showAttachmentDialog,\n      selectedAttachments,\n      showImagePreview,\n      previewImageUrl,\n      currentPreviewAttachment,\n      currentUser,\n\n      // 方法\n      loadSubmissions,\n      viewSubmission,\n      approveSubmission,\n      rejectSubmission,\n      getStatusColor,\n      getStatusText,\n      getPriorityColor,\n      getPriorityText,\n      formatDate,\n\n      // 附件方法\n      hasAttachments,\n      getAttachmentCount,\n      parseAttachments,\n      viewAttachments,\n      downloadAttachment,\n      downloadAllAttachments,\n      isImageFile,\n      previewAttachment,\n      formatFileSize\n    }\n  }\n}\n</script>\n\n<style scoped>\n.task-review-container {\n  padding: 20px;\n}\n\n.page-card {\n  min-height: calc(100vh - 120px);\n}\n\n.card-header {\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n}\n\n.card-header h2 {\n  margin: 0;\n  color: #303133;\n  font-size: 24px;\n  font-weight: 600;\n}\n\n.subtitle {\n  margin: 0;\n  color: #909399;\n  font-size: 14px;\n}\n\n.filter-section {\n  margin-bottom: 20px;\n  padding: 20px;\n  background: #f8f9fa;\n  border-radius: 8px;\n}\n\n.submissions-list {\n  margin-bottom: 20px;\n}\n\n.submission-items {\n  display: flex;\n  flex-direction: column;\n  gap: 16px;\n}\n\n.submission-card {\n  transition: all 0.3s ease;\n}\n\n.submission-card:hover {\n  transform: translateY(-2px);\n}\n\n.submission-header {\n  display: flex;\n  justify-content: between;\n  align-items: flex-start;\n  margin-bottom: 12px;\n}\n\n.submission-info {\n  flex: 1;\n}\n\n.task-title {\n  margin: 0 0 8px 0;\n  color: #303133;\n  font-size: 16px;\n  font-weight: 600;\n}\n\n.submission-meta {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  font-size: 12px;\n  color: #909399;\n}\n\n.submission-actions {\n  display: flex;\n  gap: 8px;\n}\n\n.submission-content {\n  margin-bottom: 12px;\n}\n\n.content-preview {\n  margin: 0 0 8px 0;\n  color: #606266;\n  font-size: 14px;\n  line-height: 1.5;\n  display: -webkit-box;\n  -webkit-line-clamp: 2;\n  -webkit-box-orient: vertical;\n  overflow: hidden;\n}\n\n.attachments {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  color: #409eff;\n  font-size: 12px;\n}\n\n.view-attachments-btn {\n  padding: 0 4px;\n  font-size: 12px;\n  color: #409eff;\n}\n\n.view-attachments-btn:hover {\n  color: #66b1ff;\n}\n\n.parent-task {\n  padding: 12px;\n  background: #f8f9fa;\n  border-radius: 6px;\n  border-left: 3px solid #409eff;\n}\n\n.parent-task-header {\n  display: flex;\n  align-items: center;\n  gap: 6px;\n  margin-bottom: 8px;\n  font-weight: 600;\n  color: #303133;\n  font-size: 14px;\n}\n\n.parent-task-content {\n  margin: 0;\n  color: #606266;\n  font-size: 13px;\n  line-height: 1.4;\n  display: -webkit-box;\n  -webkit-line-clamp: 2;\n  -webkit-box-orient: vertical;\n  overflow: hidden;\n}\n\n.pagination {\n  display: flex;\n  justify-content: center;\n  margin-top: 20px;\n}\n\n.submission-detail {\n  max-height: 60vh;\n  overflow-y: auto;\n}\n\n.submission-content-detail {\n  max-height: 200px;\n  overflow-y: auto;\n  padding: 8px;\n  background: #f8f9fa;\n  border-radius: 4px;\n  white-space: pre-wrap;\n  line-height: 1.5;\n}\n\n.original-task-section {\n  margin-top: 20px;\n  padding-top: 20px;\n  border-top: 1px solid #ebeef5;\n}\n\n.original-task-section h4 {\n  margin: 0 0 12px 0;\n  color: #303133;\n  font-size: 16px;\n}\n\n.dialog-footer {\n  display: flex;\n  justify-content: flex-end;\n  gap: 12px;\n}\n\n.loading-state,\n.empty-state {\n  padding: 40px 20px;\n  text-align: center;\n}\n\n/* 附件相关样式 */\n.attachment-list {\n  display: flex;\n  flex-direction: column;\n  gap: 8px;\n}\n\n.attachment-item {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  padding: 8px 12px;\n  background: #f8f9fa;\n  border-radius: 6px;\n  border: 1px solid #e4e7ed;\n}\n\n.attachment-name {\n  flex: 1;\n  font-size: 14px;\n  color: #303133;\n}\n\n.download-btn {\n  padding: 0 8px;\n  font-size: 12px;\n}\n\n.attachment-dialog {\n  max-height: 400px;\n  overflow-y: auto;\n}\n\n.attachment-dialog-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 16px;\n  border: 1px solid #e4e7ed;\n  border-radius: 8px;\n  margin-bottom: 12px;\n  transition: all 0.3s ease;\n}\n\n.attachment-dialog-item:hover {\n  border-color: #409eff;\n  background: #f0f9ff;\n}\n\n.attachment-info {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  flex: 1;\n}\n\n.attachment-details {\n  flex: 1;\n}\n\n.attachment-name {\n  font-size: 14px;\n  font-weight: 500;\n  color: #303133;\n  margin-bottom: 4px;\n}\n\n.attachment-meta {\n  display: flex;\n  gap: 12px;\n  font-size: 12px;\n  color: #909399;\n}\n\n.file-size,\n.file-type {\n  padding: 2px 6px;\n  background: #f0f2f5;\n  border-radius: 4px;\n}\n\n.attachment-actions {\n  display: flex;\n  gap: 8px;\n}\n\n.no-attachments {\n  padding: 40px 20px;\n  text-align: center;\n}\n\n.image-preview {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  min-height: 300px;\n}\n\n.preview-image {\n  max-width: 100%;\n  max-height: 70vh;\n  object-fit: contain;\n  border-radius: 8px;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n}\n</style>\n"], "mappings": ";;;;;AA6SA,SAASA,GAAG,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,SAAQ,QAAS,KAAI;AACvD,SAASC,SAAS,EAAEC,YAAW,QAAS,cAAa;AACrD,SAASC,OAAO,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,IAAG,QAAS,yBAAwB;AACrF,SAASC,SAAS,EAAEC,UAAU,EAAEC,OAAM,QAAS,OAAM;AACrD,SAASC,QAAO,QAAS,MAAK;AAE9B,eAAe;EACbC,IAAI,EAAE,gBAAgB;EACtBC,UAAU,EAAE;IACVV,OAAO;IACPC,SAAS;IACTC,QAAQ;IACRC,QAAQ;IACRC;EACF,CAAC;EACDO,KAAKA,CAAA,EAAG;IACN,MAAMC,KAAI,GAAIJ,QAAQ,CAAC;IACvB,MAAMK,WAAU,GAAIjB,QAAQ,CAAC,MAAMgB,KAAK,CAACE,OAAO,CAACC,IAAI;;IAErD;IACA,MAAMC,OAAM,GAAItB,GAAG,CAAC,KAAK;IACzB,MAAMuB,WAAU,GAAIvB,GAAG,CAAC,EAAE;IAC1B,MAAMwB,UAAS,GAAIxB,GAAG,CAAC,EAAE;IACzB,MAAMyB,KAAI,GAAIzB,GAAG,CAAC,CAAC;IACnB,MAAM0B,WAAU,GAAI1B,GAAG,CAAC,CAAC;IACzB,MAAM2B,QAAO,GAAI3B,GAAG,CAAC,EAAE;;IAEvB;IACA,MAAM4B,YAAW,GAAI5B,GAAG,CAAC,WAAW,GAAE;IACtC,MAAM6B,aAAY,GAAI7B,GAAG,CAAC,EAAE;;IAE5B;IACA,MAAM8B,gBAAe,GAAI9B,GAAG,CAAC,KAAK;IAClC,MAAM+B,kBAAiB,GAAI/B,GAAG,CAAC,IAAI;;IAEnC;IACA,MAAMgC,oBAAmB,GAAIhC,GAAG,CAAC,KAAK;IACtC,MAAMiC,mBAAkB,GAAIjC,GAAG,CAAC,EAAE;IAClC,MAAMkC,gBAAe,GAAIlC,GAAG,CAAC,KAAK;IAClC,MAAMmC,eAAc,GAAInC,GAAG,CAAC,EAAE;IAC9B,MAAMoC,wBAAuB,GAAIpC,GAAG,CAAC,IAAI;;IAEzC;IACA,MAAMqC,cAAa,GAAI,MAAAA,CAAA,KAAY;MACjC,IAAI;QACF,MAAMC,QAAO,GAAI,MAAM1B,UAAU,CAAC2B,aAAa,CAAC;QAChDf,UAAU,CAACgB,KAAI,GAAIF,QAAQ,EAAEG,OAAM,IAAK,EAAC;MAC3C,EAAE,OAAOC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK;MAChC;IACF;;IAEA;IACA,MAAME,eAAc,GAAI,MAAAA,CAAA,KAAY;MAClC,IAAI;QACFtB,OAAO,CAACkB,KAAI,GAAI,IAAG;QACnB,MAAMK,MAAK,GAAI;UACbC,IAAI,EAAEpB,WAAW,CAACc,KAAK;UACvBO,IAAI,EAAEpB,QAAQ,CAACa,KAAK;UACpBQ,IAAI,EAAE,MAAM;UAAG;UACfC,MAAM,EAAE,YAAY;UAAG;UACvBC,OAAO,EAAE;QACX;QAEA,MAAMZ,QAAO,GAAI,MAAM3B,SAAS,CAACwC,UAAU,CAACN,MAAM;QAClD,IAAIO,cAAa,GAAId,QAAQ,EAAEG,OAAM,IAAK,EAAC;;QAE3C;QACAW,cAAa,GAAIA,cAAc,CAACC,MAAM,CAACC,IAAG,IACxC,CAAC,WAAW,EAAE,WAAW,EAAE,UAAU,CAAC,CAACC,QAAQ,CAACD,IAAI,CAACE,MAAM,CAC7D;;QAEA;QACA,IAAI5B,YAAY,CAACY,KAAK,EAAE;UACtBY,cAAa,GAAIA,cAAc,CAACC,MAAM,CAACI,UAAS,IAC9CA,UAAU,CAACD,MAAK,KAAM5B,YAAY,CAACY,KACrC;QACF;;QAEA;QACA,IAAIX,aAAa,CAACW,KAAK,EAAE;UACvBY,cAAa,GAAIA,cAAc,CAACC,MAAM,CAACI,UAAS,IAC9CA,UAAU,CAACC,SAAQ,KAAM7B,aAAa,CAACW,KACzC;QACF;QAEAjB,WAAW,CAACiB,KAAI,GAAIY,cAAa;QACjC3B,KAAK,CAACe,KAAI,GAAIY,cAAc,CAACO,MAAK;QAElChB,OAAO,CAACiB,GAAG,CAAC,UAAU,EAAER,cAAc,CAACS,GAAG,CAACP,IAAG,KAAM;UAClDQ,EAAE,EAAER,IAAI,CAACQ,EAAE;UACXC,KAAK,EAAET,IAAI,CAACS,KAAK;UACjBP,MAAM,EAAEF,IAAI,CAACE,MAAM;UACnBQ,UAAU,EAAEV,IAAI,CAACU;QACnB,CAAC,CAAC,CAAC;MACL,EAAE,OAAOtB,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK;QAChCtC,SAAS,CAACsC,KAAK,CAAC,UAAU;MAC5B,UAAU;QACRpB,OAAO,CAACkB,KAAI,GAAI,KAAI;MACtB;IACF;;IAEA;IACA,MAAMyB,eAAc,GAAI,MAAAA,CAAA,KAAY;MAClC,KAAK,MAAMR,UAAS,IAAKlC,WAAW,CAACiB,KAAK,EAAE;QAC1C;QACA,IAAIiB,UAAU,CAACS,QAAQ,EAAE;UACvB,IAAI;YACF,MAAMC,UAAS,GAAI,MAAMxD,SAAS,CAACyD,SAAS,CAACX,UAAU,CAACS,QAAQ;YAChET,UAAU,CAACU,UAAS,GAAIA,UAAS;UACnC,EAAE,OAAOzB,KAAK,EAAE;YACdC,OAAO,CAACD,KAAK,CAAC,UAAU,EAAEA,KAAK;UACjC;QACF;;QAEA;QACA,IAAIe,UAAU,CAACK,EAAE,EAAE;UACjB,IAAI;YACFnB,OAAO,CAACiB,GAAG,CAAC,aAAaH,UAAU,CAACK,EAAE,WAAW;YACjD,MAAMxB,QAAO,GAAI,MAAMzB,OAAO,CAACwD,cAAc,CAACZ,UAAU,CAACK,EAAE;YAC3DnB,OAAO,CAACiB,GAAG,CAAC,SAASH,UAAU,CAACK,EAAE,SAAS,EAAExB,QAAQ;YAErD,MAAMgC,eAAc,GAAIhC,QAAQ,EAAEiC,IAAG,IAAKjC,QAAO,IAAK,EAAC;YACvD,IAAIgC,eAAc,IAAKA,eAAe,CAACX,MAAK,GAAI,CAAC,EAAE;cACjD;cACAF,UAAU,CAACa,eAAc,GAAIA,eAAc;cAC3C;cACAb,UAAU,CAACe,WAAU,GAAI,GAAGF,eAAe,CAACX,MAAM,KAAI;cACtDhB,OAAO,CAACiB,GAAG,CAAC,QAAQH,UAAU,CAACK,EAAE,QAAQQ,eAAe,CAACX,MAAM,MAAM;;cAErE;cACAW,eAAe,CAACG,OAAO,CAAC,CAACC,IAAI,EAAEC,KAAK,KAAK;gBACvC,IAAI,CAACD,IAAI,CAACZ,EAAE,EAAE;kBACZnB,OAAO,CAACiC,IAAI,CAAC,SAASD,KAAK,QAAQ,EAAED,IAAI;gBAC3C;cACF,CAAC;YACH,OAAO;cACL/B,OAAO,CAACiB,GAAG,CAAC,SAASH,UAAU,CAACK,EAAE,OAAO;YAC3C;UACF,EAAE,OAAOpB,KAAK,EAAE;YACdC,OAAO,CAACD,KAAK,CAAC,UAAUe,UAAU,CAACK,EAAE,WAAW,EAAEpB,KAAK;YACvDC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAE;cACvBc,MAAM,EAAEd,KAAK,CAACJ,QAAQ,EAAEkB,MAAM;cAC9BqB,OAAO,EAAEnC,KAAK,CAACmC,OAAO;cACtBN,IAAI,EAAE7B,KAAK,CAACJ,QAAQ,EAAEiC;YACxB,CAAC;UACH;QACF;MACF;IACF;;IAEA;IACA,MAAMO,cAAa,GAAKrB,UAAU,IAAK;MACrC1B,kBAAkB,CAACS,KAAI,GAAIiB,UAAS;MACpC3B,gBAAgB,CAACU,KAAI,GAAI,IAAG;IAC9B;;IAEA;IACA,MAAMuC,iBAAgB,GAAI,MAAOtB,UAAU,IAAK;MAC9C,IAAI;QACF,MAAMpD,YAAY,CAAC2E,OAAO,CACxB,SAASvB,UAAU,CAACM,KAAK,QAAQ,EACjC,MAAM,EACN;UACEkB,iBAAiB,EAAE,IAAI;UACvBC,gBAAgB,EAAE,IAAI;UACtBlC,IAAI,EAAE;QACR,CACF;QAEA,MAAMrC,SAAS,CAACwE,gBAAgB,CAAC1B,UAAU,CAACK,EAAE,EAAE,WAAW;QAC3D1D,SAAS,CAACgF,OAAO,CAAC,MAAM;QACxBtD,gBAAgB,CAACU,KAAI,GAAI,KAAI;QAC7BI,eAAe,CAAC;MAClB,EAAE,OAAOF,KAAK,EAAE;QACd,IAAIA,KAAI,KAAM,QAAQ,EAAE;UACtBC,OAAO,CAACD,KAAK,CAAC,OAAO,EAAEA,KAAK;UAC5BtC,SAAS,CAACsC,KAAK,CAAC,MAAM;QACxB;MACF;IACF;;IAEA;IACA,MAAM2C,gBAAe,GAAI,MAAO5B,UAAU,IAAK;MAC7C,IAAI;QACF,MAAM;UAAEjB,KAAK,EAAE8C;QAAS,IAAI,MAAMjF,YAAY,CAACkF,MAAM,CACnD,UAAU,EACV,MAAM,EACN;UACEN,iBAAiB,EAAE,IAAI;UACvBC,gBAAgB,EAAE,IAAI;UACtBM,SAAS,EAAE,UAAU;UACrBC,gBAAgB,EAAE;QACpB,CACF;QAEA,MAAM9E,SAAS,CAACwE,gBAAgB,CAAC1B,UAAU,CAACK,EAAE,EAAE,UAAU;QAC1D1D,SAAS,CAACgF,OAAO,CAAC,OAAO;QACzBtD,gBAAgB,CAACU,KAAI,GAAI,KAAI;QAC7BI,eAAe,CAAC;MAClB,EAAE,OAAOF,KAAK,EAAE;QACd,IAAIA,KAAI,KAAM,QAAQ,EAAE;UACtBC,OAAO,CAACD,KAAK,CAAC,OAAO,EAAEA,KAAK;UAC5BtC,SAAS,CAACsC,KAAK,CAAC,MAAM;QACxB;MACF;IACF;;IAEA;IACA,MAAMgD,cAAa,GAAKlC,MAAM,IAAK;MACjC,MAAMmC,QAAO,GAAI;QACf,WAAW,EAAE,SAAS;QACtB,WAAW,EAAE,SAAS;QACtB,UAAU,EAAE;MACd;MACA,OAAOA,QAAQ,CAACnC,MAAM,KAAK,MAAK;IAClC;IAEA,MAAMoC,aAAY,GAAKpC,MAAM,IAAK;MAChC,MAAMqC,OAAM,GAAI;QACd,WAAW,EAAE,KAAK;QAClB,WAAW,EAAE,KAAK;QAClB,UAAU,EAAE;MACd;MACA,OAAOA,OAAO,CAACrC,MAAM,KAAKA,MAAK;IACjC;IAEA,MAAMsC,gBAAe,GAAKC,QAAQ,IAAK;MACrC,IAAI,OAAOA,QAAO,KAAM,QAAQ,EAAE;QAChC,MAAMC,YAAW,GAAI;UACnB,CAAC,EAAE,KAAK;UACR,CAAC,EAAE,QAAQ;UACX,CAAC,EAAE,MAAM;UACT,CAAC,EAAE,QAAQ;UACX,CAAC,EAAE;QACL;QACAD,QAAO,GAAIC,YAAY,CAACD,QAAQ,KAAK,QAAO;MAC9C;MAEA,MAAMJ,QAAO,GAAI;QACf,KAAK,EAAE,MAAM;QACb,QAAQ,EAAE,SAAS;QACnB,MAAM,EAAE,SAAS;QACjB,QAAQ,EAAE;MACZ;MACA,OAAOA,QAAQ,CAACI,QAAQ,KAAK,MAAK;IACpC;IAEA,MAAME,eAAc,GAAKF,QAAQ,IAAK;MACpC,IAAI,OAAOA,QAAO,KAAM,QAAQ,EAAE;QAChC,MAAMC,YAAW,GAAI;UACnB,CAAC,EAAE,KAAK;UACR,CAAC,EAAE,QAAQ;UACX,CAAC,EAAE,MAAM;UACT,CAAC,EAAE,QAAQ;UACX,CAAC,EAAE;QACL;QACAD,QAAO,GAAIC,YAAY,CAACD,QAAQ,KAAK,QAAO;MAC9C;MAEA,MAAMF,OAAM,GAAI;QACd,KAAK,EAAE,GAAG;QACV,QAAQ,EAAE,GAAG;QACb,MAAM,EAAE,GAAG;QACX,QAAQ,EAAE;MACZ;MACA,OAAOA,OAAO,CAACE,QAAQ,KAAK,GAAE;IAChC;;IAEA;IACA,MAAMG,UAAS,GAAKC,UAAU,IAAK;MACjC,IAAI,CAACA,UAAU,EAAE,OAAO,GAAE;MAC1B,MAAMC,IAAG,GAAI,IAAIC,IAAI,CAACF,UAAU;MAChC,OAAOC,IAAI,CAACE,cAAc,CAAC,OAAO;IACpC;;IAEA;IACA,MAAMC,cAAa,GAAK9C,UAAU,IAAK;MACrC;MACA,IAAIA,UAAU,CAACa,eAAc,IAAKb,UAAU,CAACa,eAAe,CAACX,MAAK,GAAI,CAAC,EAAE;QACvE,OAAO,IAAG;MACZ;;MAEA;MACA,MAAMa,WAAU,GAAIf,UAAU,CAACe,WAAU;MACzC,IAAI,CAACA,WAAU,IAAKA,WAAW,CAACgC,IAAI,CAAC,MAAM,EAAE,EAAE;QAC7C,OAAO,KAAI;MACb;MAEA,IAAI;QACF;QACA,IAAIhC,WAAW,CAACiC,UAAU,CAAC,GAAG,KAAKjC,WAAW,CAACiC,UAAU,CAAC,GAAG,CAAC,EAAE;UAC9D,MAAMC,MAAK,GAAIC,IAAI,CAACC,KAAK,CAACpC,WAAW;UACrC,IAAIqC,KAAK,CAACC,OAAO,CAACJ,MAAM,CAAC,EAAE;YACzB,OAAOA,MAAM,CAAC/C,MAAK,GAAI;UACzB;UACA,OAAO,IAAG;QACZ;QACA;QACA,MAAMoD,SAAQ,GAAIvC,WAAW,CAACwC,KAAK,CAAC,GAAG,CAAC,CAAC3D,MAAM,CAACtC,IAAG,IAAKA,IAAI,CAACyF,IAAI,CAAC,CAAC;QACnE,OAAOO,SAAS,CAACpD,MAAK,GAAI;MAC5B,EAAE,OAAOjB,KAAK,EAAE;QACd;QACA,MAAMqE,SAAQ,GAAIvC,WAAW,CAACwC,KAAK,CAAC,GAAG,CAAC,CAAC3D,MAAM,CAACtC,IAAG,IAAKA,IAAI,CAACyF,IAAI,CAAC,CAAC;QACnE,OAAOO,SAAS,CAACpD,MAAK,GAAI;MAC5B;IACF;IAEA,MAAMsD,kBAAiB,GAAKxD,UAAU,IAAK;MACzC;MACA,IAAIA,UAAU,CAACa,eAAc,IAAKb,UAAU,CAACa,eAAe,CAACX,MAAK,GAAI,CAAC,EAAE;QACvE,OAAOF,UAAU,CAACa,eAAe,CAACX,MAAK;MACzC;;MAEA;MACA,MAAMa,WAAU,GAAIf,UAAU,CAACe,WAAU;MACzC,IAAI,CAACA,WAAW,EAAE,OAAO;MAEzB,IAAI;QACF;QACA,IAAIA,WAAW,CAACiC,UAAU,CAAC,GAAG,KAAKjC,WAAW,CAACiC,UAAU,CAAC,GAAG,CAAC,EAAE;UAC9D,MAAMC,MAAK,GAAIC,IAAI,CAACC,KAAK,CAACpC,WAAW;UACrC,IAAIqC,KAAK,CAACC,OAAO,CAACJ,MAAM,CAAC,EAAE;YACzB,OAAOA,MAAM,CAAC/C,MAAK;UACrB;UACA,OAAO;QACT;QACA;QACA,OAAOa,WAAW,CAACwC,KAAK,CAAC,GAAG,CAAC,CAAC3D,MAAM,CAACtC,IAAG,IAAKA,IAAI,CAACyF,IAAI,CAAC,CAAC,CAAC,CAAC7C,MAAK;MACjE,EAAE,OAAOjB,KAAK,EAAE;QACd;QACA,OAAO8B,WAAW,CAACwC,KAAK,CAAC,GAAG,CAAC,CAAC3D,MAAM,CAACtC,IAAG,IAAKA,IAAI,CAACyF,IAAI,CAAC,CAAC,CAAC,CAAC7C,MAAK;MACjE;IACF;IAEA,MAAMuD,gBAAe,GAAK1C,WAAW,IAAK;MACxC,IAAI,CAACA,WAAW,EAAE,OAAO,EAAC;MAE1B,IAAI;QACF;QACA,IAAIA,WAAW,CAACiC,UAAU,CAAC,GAAG,KAAKjC,WAAW,CAACiC,UAAU,CAAC,GAAG,CAAC,EAAE;UAC9D,MAAMC,MAAK,GAAIC,IAAI,CAACC,KAAK,CAACpC,WAAW;UACrC,IAAIqC,KAAK,CAACC,OAAO,CAACJ,MAAM,CAAC,EAAE;YACzB,OAAOA,MAAM,CAAC7C,GAAG,CAACsD,IAAG,KAAM;cACzBpG,IAAI,EAAEoG,IAAI,CAACpG,IAAG,IAAKoG,IAAI,CAACC,QAAO,IAAKD,IAAI;cACxCrD,EAAE,EAAEqD,IAAI,CAACrD,EAAE;cACXuD,GAAG,EAAEF,IAAI,CAACE,GAAE,IAAKF,IAAI,CAACG,IAAI;cAC1BvE,IAAI,EAAEoE,IAAI,CAACpE,IAAI;cACfC,IAAI,EAAEmE,IAAI,CAACnE,IAAG,IAAKmE,IAAI,CAACI;YAC1B,CAAC,CAAC;UACJ;UACA,OAAO,CAAC;YACNxG,IAAI,EAAE2F,MAAM,CAAC3F,IAAG,IAAK2F,MAAM,CAACU,QAAO,IAAK,MAAM;YAC9CtD,EAAE,EAAE4C,MAAM,CAAC5C,EAAE;YACbuD,GAAG,EAAEX,MAAM,CAACW,GAAE,IAAKX,MAAM,CAACY,IAAI;YAC9BvE,IAAI,EAAE2D,MAAM,CAAC3D,IAAI;YACjBC,IAAI,EAAE0D,MAAM,CAAC1D,IAAG,IAAK0D,MAAM,CAACa;UAC9B,CAAC;QACH;;QAEA;QACA,OAAO/C,WAAW,CAACwC,KAAK,CAAC,GAAG,EACzB3D,MAAM,CAACtC,IAAG,IAAKA,IAAI,CAACyF,IAAI,CAAC,CAAC,EAC1B3C,GAAG,CAAC9C,IAAG,KAAM;UACZA,IAAI,EAAEA,IAAI,CAACyF,IAAI,CAAC,CAAC;UACjB1C,EAAE,EAAE,IAAI;UAAG;UACXuD,GAAG,EAAE,IAAI;UACTtE,IAAI,EAAE,IAAI;UACVC,IAAI,EAAE,IAAI;UACVwE,WAAW,EAAE,IAAG,CAAG;QACrB,CAAC,CAAC;MACN,EAAE,OAAO9E,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK;QAChC,OAAO,CAAC;UACN3B,IAAI,EAAE,QAAQ;UACdsG,GAAG,EAAE,IAAI;UACTtE,IAAI,EAAE,IAAI;UACVC,IAAI,EAAE;QACR,CAAC;MACH;IACF;IAEA,MAAMyE,eAAc,GAAI,MAAOhE,UAAU,IAAK;MAC5Cd,OAAO,CAACiB,GAAG,CAAC,iBAAiB,EAAEH,UAAU;;MAEzC;MACA,IAAIA,UAAU,CAACa,eAAc,IAAKb,UAAU,CAACa,eAAe,CAACX,MAAK,GAAI,CAAC,EAAE;QACvEhB,OAAO,CAACiB,GAAG,CAAC,iBAAiB,EAAEH,UAAU,CAACa,eAAe;QACzDrC,mBAAmB,CAACO,KAAI,GAAIiB,UAAU,CAACa,eAAe,CAACT,GAAG,CAACa,IAAG,KAAM;UAClEZ,EAAE,EAAEY,IAAI,CAACZ,EAAE;UACX/C,IAAI,EAAE2D,IAAI,CAACgD,YAAW,IAAKhD,IAAI,CAACiD,QAAO,IAAKjD,IAAI,CAAC3D,IAAI;UACrDsG,GAAG,EAAE,cAAc3C,IAAI,CAACZ,EAAE,WAAW;UACrC8D,UAAU,EAAE,cAAclD,IAAI,CAACZ,EAAE,UAAU;UAC3Cf,IAAI,EAAE2B,IAAI,CAACmD,QAAO,IAAKnD,IAAI,CAAC3B,IAAI;UAChCC,IAAI,EAAE0B,IAAI,CAACoD,QAAO,IAAKpD,IAAI,CAAC6C,WAAU,IAAK7C,IAAI,CAAC1B,IAAI;UACpD+E,UAAU,EAAErD,IAAI,CAACqD,UAAS,IAAKrD,IAAI,CAACsD;QACtC,CAAC,CAAC;MACJ,OAAO;QACLrF,OAAO,CAACiB,GAAG,CAAC,kBAAkB;;QAE9B;QACA,IAAI;UACFjB,OAAO,CAACiB,GAAG,CAAC,gCAAgCH,UAAU,CAACK,EAAE,GAAG;UAC5D,MAAMxB,QAAO,GAAI,MAAMzB,OAAO,CAACwD,cAAc,CAACZ,UAAU,CAACK,EAAE;UAC3DnB,OAAO,CAACiB,GAAG,CAAC,WAAW,EAAEtB,QAAQ;UAEjC,MAAM2F,KAAI,GAAI3F,QAAQ,EAAEiC,IAAG,IAAKjC,QAAO,IAAK,EAAC;UAC7C,IAAI2F,KAAI,IAAKA,KAAK,CAACtE,MAAK,GAAI,CAAC,EAAE;YAC7BhB,OAAO,CAACiB,GAAG,CAAC,aAAa,EAAEqE,KAAK;YAChChG,mBAAmB,CAACO,KAAI,GAAIyF,KAAK,CAACpE,GAAG,CAACa,IAAG,KAAM;cAC7CZ,EAAE,EAAEY,IAAI,CAACZ,EAAE;cACX/C,IAAI,EAAE2D,IAAI,CAACgD,YAAW,IAAKhD,IAAI,CAACiD,QAAO,IAAKjD,IAAI,CAAC3D,IAAI;cACrDsG,GAAG,EAAE,cAAc3C,IAAI,CAACZ,EAAE,WAAW;cACrC8D,UAAU,EAAE,cAAclD,IAAI,CAACZ,EAAE,UAAU;cAC3Cf,IAAI,EAAE2B,IAAI,CAACmD,QAAO,IAAKnD,IAAI,CAAC3B,IAAI;cAChCC,IAAI,EAAE0B,IAAI,CAACoD,QAAO,IAAKpD,IAAI,CAAC6C,WAAU,IAAK7C,IAAI,CAAC1B,IAAI;cACpD+E,UAAU,EAAErD,IAAI,CAACqD,UAAS,IAAKrD,IAAI,CAACsD;YACtC,CAAC,CAAC;UACJ,OAAO;YACLrF,OAAO,CAACiB,GAAG,CAAC,4BAA4B;YACxC;YACA,MAAMsE,iBAAgB,GAAIhB,gBAAgB,CAACzD,UAAU,CAACe,WAAW;YACjE7B,OAAO,CAACiB,GAAG,CAAC,aAAa,EAAEsE,iBAAiB;;YAE5C;YACA,MAAMC,kBAAiB,GAAI,MAAMC,OAAO,CAACC,GAAG,CAC1CH,iBAAiB,CAACrE,GAAG,CAAC,MAAOyE,UAAU,IAAK;cAC1C,IAAIA,UAAU,CAACd,WAAW,EAAE;gBAC1B,IAAI;kBACF7E,OAAO,CAACiB,GAAG,CAAC,YAAY0E,UAAU,CAACvH,IAAI,EAAE;kBACzC;kBACA,MAAMkH,KAAI,GAAI,MAAMpH,OAAO,CAAC0H,gBAAgB,CAAC9E,UAAU,CAACK,EAAE;kBAC1DnB,OAAO,CAACiB,GAAG,CAAC,UAAU,EAAEqE,KAAK;kBAE7B,MAAMO,QAAO,GAAIP,KAAK,EAAE1D,IAAG,IAAK0D,KAAI,IAAK,EAAC;kBAC1C,MAAMQ,WAAU,GAAID,QAAQ,CAACE,IAAI,CAAChE,IAAG,IACnCA,IAAI,CAACgD,YAAW,KAAMY,UAAU,CAACvH,IAAG,IACpC2D,IAAI,CAACiD,QAAO,KAAMW,UAAU,CAACvH,IAAG,IAChC2D,IAAI,CAAC3D,IAAG,KAAMuH,UAAU,CAACvH,IAC3B;kBAEA,IAAI0H,WAAW,EAAE;oBACf9F,OAAO,CAACiB,GAAG,CAAC,WAAW,EAAE6E,WAAW;oBACpC,OAAO;sBACL3E,EAAE,EAAE2E,WAAW,CAAC3E,EAAE;sBAClB/C,IAAI,EAAE0H,WAAW,CAACf,YAAW,IAAKe,WAAW,CAACd,QAAO,IAAKc,WAAW,CAAC1H,IAAI;sBAC1EsG,GAAG,EAAE,cAAcoB,WAAW,CAAC3E,EAAE,WAAW;sBAC5C8D,UAAU,EAAE,cAAca,WAAW,CAAC3E,EAAE,UAAU;sBAClDf,IAAI,EAAE0F,WAAW,CAACZ,QAAO,IAAKY,WAAW,CAAC1F,IAAI;sBAC9CC,IAAI,EAAEyF,WAAW,CAACX,QAAO,IAAKW,WAAW,CAAClB,WAAU,IAAKkB,WAAW,CAACzF,IAAI;sBACzE+E,UAAU,EAAEU,WAAW,CAACV,UAAS,IAAKU,WAAW,CAACT;oBACpD;kBACF,OAAO;oBACLrF,OAAO,CAACiB,GAAG,CAAC,YAAY,EAAE0E,UAAU,CAACvH,IAAI;kBAC3C;gBACF,EAAE,OAAO2B,KAAK,EAAE;kBACdC,OAAO,CAACD,KAAK,CAAC,aAAa,EAAEA,KAAK;gBACpC;cACF;cACA,OAAO4F,UAAS;YAClB,CAAC,CACH;YAEArG,mBAAmB,CAACO,KAAI,GAAI2F,kBAAiB;UAC/C;QACF,EAAE,OAAOzF,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,aAAa,EAAEA,KAAK;UAClCtC,SAAS,CAACsC,KAAK,CAAC,YAAW,IAAKA,KAAK,CAACmC,OAAM,IAAK,MAAM,CAAC;UACxD5C,mBAAmB,CAACO,KAAI,GAAI,EAAC;QAC/B;MACF;MAEAG,OAAO,CAACiB,GAAG,CAAC,YAAY,EAAE3B,mBAAmB,CAACO,KAAK;MACnDR,oBAAoB,CAACQ,KAAI,GAAI,IAAG;IAClC;IAEA,MAAMmG,kBAAiB,GAAI,MAAOL,UAAU,IAAK;MAC/C3F,OAAO,CAACiB,GAAG,CAAC,YAAY,EAAE0E,UAAU;MAEpC,IAAI;QACF,IAAIA,UAAU,CAACxE,EAAE,EAAE;UACjBnB,OAAO,CAACiB,GAAG,CAAC,gBAAgB0E,UAAU,CAACxE,EAAE,EAAE;UAC3C;UACA,MAAMxB,QAAO,GAAI,MAAMzB,OAAO,CAAC+H,YAAY,CAACN,UAAU,CAACxE,EAAE;UAEzDnB,OAAO,CAACiB,GAAG,CAAC,UAAU,EAAEtB,QAAQ;UAChCK,OAAO,CAACiB,GAAG,CAAC,UAAU,EAAE,OAAOtB,QAAQ;UACvCK,OAAO,CAACiB,GAAG,CAAC,aAAa,EAAEtB,QAAO,YAAauG,IAAI;;UAEnD;UACA,IAAIC,IAAG;UACP,IAAIxG,QAAQ,CAACiC,IAAG,IAAKjC,QAAQ,CAACiC,IAAG,YAAasE,IAAI,EAAE;YAClD;YACAC,IAAG,GAAIxG,QAAQ,CAACiC,IAAG;UACrB,OAAO,IAAIjC,QAAO,YAAauG,IAAI,EAAE;YACnC;YACAC,IAAG,GAAIxG,QAAO;UAChB,OAAO;YACL;YACAK,OAAO,CAACD,KAAK,CAAC,eAAe,EAAEJ,QAAQ;YACvClC,SAAS,CAACsC,KAAK,CAAC,eAAe;YAC/B;UACF;;UAEA;UACA,MAAM2E,GAAE,GAAI0B,MAAM,CAACC,GAAG,CAACC,eAAe,CAACH,IAAI;UAC3C,MAAMI,IAAG,GAAIC,QAAQ,CAACC,aAAa,CAAC,GAAG;UACvCF,IAAI,CAACG,IAAG,GAAIhC,GAAE;UACd6B,IAAI,CAACI,QAAO,GAAIhB,UAAU,CAACvH,IAAG;UAC9BoI,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,IAAI;UAC9BA,IAAI,CAACO,KAAK,CAAC;UACXN,QAAQ,CAACI,IAAI,CAACG,WAAW,CAACR,IAAI;UAC9BH,MAAM,CAACC,GAAG,CAACW,eAAe,CAACtC,GAAG;UAE9B1E,OAAO,CAACiB,GAAG,CAAC,UAAU;UACtBxD,SAAS,CAACgF,OAAO,CAAC,QAAQ;QAC5B,OAAO,IAAIkD,UAAU,CAACjB,GAAG,EAAE;UACzB1E,OAAO,CAACiB,GAAG,CAAC,eAAe0E,UAAU,CAACjB,GAAG,EAAE;UAC3C;UACA,MAAM6B,IAAG,GAAIC,QAAQ,CAACC,aAAa,CAAC,GAAG;UACvCF,IAAI,CAACG,IAAG,GAAIf,UAAU,CAACjB,GAAE;UACzB6B,IAAI,CAACI,QAAO,GAAIhB,UAAU,CAACvH,IAAG;UAC9BmI,IAAI,CAACU,MAAK,GAAI,QAAO,EAAG;UACxBT,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,IAAI;UAC9BA,IAAI,CAACO,KAAK,CAAC;UACXN,QAAQ,CAACI,IAAI,CAACG,WAAW,CAACR,IAAI;UAE9BvG,OAAO,CAACiB,GAAG,CAAC,aAAa;UACzBxD,SAAS,CAACgF,OAAO,CAAC,SAAS;QAC7B,OAAO;UACLzC,OAAO,CAACiB,GAAG,CAAC,iBAAiB,EAAE0E,UAAU;UACzC;UACAlI,SAAS,CAACyJ,OAAO,CAAC,MAAMvB,UAAU,CAACvH,IAAI,wBAAwB;;UAE/D;UACA4B,OAAO,CAACiB,GAAG,CAAC,UAAU;UACtBjB,OAAO,CAACiB,GAAG,CAAC,SAAS,EAAE0E,UAAU;UACjC3F,OAAO,CAACiB,GAAG,CAAC,UAAU,EAAE,CAAC,CAAC0E,UAAU,CAACxE,EAAE;UACvCnB,OAAO,CAACiB,GAAG,CAAC,WAAW,EAAE,CAAC,CAAC0E,UAAU,CAACjB,GAAG;UACzC1E,OAAO,CAACiB,GAAG,CAAC,WAAW,EAAE0E,UAAU,CAACd,WAAW;QACjD;MACF,EAAE,OAAO9E,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK;QAChCC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAE;UACvBmC,OAAO,EAAEnC,KAAK,CAACmC,OAAO;UACtBrB,MAAM,EAAEd,KAAK,CAACJ,QAAQ,EAAEkB,MAAM;UAC9Be,IAAI,EAAE7B,KAAK,CAACJ,QAAQ,EAAEiC;QACxB,CAAC;;QAED;QACA,IAAI7B,KAAK,CAACJ,QAAQ,EAAEkB,MAAK,KAAM,GAAG,EAAE;UAClCpD,SAAS,CAACsC,KAAK,CAAC,YAAY;QAC9B,OAAO,IAAIA,KAAK,CAACJ,QAAQ,EAAEkB,MAAK,KAAM,GAAG,EAAE;UACzCpD,SAAS,CAACsC,KAAK,CAAC,WAAW;QAC7B,OAAO,IAAIA,KAAK,CAACJ,QAAQ,EAAEkB,MAAK,KAAM,GAAG,EAAE;UACzCpD,SAAS,CAACsC,KAAK,CAAC,MAAM;QACxB,OAAO;UACLtC,SAAS,CAACsC,KAAK,CAAC,UAAS,IAAKA,KAAK,CAACmC,OAAM,IAAK,MAAM,CAAC;QACxD;MACF;IACF;IAEA,MAAMiF,sBAAqB,GAAI,MAAAA,CAAA,KAAY;MACzC,KAAK,MAAMxB,UAAS,IAAKrG,mBAAmB,CAACO,KAAK,EAAE;QAClD,MAAMmG,kBAAkB,CAACL,UAAU;QACnC;QACA,MAAM,IAAIF,OAAO,CAAC2B,OAAM,IAAKC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC;MACvD;IACF;IAEA,MAAME,WAAU,GAAK7C,QAAQ,IAAK;MAChC,IAAI,CAACA,QAAQ,EAAE,OAAO,KAAI;MAC1B,MAAM8C,eAAc,GAAI,CAAC,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM;MACjF,MAAMC,GAAE,GAAI/C,QAAQ,CAACgD,WAAW,CAAC,CAAC,CAACC,SAAS,CAACjD,QAAQ,CAACkD,WAAW,CAAC,GAAG,CAAC;MACtE,OAAOJ,eAAe,CAAC3G,QAAQ,CAAC4G,GAAG;IACrC;IAEA,MAAMI,iBAAgB,GAAI,MAAOjC,UAAU,IAAK;MAC9C,IAAI;QACF,IAAIA,UAAU,CAACxE,EAAE,EAAE;UACjB;UACA,MAAMxB,QAAO,GAAI,MAAMzB,OAAO,CAAC2J,WAAW,CAAClC,UAAU,CAACxE,EAAE;UACxD,MAAMgF,IAAG,GAAI,IAAID,IAAI,CAAC,CAACvG,QAAQ,CAAC;UAChCH,eAAe,CAACK,KAAI,GAAIuG,MAAM,CAACC,GAAG,CAACC,eAAe,CAACH,IAAI;UACvD1G,wBAAwB,CAACI,KAAI,GAAI8F,UAAS;UAC1CpG,gBAAgB,CAACM,KAAI,GAAI,IAAG;QAC9B,OAAO,IAAI8F,UAAU,CAACV,UAAS,IAAKU,UAAU,CAACjB,GAAG,EAAE;UAClDlF,eAAe,CAACK,KAAI,GAAI8F,UAAU,CAACV,UAAS,IAAKU,UAAU,CAACjB,GAAE;UAC9DjF,wBAAwB,CAACI,KAAI,GAAI8F,UAAS;UAC1CpG,gBAAgB,CAACM,KAAI,GAAI,IAAG;QAC9B,OAAO;UACLpC,SAAS,CAACyJ,OAAO,CAAC,WAAW;QAC/B;MACF,EAAE,OAAOnH,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK;QAC9BtC,SAAS,CAACsC,KAAK,CAAC,QAAQ;MAC1B;IACF;IAEA,MAAM+H,cAAa,GAAKC,KAAK,IAAK;MAChC,IAAI,CAACA,KAAK,EAAE,OAAO,GAAE;MACrB,MAAMC,KAAI,GAAI,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI;MACpC,MAAMC,CAAA,GAAIC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACjH,GAAG,CAAC8G,KAAK,IAAIG,IAAI,CAACjH,GAAG,CAAC,IAAI,CAAC;MACrD,OAAOiH,IAAI,CAACE,KAAK,CAACL,KAAI,GAAIG,IAAI,CAACG,GAAG,CAAC,IAAI,EAAEJ,CAAC,IAAI,GAAG,IAAI,GAAE,GAAI,GAAE,GAAID,KAAK,CAACC,CAAC;IAC1E;;IAEA;IACAzK,SAAS,CAAC,MAAM;MACdkC,cAAc,CAAC;MACfO,eAAe,CAAC;IAClB,CAAC;IAED,OAAO;MACL;MACAtB,OAAO;MACPC,WAAW;MACXC,UAAU;MACVC,KAAK;MACLC,WAAW;MACXC,QAAQ;MACRC,YAAY;MACZC,aAAa;MACbC,gBAAgB;MAChBC,kBAAkB;MAClBC,oBAAoB;MACpBC,mBAAmB;MACnBC,gBAAgB;MAChBC,eAAe;MACfC,wBAAwB;MACxBjB,WAAW;MAEX;MACAyB,eAAe;MACfkC,cAAc;MACdC,iBAAiB;MACjBM,gBAAgB;MAChBK,cAAc;MACdE,aAAa;MACbE,gBAAgB;MAChBG,eAAe;MACfC,UAAU;MAEV;MACAK,cAAc;MACdU,kBAAkB;MAClBC,gBAAgB;MAChBO,eAAe;MACfkB,kBAAkB;MAClBmB,sBAAsB;MACtBG,WAAW;MACXM,iBAAiB;MACjBE;IACF;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}