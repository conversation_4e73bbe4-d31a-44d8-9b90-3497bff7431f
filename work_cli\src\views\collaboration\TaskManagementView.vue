<template>
  <div class="task-management">
    <el-card>
      <template #header>
        <div class="card-header">
          <h3>任务管理</h3>
          <div class="header-actions">
            <el-select v-model="currentTeamId" placeholder="选择团队" @change="loadTasks" style="width: 200px;">
              <el-option
                v-for="team in myTeams"
                :key="team.id"
                :label="team.projectName ? `${team.name} (${team.projectName})` : team.name"
                :value="team.id"
              />
            </el-select>
            <el-button v-if="isTeacher" type="primary" @click="goToTaskPublish" :icon="Plus">
              发布任务
            </el-button>
            <el-button @click="loadTasks" :loading="loading" :icon="Refresh">
              刷新
            </el-button>
          </div>
        </div>
      </template>
      
      <!-- 任务统计 -->
      <div class="task-stats">
        <el-row :gutter="16">
          <el-col :span="6">
            <el-statistic title="总任务数" :value="taskStats.total" />
          </el-col>
          <el-col :span="6">
            <el-statistic title="进行中" :value="taskStats.inProgress" />
          </el-col>
          <el-col :span="6">
            <el-statistic title="已完成" :value="taskStats.completed" />
          </el-col>
          <el-col :span="6">
            <el-statistic title="完成率" :value="taskStats.completionRate" suffix="%" />
          </el-col>
        </el-row>
      </div>
      


      <!-- 任务列表 -->
      <div class="task-list" v-loading="loading">
        <el-tabs v-model="activeTab" @tab-change="loadTasks">
          <el-tab-pane label="全部任务" name="all" />
          <el-tab-pane label="已发布" name="PUBLISHED" />
          <el-tab-pane label="活跃任务" name="ACTIVE" />
          <el-tab-pane label="进行中" name="IN_PROGRESS" />
          <el-tab-pane label="待审核" name="SUBMITTED" />
          <el-tab-pane label="已完成" name="COMPLETED" />
          <el-tab-pane label="已取消" name="CANCELLED" />
        </el-tabs>

        <div v-if="tasks.length === 0 && !loading" class="empty-state">
          <el-empty description="暂无任务">
            <el-button v-if="isTeacher" type="primary" @click="goToTaskPublish">
              发布第一个任务
            </el-button>
            <template v-else>
              <p style="color: #909399; font-size: 14px; margin-top: 16px;">
                等待教师发布任务
              </p>
            </template>
          </el-empty>
        </div>

        <div v-else class="task-grid">
          <div v-for="task in tasks" :key="task.id" class="task-card">
            <el-card shadow="hover" @click="viewTask(task)">
              <h4>{{ task.title }}</h4>
              <p class="task-description">{{ task.content || task.description || '暂无描述' }}</p>
              <div class="task-meta">
                <el-tag :type="getStatusColor(task.status)" size="small">
                  {{ getStatusText(task.status) }}
                </el-tag>
                <el-tag v-if="task.priority" :type="getPriorityColor(task.priority)" size="small">
                  {{ getPriorityText(task.priority) }}
                </el-tag>
              </div>
              <div class="task-info">
                <div class="info-item" v-if="task.deadline">
                  <el-icon><Calendar /></el-icon>
                  <span>{{ formatDate(task.deadline) }}</span>
                </div>
                <div class="info-item" v-if="task.assignee">
                  <el-icon><User /></el-icon>
                  <span>{{ task.assignee }}</span>
                </div>
              </div>
              <div class="task-footer">
                <el-button size="small" @click.stop="viewTask(task)">
                  查看详情
                </el-button>

                <!-- 学生操作 -->
                <template v-if="!isTeacher">
                  <el-button
                    v-if="task.status === 'PUBLISHED'"
                    size="small"
                    type="primary"
                    @click.stop="startTask(task)"
                  >
                    开始任务
                  </el-button>
                  <el-button
                    v-if="task.status === 'ACTIVE'"
                    size="small"
                    type="success"
                    @click.stop="showSubmitDialog(task)"
                  >
                    提交任务
                  </el-button>
                  <el-button
                    v-if="task.status === 'IN_PROGRESS'"
                    size="small"
                    type="warning"
                    @click.stop="showSubmitDialog(task)"
                  >
                    重新提交
                  </el-button>
                  <el-tag
                    v-if="task.status === 'SUBMITTED'"
                    size="small"
                    type="warning"
                  >
                    等待审核
                  </el-tag>
                  <el-tag
                    v-if="task.status === 'COMPLETED'"
                    size="small"
                    type="success"
                  >
                    已完成
                  </el-tag>
                </template>

                <!-- 教师操作 -->
                <template v-if="isTeacher">
                  <el-button v-if="canEdit(task)" size="small" @click.stop="editTask(task)">
                    编辑
                  </el-button>
                  <el-dropdown v-if="canManage(task)" @command="handleTaskAction" @click.stop>
                    <el-button size="small" :icon="ArrowDown">
                      更多
                    </el-button>
                    <template #dropdown>
                      <el-dropdown-menu>
                        <el-dropdown-item
                          v-if="task.status === 'SUBMITTED'"
                          :command="{action: 'approve', task}"
                        >
                          通过审核
                        </el-dropdown-item>
                        <el-dropdown-item
                          v-if="task.status === 'SUBMITTED'"
                          :command="{action: 'reject', task}"
                        >
                          拒绝任务
                        </el-dropdown-item>
                        <el-dropdown-item
                          v-if="task.status === 'PUBLISHED'"
                          :command="{action: 'activate', task}"
                        >
                          激活任务
                        </el-dropdown-item>
                        <el-dropdown-item
                          v-if="['PUBLISHED', 'ACTIVE', 'IN_PROGRESS'].includes(task.status)"
                          :command="{action: 'cancel', task}"
                        >
                          取消任务
                        </el-dropdown-item>
                        <el-dropdown-item
                          :command="{action: 'delete', task}"
                          divided
                        >
                          删除任务
                        </el-dropdown-item>
                      </el-dropdown-menu>
                    </template>
                  </el-dropdown>
                </template>
              </div>
            </el-card>
          </div>
        </div>
      </div>

      
      <!-- 分页 -->
      <div v-if="total > 0" class="pagination">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :total="total"
          :page-sizes="[10, 20, 50]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="loadTasks"
          @current-change="loadTasks"
        />
      </div>
    </el-card>
    
    <!-- 创建/编辑任务对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      :title="editingTask ? '编辑任务' : '创建任务'"
      width="600px"
    >
      <el-form
        ref="formRef"
        :model="taskForm"
        :rules="formRules"
        label-width="80px"
      >
        <el-form-item label="任务标题" prop="title">
          <el-input v-model="taskForm.title" placeholder="请输入任务标题" />
        </el-form-item>
        
        <el-form-item label="任务描述" prop="description">
          <el-input
            v-model="taskForm.description"
            type="textarea"
            :rows="4"
            placeholder="请输入任务描述"
          />
        </el-form-item>
        
        <el-form-item label="负责人">
          <el-input :value="currentUser?.realName || '当前用户'" disabled />
          <div class="form-help-text">任务创建者即为负责人</div>
        </el-form-item>
        
        <el-form-item label="优先级" prop="priority">
          <el-select v-model="taskForm.priority" placeholder="请选择优先级">
            <el-option label="低" value="LOW" />
            <el-option label="中" value="MEDIUM" />
            <el-option label="高" value="HIGH" />
            <el-option label="紧急" value="URGENT" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="截止时间" prop="deadline">
          <el-date-picker
            v-model="taskForm.deadline"
            type="datetime"
            placeholder="请选择截止时间"
            style="width: 100%"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button type="primary" @click="submitTask" :loading="submitting">
          {{ editingTask ? '更新' : '创建' }}
        </el-button>
      </template>
    </el-dialog>
    
    <!-- 任务详情对话框 -->
    <el-dialog v-model="showDetailDialog" title="任务详情" width="700px">
      <div v-if="selectedTask" class="task-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="任务标题" :span="2">{{ selectedTask.title }}</el-descriptions-item>
          <el-descriptions-item label="任务状态">
            <el-tag :type="getStatusColor(selectedTask.status)">
              {{ getStatusText(selectedTask.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="优先级">
            <el-tag :type="getPriorityColor(selectedTask.priority)">
              {{ getPriorityText(selectedTask.priority) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="创建人">{{ selectedTask.creator?.realName }}</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ formatDate(selectedTask.createTime) }}</el-descriptions-item>
          <el-descriptions-item label="截止时间">{{ formatDate(selectedTask.deadline) }}</el-descriptions-item>
          <el-descriptions-item label="任务描述" :span="2">{{ selectedTask.content || selectedTask.description || '暂无描述' }}</el-descriptions-item>
        </el-descriptions>
        
        <!-- 进度更新 -->
        <div v-if="selectedTask.status === 'IN_PROGRESS'" class="progress-update">
          <h4>更新进度</h4>
          <el-slider
            v-model="progressValue"
            :max="100"
            :step="5"
            show-stops
            show-input
            @change="updateProgress"
          />
        </div>
      </div>
      
      <template #footer>
        <el-button @click="showDetailDialog = false">关闭</el-button>
      </template>
    </el-dialog>

    <!-- 任务提交对话框 -->
    <el-dialog
      v-model="showSubmitTaskDialog"
      title="提交任务"
      width="600px"
    >
      <el-form
        ref="submitFormRef"
        :model="submitForm"
        :rules="submitFormRules"
        label-width="100px"
      >
        <el-form-item label="任务名称">
          <span>{{ selectedTask?.title }}</span>
        </el-form-item>

        <el-form-item label="提交说明" prop="content">
          <el-input
            v-model="submitForm.content"
            type="textarea"
            :rows="5"
            placeholder="请详细描述您的任务完成情况..."
          />
        </el-form-item>

        <el-form-item label="提交文件">
          <el-upload
            ref="uploadRef"
            :file-list="submitForm.fileList"
            :on-change="handleFileChange"
            :on-remove="handleFileRemove"
            :before-upload="beforeUpload"
            :auto-upload="false"
            multiple
            drag
          >
            <el-icon class="el-icon--upload"><upload-filled /></el-icon>
            <div class="el-upload__text">
              将文件拖到此处，或<em>点击上传</em>
            </div>
            <template #tip>
              <div class="el-upload__tip">
                支持多个文件上传，单个文件大小不超过10MB
              </div>
            </template>
          </el-upload>
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="showSubmitTaskDialog = false">取消</el-button>
        <el-button type="primary" @click="submitTaskCompletion" :loading="submitting">
          提交
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { ref, reactive, onMounted, computed, watch, nextTick } from 'vue'
import { useStore } from 'vuex'
import { useRouter } from 'vue-router'
import { recordAPI, teamAPI, projectAPI } from '@/api'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, ArrowDown, UploadFilled, Refresh, Calendar, User } from '@element-plus/icons-vue'

export default {
  name: 'TaskManagementView',
  components: {
    Plus,
    ArrowDown,
    UploadFilled,
    Refresh,
    Calendar,
    User
  },
  setup() {
    const store = useStore()
    const router = useRouter()
    const formRef = ref()
    
    const loading = ref(false)
    const submitting = ref(false)
    const showCreateDialog = ref(false)
    const showDetailDialog = ref(false)
    const showSubmitTaskDialog = ref(false)
    const editingTask = ref(null)
    const selectedTask = ref(null)
    const progressValue = ref(0)

    // 任务提交表单
    const submitForm = reactive({
      content: '',
      fileList: []
    })
    const submitFormRules = {
      content: [
        { required: true, message: '请输入提交说明', trigger: 'blur' }
      ]
    }
    
    const tasks = ref([])
    const myTeams = ref([])
    const currentTeamId = ref('')
    const activeTab = ref('all')
    const currentPage = ref(1)
    const pageSize = ref(10)
    const total = ref(0)
    
    const taskForm = reactive({
      title: '',
      description: '',
      priority: 'MEDIUM',
      deadline: null
    })
    
    const formRules = {
      title: [
        { required: true, message: '请输入任务标题', trigger: 'blur' },
        { min: 2, max: 100, message: '标题长度在 2 到 100 个字符', trigger: 'blur' }
      ],
      description: [
        { required: true, message: '请输入任务描述', trigger: 'blur' }
      ],
      deadline: [
        { required: true, message: '请选择截止时间', trigger: 'change' }
      ]
    }
    
    const currentUser = computed(() => store.getters.currentUser)
    const isTeacher = computed(() => currentUser.value?.role === 'TEACHER')
    const isStudent = computed(() => currentUser.value?.role === 'STUDENT')

    // 跳转到任务发布页面
    const goToTaskPublish = () => {
      router.push('/dashboard/task-publish')
    }
    
    // 任务统计
    const taskStats = computed(() => {
      const total = tasks.value.length
      const completed = tasks.value.filter(t => t.status === 'COMPLETED').length
      const inProgress = tasks.value.filter(t => t.status === 'IN_PROGRESS').length
      const completionRate = total > 0 ? Math.round((completed / total) * 100) : 0

      return {
        total,
        completed,
        inProgress,
        completionRate
      }
    })

    // 教师端：按项目和团队分组的任务数据
    const groupedTasks = computed(() => {
      if (!isTeacher.value || tasks.value.length === 0) {
        return []
      }

      // 按项目分组
      const projectGroups = {}

      tasks.value.forEach(task => {
        const projectId = task.projectId || 'unknown'
        const projectName = task.projectName || '未知项目'
        const teamId = task.teamId || 'unknown'
        const teamName = task.teamName || '未知团队'

        if (!projectGroups[projectId]) {
          projectGroups[projectId] = {
            projectId,
            projectName,
            teams: {},
            totalTasks: 0,
            completedTasks: 0
          }
        }

        if (!projectGroups[projectId].teams[teamId]) {
          projectGroups[projectId].teams[teamId] = {
            teamId,
            teamName,
            tasks: []
          }
        }

        projectGroups[projectId].teams[teamId].tasks.push(task)
        projectGroups[projectId].totalTasks++

        if (task.status === 'COMPLETED') {
          projectGroups[projectId].completedTasks++
        }
      })

      // 转换为数组格式
      return Object.values(projectGroups).map(project => ({
        ...project,
        teams: Object.values(project.teams)
      }))
    })
    
    // 加载我的团队
    const loadMyTeams = async () => {
      try {
        console.log('=== 团队加载调试信息 ===')
        console.log('当前用户:', currentUser.value)
        console.log('用户角色:', currentUser.value?.role)

        if (currentUser.value?.role === 'TEACHER') {
          // 教师：获取自己项目的团队
          await loadTeacherProjectTeams()
        } else {
          // 学生：获取参与的团队
          console.log('🔍 学生获取团队列表...')
          const response = await teamAPI.getJoinedTeams()
          console.log('✅ 学生团队响应:', response)
          myTeams.value = response?.records || []
          console.log('📋 学生团队列表:', myTeams.value.map(team => ({
            id: team.id,
            name: team.name,
            projectId: team.projectId,
            projectName: team.projectName,
            status: team.status
          })))
        }

        console.log('团队列表:', myTeams.value)
        console.log('团队数量:', myTeams.value.length)

        if (myTeams.value.length > 0) {
          currentTeamId.value = myTeams.value[0].id
          console.log('选中的团队ID:', currentTeamId.value)
          loadTasks()
        } else {
          console.log('没有找到任何团队')
        }
      } catch (error) {
        console.error('加载团队列表失败:', error)
        ElMessage.error('加载团队列表失败')
      }
    }

    // 加载教师项目的团队
    const loadTeacherProjectTeams = async () => {
      try {
        // 1. 先获取教师自己的项目列表
        const projectsResponse = await projectAPI.getMyProjects()
        console.log('教师项目响应:', projectsResponse)
        const myProjects = projectsResponse?.records || []
        console.log('教师项目列表:', myProjects)

        if (myProjects.length === 0) {
          console.log('教师没有发布任何项目')
          myTeams.value = []
          return
        }

        // 2. 获取每个项目的团队列表
        const allTeams = []
        for (const project of myProjects) {
          try {
            console.log(`=== 开始获取项目 ${project.name} (ID: ${project.id}) 的团队 ===`)
            const teamsResponse = await teamAPI.getProjectTeams(project.id)
            console.log(`项目 ${project.name} 的团队响应:`, teamsResponse)
            console.log('响应类型:', typeof teamsResponse)
            console.log('响应结构:', JSON.stringify(teamsResponse, null, 2))

            const projectTeams = teamsResponse?.records || []
            console.log(`项目 ${project.name} 的团队数量:`, projectTeams.length)

            // 为每个团队添加项目信息，便于显示
            const teamsWithProject = projectTeams.map(team => ({
              ...team,
              projectName: project.name,
              projectId: project.id
            }))

            allTeams.push(...teamsWithProject)
            console.log(`项目 ${project.name} 处理完成，累计团队数:`, allTeams.length)
          } catch (error) {
            console.error(`=== 获取项目 ${project.name} 的团队失败 ===`)
            console.error('错误对象:', error)
            console.error('错误消息:', error.message)
            console.error('错误响应:', error.response)
            console.error('错误状态:', error.response?.status)
            console.error('错误数据:', error.response?.data)
          }
        }

        console.log('教师所有项目的团队:', allTeams)
        myTeams.value = allTeams

      } catch (error) {
        console.error('加载教师项目团队失败:', error)
        throw error
      }
    }

    // 加载任务列表
    const loadTasks = async () => {
      if (!currentTeamId.value) {
        console.log('❌ 没有选择团队，无法加载任务')
        return
      }

      try {
        loading.value = true
        const params = {
          page: currentPage.value,
          size: pageSize.value,
          type: 'TASK',  // 只获取任务类型的记录
          sortBy: 'createTime',
          sortDir: 'desc'
        }

        console.log('=== 任务加载调试信息 ===')
        console.log('当前用户:', currentUser.value)
        console.log('当前团队ID:', currentTeamId.value)
        console.log('所有团队:', myTeams.value)
        console.log('请求参数:', params)
        console.log('请求URL:', `/records/teams/${currentTeamId.value}`)

        // 获取团队任务
        const response = await recordAPI.getTeamRecords(currentTeamId.value, params)
        console.log('✅ 团队任务API响应:', response)
        console.log('响应数据结构:', {
          hasRecords: !!response?.records,
          recordsLength: response?.records?.length || 0,
          totalElements: response?.totalElements,
          totalPages: response?.totalPages,
          currentPage: response?.currentPage
        })

        let allTasks = response?.records || []
        console.log('📋 获取到的任务数量:', allTasks.length)

        // 显示任务的详细信息
        if (allTasks.length > 0) {
          console.log('任务详情:', allTasks.map(task => ({
            id: task.id,
            title: task.title,
            type: task.type,
            status: task.status,
            teamId: task.teamId,
            projectId: task.projectId
          })))

          console.log('任务状态分布:', allTasks.reduce((acc, task) => {
            acc[task.status] = (acc[task.status] || 0) + 1
            return acc
          }, {}))
        } else {
          console.log('❌ 没有获取到任何任务数据')

          // 尝试获取所有任务进行对比
          try {
            console.log('🔍 尝试获取所有任务进行对比...')
            const allTasksResponse = await recordAPI.getRecords({ type: 'TASK', page: 1, size: 50 })
            console.log('所有任务响应:', allTasksResponse)
            const allTasksInSystem = allTasksResponse?.records || []
            console.log('系统中所有任务:', allTasksInSystem.map(task => ({
              id: task.id,
              title: task.title,
              teamId: task.teamId,
              projectId: task.projectId,
              status: task.status,
              creator: task.creator?.realName
            })))

            // 调用调试接口
            console.log('🔍 调用调试接口获取任务详情...')
            const debugResponse = await fetch('/api/records/debug/tasks', {
              headers: {
                'Authorization': `Bearer ${localStorage.getItem('token')}`
              }
            })
            const debugData = await debugResponse.json()
            console.log('调试接口响应:', debugData)
          } catch (error) {
            console.log('获取所有任务失败:', error)
          }
        }



        // 为缺少status字段的任务添加默认状态
        allTasks = allTasks.map(task => ({
          ...task,
          status: task.status || 'PUBLISHED',  // 默认状态为PUBLISHED（已发布）
          deadline: task.dueDate || null,  // 使用dueDate字段
          assignee: task.assignee || null,
          progress: task.progress || 0
        }))

        // 前端过滤任务状态
        console.log('当前选中的标签页:', activeTab.value)
        console.log('过滤前所有任务:', allTasks.map(task => ({ id: task.id, title: task.title, status: task.status })))

        if (activeTab.value !== 'all') {
          const filteredTasks = allTasks.filter(task => task.status === activeTab.value)
          console.log(`过滤前任务数量: ${allTasks.length}, 过滤后任务数量: ${filteredTasks.length}`)
          console.log('过滤后的任务:', filteredTasks.map(task => ({ id: task.id, title: task.title, status: task.status })))
          allTasks = filteredTasks
        }

        // 注意：现在任务状态直接反映提交状态，不需要额外加载提交记录

        // 强制触发响应式更新
        tasks.value = []
        await nextTick()
        tasks.value = [...allTasks]
        total.value = allTasks.length

      } catch (error) {
        console.error('=== 任务加载错误信息 ===')
        console.error('错误对象:', error)
        console.error('错误消息:', error.message)
        console.error('错误响应:', error.response)
        console.error('错误状态:', error.response?.status)
        console.error('错误数据:', error.response?.data)

        ElMessage.error('加载任务失败: ' + (error.response?.data?.message || error.message || '未知错误'))
      } finally {
        loading.value = false
      }
    }
    
    // 提交任务
    const submitTask = async () => {
      if (!formRef.value) return

      try {
        await formRef.value.validate()
        submitting.value = true

        const taskData = {
          title: taskForm.title,
          content: taskForm.description,
          priority: getPriorityNumber(taskForm.priority),
          dueDate: taskForm.deadline ? new Date(taskForm.deadline).toISOString() : null
        }

        // 创建任务时需要额外的字段
        if (!editingTask.value) {
          taskData.type = 'TASK'
          taskData.teamId = currentTeamId.value
        }

        console.log('提交任务数据:', taskData)
        console.log('是否为编辑模式:', !!editingTask.value)

        if (editingTask.value) {
          console.log('更新任务ID:', editingTask.value.id)
          await recordAPI.updateRecord(editingTask.value.id, taskData)
          ElMessage.success('任务更新成功')
        } else {
          console.log('创建新任务')
          await recordAPI.createRecord(taskData)
          ElMessage.success('任务创建成功')
        }

        showCreateDialog.value = false
        resetForm()
        loadTasks()
      } catch (error) {
        console.error('提交任务失败:', error)
        console.error('错误详情:', error.response?.data)
        const errorMessage = error.response?.data?.message || error.message || '提交任务失败'
        ElMessage.error(errorMessage)
      } finally {
        submitting.value = false
      }
    }
    
    // 重置表单
    const resetForm = () => {
      Object.assign(taskForm, {
        title: '',
        description: '',
        priority: 'MEDIUM',
        deadline: null
      })
      editingTask.value = null
    }
    
    // 查看任务详情
    const viewTask = (task) => {
      selectedTask.value = task
      progressValue.value = task.progress || 0
      showDetailDialog.value = true
    }
    
    // 编辑任务
    const editTask = (task) => {
      editingTask.value = task
      Object.assign(taskForm, {
        title: task.title,
        description: task.content,
        priority: task.priority,
        deadline: task.dueDate ? new Date(task.dueDate) : null
      })
      showCreateDialog.value = true
    }
    
    // 处理任务操作
    const handleTaskAction = async ({ action, task }) => {
      try {
        let message = ''
        let status = ''
        
        switch (action) {
          case 'start':
            status = 'IN_PROGRESS'
            message = '任务已开始'
            break
          case 'submit':
            // 学生提交任务，显示提交对话框
            showSubmitDialog(task)
            return
          case 'approve':
            // 教师审核通过任务
            const approveParams = new URLSearchParams()
            approveParams.append('approved', 'true')
            await recordAPI.reviewTask(task.id, approveParams)
            ElMessage.success('任务审核通过')
            loadTasks()
            return
          case 'reject':
            // 教师拒绝任务，需要提供反馈
            const feedback = await ElMessageBox.prompt('请输入拒绝理由', '任务审核', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              inputType: 'textarea'
            })
            const rejectParams = new URLSearchParams()
            rejectParams.append('approved', 'false')
            rejectParams.append('feedback', feedback.value || '')
            await recordAPI.reviewTask(task.id, rejectParams)
            ElMessage.success('任务已拒绝，学生需要重新完成')
            loadTasks()
            return
          case 'activate':
            // 教师激活任务
            await recordAPI.updateTaskStatus(task.id, 'ACTIVE')
            ElMessage.success('任务已激活')
            loadTasks()
            return
          case 'cancel':
            status = 'CANCELLED'
            message = '任务已取消'
            break
          case 'delete':
            await ElMessageBox.confirm('确定要删除这个任务吗？', '确认删除', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
            })
            await recordAPI.deleteRecord(task.id)
            ElMessage.success('任务删除成功')
            loadTasks()
            return
        }
        
        if (status) {
          await recordAPI.updateRecord(task.id, { status })
          ElMessage.success(message)
          loadTasks()
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('任务操作失败:', error)
          ElMessage.error('操作失败')
        }
      }
    }
    
    // 更新进度
    const updateProgress = async (value) => {
      if (!selectedTask.value) return

      try {
        await recordAPI.updateRecord(selectedTask.value.id, { progress: value })
        ElMessage.success('进度更新成功')
        loadTasks()
      } catch (error) {
        console.error('更新进度失败:', error)
        ElMessage.error('更新进度失败')
      }
    }

    // 开始任务
    const startTask = async (task) => {
      try {
        await ElMessageBox.confirm(
          `确定要开始任务"${task.title}"吗？开始后任务状态将变为活跃状态。`,
          '开始任务',
          {
            confirmButtonText: '开始',
            cancelButtonText: '取消',
            type: 'info'
          }
        )

        // 调用API更新任务状态为ACTIVE
        await recordAPI.updateTaskStatus(task.id, 'ACTIVE')
        ElMessage.success('任务已开始')
        loadTasks() // 刷新任务列表
      } catch (error) {
        if (error !== 'cancel') {
          console.error('开始任务失败:', error)
          ElMessage.error('开始任务失败')
        }
      }
    }

    // 显示任务提交对话框
    const showSubmitDialog = (task) => {
      selectedTask.value = task
      submitForm.content = ''
      submitForm.fileList = []
      showSubmitTaskDialog.value = true
    }

    // 文件上传处理
    const uploadRef = ref()

    const handleFileChange = (_, fileList) => {
      submitForm.fileList = fileList
    }

    const handleFileRemove = (_, fileList) => {
      submitForm.fileList = fileList
    }

    const beforeUpload = (file) => {
      const isLt10M = file.size / 1024 / 1024 < 10
      if (!isLt10M) {
        ElMessage.error('文件大小不能超过10MB!')
        return false
      }
      return true
    }

    // 提交任务完成
    const submitTaskCompletion = async () => {
      try {
        submitting.value = true

        // 使用FormData支持文件上传
        const formData = new FormData()
        formData.append('submissionContent', submitForm.content)

        // 添加文件
        if (submitForm.fileList && submitForm.fileList.length > 0) {
          submitForm.fileList.forEach((fileItem) => {
            if (fileItem.raw) {
              formData.append('files', fileItem.raw)
            }
          })
        }

        await recordAPI.submitTaskWithFiles(selectedTask.value.id, formData)

        ElMessage.success('任务提交成功')
        showSubmitTaskDialog.value = false
        loadTasks()
      } catch (error) {
        console.error('任务提交失败:', error)
        ElMessage.error('任务提交失败')
      } finally {
        submitting.value = false
      }
    }
    
    // 权限检查
    const canEdit = (task) => {
      return task.creator?.id === currentUser.value?.id || currentUser.value?.role === 'TEACHER'
    }

    const canManage = (task) => {
      return task.creator?.id === currentUser.value?.id || currentUser.value?.role === 'TEACHER'
    }
    
    // 工具方法
    const formatDate = (date) => {
      if (!date) return ''
      return new Date(date).toLocaleString('zh-CN')
    }
    
    const isOverdue = (deadline) => {
      return new Date(deadline) < new Date()
    }
    
    const getStatusColor = (status) => {
      const colorMap = {
        'ACTIVE': 'success',
        'PUBLISHED': 'info',
        'IN_PROGRESS': 'primary',
        'SUBMITTED': 'warning',
        'COMPLETED': 'success',
        'CANCELLED': 'danger',
        'REJECTED': 'danger',
        'APPROVED': 'success',
        'DRAFT': 'info'
      }
      return colorMap[status] || 'info'  // 默认返回 'info' 而不是空字符串
    }

    const getStatusText = (status) => {
      const textMap = {
        'ACTIVE': '可提交',
        'PUBLISHED': '已发布',
        'IN_PROGRESS': '需重新提交',
        'SUBMITTED': '待审核',
        'COMPLETED': '已完成',
        'CANCELLED': '已取消',
        'REJECTED': '已拒绝',
        'APPROVED': '已通过',
        'DRAFT': '草稿'
      }
      return textMap[status] || status
    }


    
    const getPriorityColor = (priority) => {
      // 处理数字类型的优先级
      let priorityKey = priority
      if (typeof priority === 'number') {
        const numberToEnum = {
          1: 'LOW',
          2: 'MEDIUM',
          3: 'HIGH',
          4: 'URGENT',
          5: 'URGENT'
        }
        priorityKey = numberToEnum[priority] || 'MEDIUM'
      }

      const colorMap = {
        'LOW': 'info',
        'MEDIUM': 'primary',
        'HIGH': 'warning',
        'URGENT': 'danger'
      }
      return colorMap[priorityKey] || 'info'  // 默认返回 'info' 而不是空字符串
    }

    const getPriorityText = (priority) => {
      // 处理数字类型的优先级
      let priorityKey = priority
      if (typeof priority === 'number') {
        const numberToEnum = {
          1: 'LOW',
          2: 'MEDIUM',
          3: 'HIGH',
          4: 'URGENT',
          5: 'URGENT'
        }
        priorityKey = numberToEnum[priority] || 'MEDIUM'
      }

      const textMap = {
        'LOW': '低',
        'MEDIUM': '中',
        'HIGH': '高',
        'URGENT': '紧急'
      }
      return textMap[priorityKey] || '中'  // 默认返回 '中'
    }

    const getPriorityNumber = (priority) => {
      const numberMap = {
        'LOW': 1,
        'MEDIUM': 2,
        'HIGH': 3,
        'URGENT': 4
      }
      return numberMap[priority] || 2
    }
    
    // 监听团队变化
    watch(currentTeamId, (newTeamId) => {
      if (newTeamId) {
        loadTasks()
      }
    })


    


    onMounted(() => {
      loadTasks()
      loadMyTeams()
    })
    
    return {
      loading,
      submitting,
      showCreateDialog,
      showDetailDialog,
      showSubmitTaskDialog,
      editingTask,
      selectedTask,
      progressValue,
      submitForm,
      submitFormRules,
      tasks,
      myTeams,
      currentTeamId,
      activeTab,
      currentPage,
      pageSize,
      total,
      taskForm,
      formRules,
      formRef,
      currentUser,
      isTeacher,
      isStudent,
      taskStats,
      groupedTasks,
      goToTaskPublish,
      loadTasks,
      submitTask,
      viewTask,
      editTask,
      handleTaskAction,
      updateProgress,
      startTask,
      showSubmitDialog,
      submitTaskCompletion,
      uploadRef,
      handleFileChange,
      handleFileRemove,
      beforeUpload,
      canEdit,
      canManage,
      formatDate,
      isOverdue,
      getStatusColor,
      getStatusText,
      getPriorityColor,
      getPriorityText,
      getPriorityNumber
    }
  }
}
</script>

<style scoped>
.task-management {
  padding: 0;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.task-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
  margin-top: 16px;
}

.task-card {
  cursor: pointer;
  transition: all 0.3s ease;
  height: 100%;
}

.task-card:hover {
  transform: translateY(-4px);
}

.task-card .el-card {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.task-card .el-card__body {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 200px; /* 设置最小高度，确保卡片一致性 */
}

.task-card h4 {
  margin: 0 0 10px 0;
  color: #303133;
}

.task-description {
  color: #606266;
  margin: 10px 0;
  display: -webkit-box;
  -webkit-line-clamp: 2; /* 只显示2行 */
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.5;
  height: 3em; /* 固定高度为2行 (1.5 * 2 = 3em) */
  flex: none; /* 不允许弹性伸缩，保持固定高度 */
}

.task-meta {
  display: flex;
  gap: 8px;
  margin: 16px 0;
  padding-top: 12px;
  border-top: 1px solid #f0f0f0;
}

.task-info {
  margin: 12px 0;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-bottom: 6px;
  color: #909399;
  font-size: 13px;
}

.task-footer {
  margin-top: auto;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 8px; /* 统一按钮间距 */
  flex-wrap: wrap; /* 允许按钮换行 */
}

.pagination {
  margin-top: 20px;
  text-align: center;
}
</style>
