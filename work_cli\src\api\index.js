import request from '@/utils/request'

// 文件管理相关API
export const fileAPI = {
  // 上传文件
  uploadFile(file, options = {}) {
    const formData = new FormData()
    formData.append('file', file)

    // 添加可选参数
    if (options.projectId) formData.append('projectId', options.projectId)
    if (options.teamId) formData.append('teamId', options.teamId)
    if (options.recordId) formData.append('recordId', options.recordId)
    if (options.fileType) formData.append('fileType', options.fileType)
    if (options.description) formData.append('description', options.description)

    return request.post('/files/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      onUploadProgress: options.onProgress
    })
  },

  // 下载文件
  downloadFile(fileId) {
    return request.get(`/files/${fileId}/download`, {
      responseType: 'blob'
    })
  },

  // 预览文件
  previewFile(fileId) {
    return request.get(`/files/${fileId}/preview`, {
      responseType: 'blob'
    })
  },

  // 获取文件详情
  getFileInfo(fileId) {
    return request.get(`/files/${fileId}`)
  },

  // 根据记录ID获取文件列表
  getFilesByRecord(recordId) {
    return request.get(`/files/record/${recordId}`)
  },

  // 更新文件信息
  updateFileInfo(fileId, data) {
    const formData = new FormData()
    if (data.description) formData.append('description', data.description)

    return request.put(`/files/${fileId}`, formData)
  },

  // 删除文件
  deleteFile(fileId) {
    return request.delete(`/files/${fileId}`)
  },

  // 获取所有文件
  getAllFiles(params = {}) {
    return request.get('/files', { params })
  },

  // 获取我的文件
  getMyFiles(params = {}) {
    return request.get('/files/my', { params })
  },

  // 获取项目文件
  getProjectFiles(projectId, params = {}) {
    return request.get(`/files/project/${projectId}`, { params })
  },

  // 获取团队文件
  getTeamFiles(teamId, params = {}) {
    return request.get(`/files/team/${teamId}`, { params })
  },

  // 根据记录ID获取文件列表（统一方法名）
  getRecordFiles(recordId) {
    return request.get(`/files/record/${recordId}`)
  },

  // 别名方法，保持向后兼容
  getFilesByRecord(recordId) {
    return this.getRecordFiles(recordId)
  },

  // 根据类型获取文件
  getFilesByType(fileType, params = {}) {
    return request.get(`/files/type/${fileType}`, { params })
  },

  // 搜索文件
  searchFiles(keyword, params = {}) {
    return request.get('/files/search', {
      params: { keyword, ...params }
    })
  },

  // 高级搜索文件
  advancedSearchFiles(searchParams = {}) {
    return request.get('/files/advanced-search', { params: searchParams })
  },

  // 协作空间相关API
  // 上传文件到协作空间
  uploadCollaborationFile(file, options = {}) {
    const formData = new FormData()
    formData.append('file', file)
    formData.append('teamId', options.teamId)
    if (options.description) formData.append('description', options.description)

    return request.post('/files/collaboration/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      onUploadProgress: options.onProgress
    })
  },

  // 获取协作空间文件列表
  getCollaborationFiles(teamId, params = {}) {
    return request.get(`/files/collaboration/team/${teamId}`, { params })
  },

  // 获取协作空间统计信息
  getCollaborationStats(teamId) {
    return request.get(`/files/collaboration/team/${teamId}/stats`)
  },

  // 调试 - 获取所有文件信息
  getAllFilesForDebug() {
    return request.get('/files/debug/all-files')
  },

  // 获取最近文件
  getRecentFiles(limit = 10) {
    return request.get('/files/recent', { params: { limit } })
  },

  // 获取热门文件
  getPopularFiles(limit = 10) {
    return request.get('/files/popular', { params: { limit } })
  },

  // 批量删除文件
  batchDeleteFiles(fileIds) {
    return request.delete('/files/batch', { data: fileIds })
  },

  // 获取文件统计
  getFileStatistics() {
    return request.get('/files/statistics')
  },

  // 获取项目文件统计
  getProjectFileStatistics(projectId) {
    return request.get(`/files/statistics/project/${projectId}`)
  },

  // 获取团队文件统计
  getTeamFileStatistics(teamId) {
    return request.get(`/files/statistics/team/${teamId}`)
  },

  // 清理孤儿文件
  cleanupOrphanFiles() {
    return request.post('/files/cleanup')
  }
}

// 仪表盘统计相关API
export const dashboardAPI = {
  // 获取仪表盘统计数据
  getDashboardStats() {
    return request.get('/api/dashboard/stats')
  },

  // 测试用户信息获取
  testUserInfo() {
    return request.get('/api/dashboard/test-user')
  },

  // 获取最新项目
  getLatestProjects(limit = 5) {
    return request.get('/projects/latest', { params: { limit } })
  },

  // 获取最新活动
  getLatestActivities(limit = 10) {
    return request.get('/records/latest', { params: { limit } })
  }
}

// 用户认证相关API
export const authAPI = {
  // 用户注册
  register(userData) {
    return request.post('/auth/register', userData)
  },

  // 用户登录
  login(credentials) {
    return request.post('/auth/login', credentials)
  },

  // 刷新token
  refreshToken() {
    return request.post('/auth/refresh')
  },

  // 获取当前用户信息
  getCurrentUser() {
    return request.get('/auth/me')
  },

  // 用户登出
  logout() {
    return request.post('/auth/logout')
  },

  // 更新个人资料
  updateProfile(profileData) {
    return request.put('/users/me', profileData)
  },

  // 修改密码
  changePassword(passwordData) {
    return request.put('/users/me/password', passwordData)
  }
}

// 项目管理相关API
export const projectAPI = {
  // 获取项目列表
  getProjects(params = {}) {
    return request.get('/projects', { params })
  },

  // 获取我的项目列表
  getMyProjects(params = {}) {
    return request.get('/projects/my', { params })
  },

  // 获取项目详情
  getProject(id) {
    return request.get(`/projects/${id}`)
  },

  // 创建项目
  createProject(projectData) {
    return request.post('/projects', projectData)
  },

  // 更新项目
  updateProject(id, projectData) {
    return request.put(`/projects/${id}`, projectData)
  },

  // 删除项目
  deleteProject(id) {
    return request.delete(`/projects/${id}`)
  },

  // 启动项目
  startProject(id) {
    return request.put(`/projects/${id}/status`, { status: 'IN_PROGRESS' })
  },

  // 终止项目
  terminateProject(id) {
    return request.post(`/projects/${id}/terminate`)
  },

  // 搜索项目
  searchProjects(keyword, params = {}) {
    return request.get('/projects/search', {
      params: { keyword, ...params }
    })
  },

  // 获取热门项目
  getPopularProjects(params = {}) {
    return request.get('/projects/popular', { params })
  },

  // 获取我的项目
  getMyProjects(params = {}) {
    return request.get('/projects/my', { params })
  },

  // 获取项目统计
  getProjectStats(id) {
    return request.get(`/projects/${id}/stats`)
  },

  // 获取全局项目统计
  getGlobalProjectStats() {
    return request.get('/projects/stats')
  },

  // 获取最新项目
  getLatestProjects(limit = 10) {
    return request.get('/projects/latest', { params: { limit } })
  }
}

// 团队管理相关API
export const teamAPI = {
  // 获取团队列表
  getTeams(params = {}) {
    return request.get('/teams', { params })
  },

  // 获取团队详情
  getTeam(id) {
    return request.get(`/teams/${id}`)
  },

  // 创建团队
  createTeam(teamData) {
    return request.post('/teams', teamData)
  },

  // 更新团队
  updateTeam(id, teamData) {
    return request.put(`/teams/${id}`, teamData)
  },

  // 停止招募
  stopRecruiting(id) {
    return request.put(`/teams/${id}/stop-recruiting`)
  },

  // 解散团队
  deleteTeam(id) {
    return request.delete(`/teams/${id}`)
  },

  // 解散团队（别名）
  disbandTeam(id) {
    return request.delete(`/teams/${id}`)
  },

  // 申请加入团队
  joinTeam(teamId, reason = '') {
    return request.post(`/teams/${teamId}/join`, { reason })
  },

  // 离开团队
  leaveTeam(teamId) {
    return request.post(`/teams/${teamId}/leave`)
  },

  // 获取我的团队（单个团队）
  getMyTeam() {
    return request.get('/teams/my')
  },

  // 获取我参与的团队
  getJoinedTeams(params = {}) {
    return request.get('/teams/joined', { params })
  },

  // 获取项目的团队列表（教师专用）
  getProjectTeams(projectId, params = {}) {
    return request.get(`/teams/project/${projectId}`, { params })
  },

  // 申请项目
  applyProject(teamId, projectId, applicationData) {
    return request.post(`/teams/${teamId}/apply/${projectId}`, applicationData)
  },

  // 获取团队成员
  getTeamMembers(teamId) {
    return request.get(`/teams/${teamId}/members`)
  },

  // 移除团队成员
  removeTeamMember(teamId, userId) {
    return request.delete(`/teams/${teamId}/members/${userId}`)
  },

  // 兼容性方法（保持向后兼容）
  removeMember(teamId, userId) {
    return this.removeTeamMember(teamId, userId)
  },

  // 获取团队统计
  getTeamStats() {
    return request.get('/teams/stats')
  },

  // 获取团队申请列表（队长）
  getTeamApplications(teamId, params = {}) {
    return request.get(`/teams/${teamId}/applications`, { params })
  },

  // 审核团队申请
  reviewApplication(applicationId, data) {
    return request.put(`/teams/applications/${applicationId}/review`, data)
  },

  // 获取待审核申请列表（队长）
  getPendingApplications(params = {}) {
    return request.get('/teams/applications/pending', { params })
  },

  // 取消申请
  cancelApplication(applicationId) {
    return request.delete(`/teams/applications/${applicationId}`)
  },

  // 获取我的申请状态
  getMyApplication() {
    return request.get('/teams/applications/my')
  },

  // 教师移除团队
  removeTeamByTeacher(teamId) {
    return request.delete(`/teams/${teamId}/remove`)
  }
}

// 记录管理相关API
export const recordAPI = {
  // 获取记录列表
  getRecords(params = {}) {
    return request.get('/records', { params })
  },

  // 获取项目记录
  getProjectRecords(projectId, params = {}) {
    return request.get(`/records/projects/${projectId}`, { params })
  },

  // 获取团队记录
  getTeamRecords(teamId, params = {}) {
    return request.get(`/records/teams/${teamId}`, { params })
  },

  // 创建记录
  createRecord(recordData) {
    return request.post('/records', recordData)
  },

  // 更新记录
  updateRecord(id, recordData) {
    return request.put(`/records/${id}`, recordData)
  },

  // 删除记录
  deleteRecord(id) {
    return request.delete(`/records/${id}`)
  },

  // 学生提交任务
  submitTask(taskId, data) {
    return request.post(`/records/tasks/${taskId}/submit`, data)
  },

  // 学生提交任务（支持文件上传）
  submitTaskWithFiles(taskId, formData) {
    return request.post(`/records/tasks/${taskId}/submit`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      timeout: 60000, // 文件上传设置60秒超时
      onUploadProgress: (progressEvent) => {
        const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total)
        console.log(`上传进度: ${percentCompleted}%`)
      }
    })
  },

  // 获取任务的提交记录
  getTaskSubmissions(taskId) {
    return request.get(`/records/tasks/${taskId}/submissions`)
  },

  // 更新任务状态
  updateTaskStatus(taskId, status) {
    return request.put(`/records/tasks/${taskId}/status`, { status })
  },

  // 获取单个记录
  getRecord(recordId) {
    return request.get(`/records/${recordId}`)
  },

  // 教师审核任务
  reviewTask(taskId, data) {
    return request.post(`/records/tasks/${taskId}/review`, data)
  },

  // 获取项目讨论
  getProjectDiscussions(projectId, params = {}) {
    return request.get(`/records/projects/${projectId}/discussions`, { params })
  },

  // 获取团队讨论
  getTeamDiscussions(teamId, params = {}) {
    return request.get(`/records/teams/${teamId}/discussions`, { params })
  },

  // 获取项目进度
  getProjectProgress(projectId, params = {}) {
    return request.get(`/records/projects/${projectId}/progress`, { params })
  },

  // 获取记录统计
  getRecordStats() {
    return request.get('/records/stats')
  },

  // 获取最新记录
  getLatestRecords(limit = 10) {
    return request.get('/records/latest', { params: { limit } })
  },

  // 获取讨论回复
  getDiscussionReplies(discussionId) {
    return request.get(`/records/${discussionId}/replies`)
  }
}

// 用户管理相关API
export const userAPI = {
  // 搜索用户
  searchUsers(params = {}) {
    return request.get('/users', { params })
  },

  // 获取用户详情
  getUser(id) {
    return request.get(`/users/${id}`)
  },

  // 获取用户列表
  getUsers(params = {}) {
    return request.get('/users', { params })
  }
}

// 审核管理相关API
export const reviewAPI = {
  // 获取申请列表
  getApplications(params = {}) {
    return request.get('/applications', { params })
  },

  // 获取待审核申请
  getPendingApplications(params = {}) {
    return request.get('/applications/pending', { params })
  },

  // 通过申请
  approveApplication(id) {
    return request.put(`/applications/${id}/approve`)
  },

  // 拒绝申请
  rejectApplication(id) {
    return request.put(`/applications/${id}/reject`)
  },

  // 获取申请详情
  getApplication(id) {
    return request.get(`/applications/${id}`)
  },

  // 审核团队申请
  reviewApplication(teamId, reviewData) {
    return request.put(`/teams/${teamId}/review`, reviewData)
  },

  // 批量审核
  batchReview(reviewData) {
    return request.post('/teams/batch-review', reviewData)
  }
}

// 公告管理相关API（普通用户）
export const announcementAPI = {
  // 获取公告列表（普通用户）
  getAnnouncements(params = {}) {
    return request.get('/users/announcements/dashboard', { params })
  },

  // 获取公告详情
  getAnnouncement(id) {
    return request.get(`/users/announcements/${id}`)
  },

  // 增加公告查看次数
  incrementViewCount(id) {
    return request.post(`/users/announcements/${id}/view`)
  },

  // 获取最新公告（仪表盘用）
  getLatestAnnouncements(limit = 5) {
    return request.get('/users/announcements/dashboard', { params: { size: limit } })
  }
}

// 导入新的API模块
export { default as authApi } from './auth'
export { default as projectApi } from './project'
export { default as teamApi } from './team'
export { default as userApi } from './user'
export { default as evaluationApi } from './evaluation'

export { default as fileApi } from './file'
export { default as recordApi } from './record'
export { default as notificationApi } from './notification'
export { default as adminApi } from './admin'

export default {
  authAPI,
  projectAPI,
  teamAPI,
  recordAPI,
  reviewAPI,
  userAPI,
  fileAPI,
  dashboardAPI,
  announcementAPI
}
