{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport { ref, onMounted, computed } from 'vue';\nimport { useRouter } from 'vue-router';\nimport { useStore } from 'vuex';\nimport { ElMessage, ElMessageBox } from 'element-plus';\nimport { teamAPI } from '@/api';\nimport { UserFilled, Refresh, Setting, Calendar, User, Bell, Document, Edit, Delete, More } from '@element-plus/icons-vue';\nimport { getAvatarUrl, getInitial } from '@/utils/avatar';\nimport TeamApplicationStatus from '@/components/TeamApplicationStatus.vue';\nexport default {\n  name: 'MyTeamView',\n  components: {\n    UserFilled,\n    Refresh,\n    Setting,\n    Calendar,\n    User,\n    Bell,\n    Document,\n    Edit,\n    Delete,\n    More,\n    TeamApplicationStatus\n  },\n  setup() {\n    const router = useRouter();\n    const store = useStore();\n    const team = ref(null);\n    const members = ref([]);\n    const loading = ref(true);\n    const pendingApplicationsCount = ref(0);\n    const myApplication = ref(null);\n    const currentUser = computed(() => store.getters.currentUser);\n    const isLeader = computed(() => {\n      return team.value?.role === 'LEADER';\n    });\n\n    // 判断团队是否正式获得项目（可以显示项目信息和进度）\n    const isProjectActive = computed(() => {\n      if (!team.value?.status) return false;\n      // 只有在 WORKING 或 COMPLETED 状态下才算正式获得项目\n      return team.value.status === 'WORKING' || team.value.status === 'COMPLETED';\n    });\n    const fetchMyTeam = async () => {\n      try {\n        loading.value = true;\n        const response = await teamAPI.getMyTeam();\n\n        // 响应拦截器已经提取了result.data，所以response本身就是团队数据\n        team.value = response;\n        if (team.value && team.value.id) {\n          await fetchTeamMembers();\n        } else {\n          // 确保清空团队数据\n          team.value = null;\n          members.value = [];\n          // 如果没有团队，获取申请状态\n          await fetchMyApplication();\n        }\n      } catch (error) {\n        ElMessage.error('获取团队信息失败: ' + (error.message || '未知错误'));\n      } finally {\n        loading.value = false;\n      }\n    };\n    const fetchMyApplication = async () => {\n      try {\n        const response = await teamAPI.getMyApplication();\n        myApplication.value = response;\n      } catch (error) {\n        // 没有申请是正常情况，或者申请已被取消/删除\n        myApplication.value = null;\n      }\n    };\n    const fetchTeamMembers = async () => {\n      try {\n        const response = await teamAPI.getTeamMembers(team.value.id);\n\n        // 响应拦截器已经提取了result.data，所以response本身就是成员数据\n        members.value = response;\n\n        // 如果是队长，获取待审核申请数量\n        if (team.value.role === 'LEADER') {\n          await fetchPendingApplicationsCount();\n        }\n      } catch (error) {\n        ElMessage.error('获取团队成员信息失败');\n      }\n    };\n    const fetchPendingApplicationsCount = async () => {\n      try {\n        const response = await teamAPI.getPendingApplications({\n          page: 1,\n          size: 1\n        });\n        // 响应拦截器已经提取了result.data，所以response本身就是分页数据\n        pendingApplicationsCount.value = response?.total || 0;\n      } catch (error) {\n        pendingApplicationsCount.value = 0;\n      }\n    };\n    const getStatusType = status => {\n      const statusMap = {\n        'PENDING': 'warning',\n        'APPROVED': 'success',\n        'REJECTED': 'danger',\n        'ACTIVE': 'primary',\n        'COMPLETED': 'info',\n        'RECRUITING': 'success',\n        'STOPPED': 'warning',\n        'DISBANDED': 'danger'\n      };\n      return statusMap[status] || 'info';\n    };\n    const getStatusText = status => {\n      const statusMap = {\n        'PENDING': '待审核',\n        'APPROVED': '已通过',\n        'REJECTED': '已拒绝',\n        'ACTIVE': '进行中',\n        'COMPLETED': '已完成'\n      };\n      return statusMap[status] || status;\n    };\n    const formatDate = dateString => {\n      if (!dateString) return '';\n      return new Date(dateString).toLocaleDateString('zh-CN');\n    };\n    const manageApplications = () => {\n      // 跳转到申请管理页面\n      router.push('/dashboard/teams/applications');\n    };\n    const manageTeam = () => {\n      // 跳转到团队管理页面\n      router.push(`/dashboard/teams/${team.value.id}/manage`);\n    };\n    const confirmLeaveTeam = async () => {\n      try {\n        await ElMessageBox.confirm('确定要退出当前团队吗？退出后您将无法参与团队项目。', '确认退出', {\n          confirmButtonText: '确定退出',\n          cancelButtonText: '取消',\n          type: 'warning'\n        });\n        await teamAPI.leaveTeam(team.value.id);\n        ElMessage.success('已成功退出团队');\n        await fetchMyTeam(); // 重新获取团队信息\n      } catch (error) {\n        if (error !== 'cancel') {\n          console.error('退出团队失败:', error);\n          ElMessage.error('退出团队失败');\n        }\n      }\n    };\n\n    // 新增方法\n    const getStatusClass = status => {\n      const classMap = {\n        'ACTIVE': 'status-active',\n        'INACTIVE': 'status-inactive',\n        'DISBANDED': 'status-disbanded'\n      };\n      return classMap[status] || 'status-default';\n    };\n\n    // 获取申请状态的标签类型\n    const getApplicationStatusType = () => {\n      switch (team.value?.status) {\n        case 'PENDING':\n          return 'warning';\n        case 'REJECTED':\n          return 'danger';\n        default:\n          return 'info';\n      }\n    };\n\n    // 获取申请状态的文本\n    const getApplicationStatusText = () => {\n      switch (team.value?.status) {\n        case 'PENDING':\n          return '申请审核中';\n        case 'REJECTED':\n          return '申请被拒绝';\n        default:\n          return '状态未知';\n      }\n    };\n\n    // 获取状态提醒的标题\n    const getStatusAlertTitle = () => {\n      switch (team.value?.status) {\n        case 'PENDING':\n          return '项目申请已提交';\n        case 'REJECTED':\n          return '项目申请被拒绝';\n        default:\n          return '状态信息';\n      }\n    };\n\n    // 获取状态提醒的描述\n    const getStatusAlertDescription = () => {\n      switch (team.value?.status) {\n        case 'PENDING':\n          return '您的团队已成功申请该项目，请耐心等待教师审核。审核结果将会及时通知您。';\n        case 'REJECTED':\n          return '很遗憾，您的项目申请未通过审核。您可以查看教师反馈，完善团队后重新申请其他项目。';\n        default:\n          return '';\n      }\n    };\n\n    // 获取状态提醒的类型\n    const getStatusAlertType = () => {\n      switch (team.value?.status) {\n        case 'PENDING':\n          return 'warning';\n        case 'REJECTED':\n          return 'error';\n        default:\n          return 'info';\n      }\n    };\n    const editTeam = () => {\n      router.push(`/dashboard/teams/${team.value.id}/edit`);\n    };\n    const confirmRemoveMember = async member => {\n      console.log('=== 我的团队页面移除成员调试 ===');\n      console.log('member对象:', member);\n      console.log('member.id:', member.id);\n      console.log('member.userId:', member.userId);\n      console.log('当前用户ID:', currentUser.value?.id);\n      console.log('是否为队长:', isLeader.value);\n      console.log('团队成员数量:', members.value.length);\n\n      // 检查是否是队长试图移除自己\n      const isRemovingSelf = member.id === currentUser.value?.id;\n      const isOnlyMember = members.value.length === 1;\n      if (isRemovingSelf && isLeader.value && isOnlyMember) {\n        // 队长是唯一成员，应该退出团队（自动解散）\n        await confirmLeaveTeam();\n        return;\n      }\n      if (isRemovingSelf && isLeader.value) {\n        // 队长试图移除自己但团队还有其他成员\n        ElMessage.error('队长不能移除自己，请先转让队长权限或退出团队');\n        return;\n      }\n      try {\n        await ElMessageBox.confirm(`确定要移除成员 ${member.realName || member.username} 吗？`, '确认移除', {\n          confirmButtonText: '确定移除',\n          cancelButtonText: '取消',\n          type: 'warning'\n        });\n        console.log('准备调用API - 团队ID:', team.value.id, '成员ID:', member.id);\n        await teamAPI.removeTeamMember(team.value.id, member.id);\n        ElMessage.success('成员移除成功');\n        await fetchMyTeam(); // 重新获取团队信息\n      } catch (error) {\n        if (error !== 'cancel') {\n          console.error('移除成员失败:', error);\n          ElMessage.error('移除成员失败: ' + (error.response?.data?.message || error.message || '未知错误'));\n        }\n      }\n    };\n    onMounted(() => {\n      fetchMyTeam();\n    });\n    return {\n      team,\n      members,\n      loading,\n      pendingApplicationsCount,\n      myApplication,\n      isLeader,\n      currentUser,\n      isProjectActive,\n      getStatusType,\n      getStatusText,\n      getStatusClass,\n      getApplicationStatusType,\n      getApplicationStatusText,\n      getStatusAlertTitle,\n      getStatusAlertDescription,\n      getStatusAlertType,\n      formatDate,\n      manageApplications,\n      manageTeam,\n      confirmLeaveTeam,\n      editTeam,\n      confirmRemoveMember,\n      fetchMyApplication,\n      // 头像工具函数\n      getAvatarUrl,\n      getInitial\n    };\n  }\n};", "map": {"version": 3, "names": ["ref", "onMounted", "computed", "useRouter", "useStore", "ElMessage", "ElMessageBox", "teamAPI", "UserFilled", "Refresh", "Setting", "Calendar", "User", "Bell", "Document", "Edit", "Delete", "More", "getAvatarUrl", "getInitial", "TeamApplicationStatus", "name", "components", "setup", "router", "store", "team", "members", "loading", "pendingApplicationsCount", "myApplication", "currentUser", "getters", "<PERSON><PERSON><PERSON><PERSON>", "value", "role", "isProjectActive", "status", "fetchMyTeam", "response", "getMyTeam", "id", "fetchTeamMembers", "fetchMyApplication", "error", "message", "getMyApplication", "getTeamMembers", "fetchPendingApplicationsCount", "getPendingApplications", "page", "size", "total", "getStatusType", "statusMap", "getStatusText", "formatDate", "dateString", "Date", "toLocaleDateString", "manageApplications", "push", "manageTeam", "confirmLeaveTeam", "confirm", "confirmButtonText", "cancelButtonText", "type", "leaveTeam", "success", "console", "getStatusClass", "classMap", "getApplicationStatusType", "getApplicationStatusText", "getStatusAlertTitle", "getStatusAlertDescription", "getStatusAlertType", "editTeam", "confirmRemoveMember", "member", "log", "userId", "length", "isRemovingSelf", "isOnlyMember", "realName", "username", "removeTeamMember", "data"], "sources": ["D:\\workspace\\idea\\worker\\work_cli\\src\\views\\team\\MyTeamView.vue"], "sourcesContent": ["<template>\n  <div class=\"my-team-container\">\n    <el-card>\n      <template #header>\n        <div class=\"card-header\">\n          <h3>我的团队</h3>\n          <div class=\"header-actions\">\n            <el-button v-if=\"team\" @click=\"fetchTeam\" :icon=\"Refresh\">\n              刷新\n            </el-button>\n          </div>\n        </div>\n      </template>\n\n      <!-- 申请状态 -->\n      <div v-if=\"!team && !loading && myApplication\" class=\"application-section\">\n        <h4 class=\"section-title\">我的申请</h4>\n        <TeamApplicationStatus\n          :application=\"myApplication\"\n          @refresh=\"fetchMyApplication\"\n        />\n      </div>\n\n      <!-- 无团队状态 -->\n      <div v-if=\"!team && !loading && !myApplication\" class=\"empty-state\">\n        <el-icon><UserFilled /></el-icon>\n        <h4>您还没有加入任何团队</h4>\n        <p>加入团队开始协作，或创建属于您的团队</p>\n        <div class=\"empty-actions\">\n          <el-button type=\"primary\" @click=\"$router.push('/dashboard/teams/create')\">\n            创建团队\n          </el-button>\n          <el-button @click=\"$router.push('/dashboard/teams')\">\n            浏览团队\n          </el-button>\n        </div>\n      </div>\n\n      <!-- 团队信息 -->\n      <div v-else-if=\"team\" class=\"team-content\">\n        <!-- 团队基本信息 -->\n        <div class=\"team-overview\">\n          <div class=\"team-header\">\n            <div class=\"team-avatar\">\n              {{ team.name?.charAt(0) || 'T' }}\n            </div>\n            <div class=\"team-basic-info\">\n              <h4>{{ team.name }}</h4>\n              <el-tag :type=\"getStatusType(team.status)\">{{ getStatusText(team.status) }}</el-tag>\n            </div>\n\n          </div>\n          <p class=\"team-description\">{{ team.description || '这个团队还没有添加描述...' }}</p>\n\n          <div class=\"team-info-grid\">\n            <div class=\"info-item\">\n              <el-icon><User /></el-icon>\n              <span>{{ team.memberCount || team.members?.length || 0 }} 名成员</span>\n            </div>\n            <div class=\"info-item\" v-if=\"team.project\">\n              <el-icon><Document /></el-icon>\n              <span>{{ team.project.name }}</span>\n            </div>\n            <div class=\"info-item\">\n              <el-icon><Calendar /></el-icon>\n              <span>{{ formatDate(team.createTime) }}</span>\n            </div>\n          </div>\n        </div>\n\n        <!-- 当前项目信息 - 只有在团队正式获得项目时才显示 -->\n        <div v-if=\"team.project && isProjectActive\" class=\"project-section\">\n          <h5>当前项目</h5>\n          <div class=\"project-info\">\n            <h6>{{ team.project.name }}</h6>\n            <p>{{ team.project.description }}</p>\n          </div>\n        </div>\n\n        <!-- 项目申请状态信息 -->\n        <div v-if=\"team.project && !isProjectActive\" class=\"application-status-section\">\n          <h5>项目申请状态</h5>\n          <div class=\"application-info\">\n            <div class=\"application-header\">\n              <h6>{{ team.project.name }}</h6>\n              <el-tag :type=\"getApplicationStatusType()\" size=\"large\">\n                {{ getApplicationStatusText() }}\n              </el-tag>\n            </div>\n            <p class=\"project-description\">{{ team.project.description }}</p>\n\n            <!-- 申请信息 -->\n            <div v-if=\"team.applicationMessage\" class=\"application-message\">\n              <h6>申请说明：</h6>\n              <p>{{ team.applicationMessage }}</p>\n            </div>\n\n            <!-- 教师反馈 -->\n            <div v-if=\"team.teacherFeedback\" class=\"teacher-feedback\">\n              <h6>教师反馈：</h6>\n              <p>{{ team.teacherFeedback }}</p>\n            </div>\n\n            <!-- 状态说明 -->\n            <div class=\"status-description\">\n              <el-alert\n                :title=\"getStatusAlertTitle()\"\n                :description=\"getStatusAlertDescription()\"\n                :type=\"getStatusAlertType()\"\n                show-icon\n                :closable=\"false\"\n              />\n            </div>\n          </div>\n        </div>\n\n        <!-- 团队成员管理 -->\n        <div class=\"members-section\">\n          <div class=\"section-header\">\n            <h5>团队成员 ({{ members?.length || 0 }}/{{ team.project?.maxTeamSize || team.maxMembers || 6 }})</h5>\n          </div>\n\n          <div class=\"members-grid\">\n            <div\n              v-for=\"member in members\"\n              :key=\"member.id\"\n              class=\"member-card\"\n            >\n              <el-card shadow=\"hover\">\n                <div class=\"member-info\">\n                  <div class=\"member-left\">\n                    <div class=\"member-avatar\">\n                      <el-avatar :size=\"40\" :src=\"getAvatarUrl(member.avatar)\">\n                        {{ getInitial(member.realName || member.username) }}\n                      </el-avatar>\n                    </div>\n                    <div class=\"member-details\">\n                      <h6>{{ member.realName || member.username }}</h6>\n                      <el-tag :type=\"member.role === 'LEADER' ? 'warning' : 'info'\" size=\"small\">\n                        {{ member.role === 'LEADER' ? '队长' : '成员' }}\n                      </el-tag>\n                      <p class=\"member-username\">@{{ member.username }}</p>\n                    </div>\n                  </div>\n                  <div class=\"member-actions\" v-if=\"isLeader && member.id !== currentUser?.id\">\n                    <el-button\n                      size=\"small\"\n                      type=\"danger\"\n                      class=\"remove-member-btn\"\n                      @click=\"confirmRemoveMember(member)\"\n                    >\n                      移除\n                    </el-button>\n                  </div>\n                </div>\n              </el-card>\n            </div>\n          </div>\n        </div>\n\n        <!-- 团队操作 -->\n        <div class=\"team-actions\">\n          <!-- 队长专用按钮 -->\n          <template v-if=\"isLeader\">\n            <el-button type=\"primary\" @click=\"editTeam\" :icon=\"Edit\" class=\"action-btn primary-btn\">\n              编辑团队\n            </el-button>\n            <el-button type=\"warning\" @click=\"manageApplications\" :icon=\"Bell\" class=\"action-btn warning-btn\">\n              申请管理\n              <el-badge v-if=\"pendingApplicationsCount > 0\" :value=\"pendingApplicationsCount\" />\n            </el-button>\n            <el-button v-if=\"members.length === 1\" type=\"danger\" @click=\"confirmLeaveTeam\" :icon=\"Delete\" class=\"action-btn danger-btn\">\n              退出团队\n            </el-button>\n          </template>\n\n          <!-- 普通成员按钮 -->\n          <template v-else>\n            <el-button type=\"danger\" @click=\"confirmLeaveTeam\" :icon=\"Delete\" class=\"action-btn danger-btn\">\n              退出团队\n            </el-button>\n          </template>\n        </div>\n      </div>\n    </el-card>\n\n    <!-- 加载状态 -->\n    <div v-if=\"loading\" style=\"padding: 20px;\">\n      <el-skeleton :rows=\"8\" animated />\n    </div>\n  </div>\n</template>\n\n<script>\nimport { ref, onMounted, computed } from 'vue'\nimport { useRouter } from 'vue-router'\nimport { useStore } from 'vuex'\nimport { ElMessage, ElMessageBox } from 'element-plus'\nimport { teamAPI } from '@/api'\nimport { UserFilled, Refresh, Setting, Calendar, User, Bell, Document, Edit, Delete, More } from '@element-plus/icons-vue'\nimport { getAvatarUrl, getInitial } from '@/utils/avatar'\nimport TeamApplicationStatus from '@/components/TeamApplicationStatus.vue'\n\nexport default {\n  name: 'MyTeamView',\n  components: {\n    UserFilled,\n    Refresh,\n    Setting,\n    Calendar,\n    User,\n    Bell,\n    Document,\n    Edit,\n    Delete,\n    More,\n    TeamApplicationStatus\n  },\n  setup() {\n    const router = useRouter()\n    const store = useStore()\n    const team = ref(null)\n    const members = ref([])\n    const loading = ref(true)\n    const pendingApplicationsCount = ref(0)\n    const myApplication = ref(null)\n\n    const currentUser = computed(() => store.getters.currentUser)\n\n    const isLeader = computed(() => {\n      return team.value?.role === 'LEADER'\n    })\n\n    // 判断团队是否正式获得项目（可以显示项目信息和进度）\n    const isProjectActive = computed(() => {\n      if (!team.value?.status) return false\n      // 只有在 WORKING 或 COMPLETED 状态下才算正式获得项目\n      return team.value.status === 'WORKING' || team.value.status === 'COMPLETED'\n    })\n\n    const fetchMyTeam = async () => {\n      try {\n        loading.value = true\n        const response = await teamAPI.getMyTeam()\n\n        // 响应拦截器已经提取了result.data，所以response本身就是团队数据\n        team.value = response\n\n        if (team.value && team.value.id) {\n          await fetchTeamMembers()\n        } else {\n          // 确保清空团队数据\n          team.value = null\n          members.value = []\n          // 如果没有团队，获取申请状态\n          await fetchMyApplication()\n        }\n      } catch (error) {\n        ElMessage.error('获取团队信息失败: ' + (error.message || '未知错误'))\n      } finally {\n        loading.value = false\n      }\n    }\n\n    const fetchMyApplication = async () => {\n      try {\n        const response = await teamAPI.getMyApplication()\n        myApplication.value = response\n      } catch (error) {\n        // 没有申请是正常情况，或者申请已被取消/删除\n        myApplication.value = null\n      }\n    }\n\n    const fetchTeamMembers = async () => {\n      try {\n        const response = await teamAPI.getTeamMembers(team.value.id)\n\n        // 响应拦截器已经提取了result.data，所以response本身就是成员数据\n        members.value = response\n\n        // 如果是队长，获取待审核申请数量\n        if (team.value.role === 'LEADER') {\n          await fetchPendingApplicationsCount()\n        }\n      } catch (error) {\n        ElMessage.error('获取团队成员信息失败')\n      }\n    }\n\n    const fetchPendingApplicationsCount = async () => {\n      try {\n        const response = await teamAPI.getPendingApplications({ page: 1, size: 1 })\n        // 响应拦截器已经提取了result.data，所以response本身就是分页数据\n        pendingApplicationsCount.value = response?.total || 0\n      } catch (error) {\n        pendingApplicationsCount.value = 0\n      }\n    }\n\n    const getStatusType = (status) => {\n      const statusMap = {\n        'PENDING': 'warning',\n        'APPROVED': 'success',\n        'REJECTED': 'danger',\n        'ACTIVE': 'primary',\n        'COMPLETED': 'info',\n        'RECRUITING': 'success',\n        'STOPPED': 'warning',\n        'DISBANDED': 'danger'\n      }\n      return statusMap[status] || 'info'\n    }\n\n    const getStatusText = (status) => {\n      const statusMap = {\n        'PENDING': '待审核',\n        'APPROVED': '已通过',\n        'REJECTED': '已拒绝',\n        'ACTIVE': '进行中',\n        'COMPLETED': '已完成'\n      }\n      return statusMap[status] || status\n    }\n\n    const formatDate = (dateString) => {\n      if (!dateString) return ''\n      return new Date(dateString).toLocaleDateString('zh-CN')\n    }\n\n    const manageApplications = () => {\n      // 跳转到申请管理页面\n      router.push('/dashboard/teams/applications')\n    }\n\n    const manageTeam = () => {\n      // 跳转到团队管理页面\n      router.push(`/dashboard/teams/${team.value.id}/manage`)\n    }\n\n    const confirmLeaveTeam = async () => {\n      try {\n        await ElMessageBox.confirm(\n          '确定要退出当前团队吗？退出后您将无法参与团队项目。',\n          '确认退出',\n          {\n            confirmButtonText: '确定退出',\n            cancelButtonText: '取消',\n            type: 'warning'\n          }\n        )\n\n        await teamAPI.leaveTeam(team.value.id)\n        ElMessage.success('已成功退出团队')\n        await fetchMyTeam() // 重新获取团队信息\n      } catch (error) {\n        if (error !== 'cancel') {\n          console.error('退出团队失败:', error)\n          ElMessage.error('退出团队失败')\n        }\n      }\n    }\n\n    // 新增方法\n    const getStatusClass = (status) => {\n      const classMap = {\n        'ACTIVE': 'status-active',\n        'INACTIVE': 'status-inactive',\n        'DISBANDED': 'status-disbanded'\n      }\n      return classMap[status] || 'status-default'\n    }\n\n\n\n    // 获取申请状态的标签类型\n    const getApplicationStatusType = () => {\n      switch (team.value?.status) {\n        case 'PENDING':\n          return 'warning'\n        case 'REJECTED':\n          return 'danger'\n        default:\n          return 'info'\n      }\n    }\n\n    // 获取申请状态的文本\n    const getApplicationStatusText = () => {\n      switch (team.value?.status) {\n        case 'PENDING':\n          return '申请审核中'\n        case 'REJECTED':\n          return '申请被拒绝'\n        default:\n          return '状态未知'\n      }\n    }\n\n    // 获取状态提醒的标题\n    const getStatusAlertTitle = () => {\n      switch (team.value?.status) {\n        case 'PENDING':\n          return '项目申请已提交'\n        case 'REJECTED':\n          return '项目申请被拒绝'\n        default:\n          return '状态信息'\n      }\n    }\n\n    // 获取状态提醒的描述\n    const getStatusAlertDescription = () => {\n      switch (team.value?.status) {\n        case 'PENDING':\n          return '您的团队已成功申请该项目，请耐心等待教师审核。审核结果将会及时通知您。'\n        case 'REJECTED':\n          return '很遗憾，您的项目申请未通过审核。您可以查看教师反馈，完善团队后重新申请其他项目。'\n        default:\n          return ''\n      }\n    }\n\n    // 获取状态提醒的类型\n    const getStatusAlertType = () => {\n      switch (team.value?.status) {\n        case 'PENDING':\n          return 'warning'\n        case 'REJECTED':\n          return 'error'\n        default:\n          return 'info'\n      }\n    }\n\n    const editTeam = () => {\n      router.push(`/dashboard/teams/${team.value.id}/edit`)\n    }\n\n\n\n\n\n    const confirmRemoveMember = async (member) => {\n      console.log('=== 我的团队页面移除成员调试 ===')\n      console.log('member对象:', member)\n      console.log('member.id:', member.id)\n      console.log('member.userId:', member.userId)\n      console.log('当前用户ID:', currentUser.value?.id)\n      console.log('是否为队长:', isLeader.value)\n      console.log('团队成员数量:', members.value.length)\n\n      // 检查是否是队长试图移除自己\n      const isRemovingSelf = member.id === currentUser.value?.id\n      const isOnlyMember = members.value.length === 1\n\n      if (isRemovingSelf && isLeader.value && isOnlyMember) {\n        // 队长是唯一成员，应该退出团队（自动解散）\n        await confirmLeaveTeam()\n        return\n      }\n\n      if (isRemovingSelf && isLeader.value) {\n        // 队长试图移除自己但团队还有其他成员\n        ElMessage.error('队长不能移除自己，请先转让队长权限或退出团队')\n        return\n      }\n\n      try {\n        await ElMessageBox.confirm(\n          `确定要移除成员 ${member.realName || member.username} 吗？`,\n          '确认移除',\n          {\n            confirmButtonText: '确定移除',\n            cancelButtonText: '取消',\n            type: 'warning'\n          }\n        )\n\n        console.log('准备调用API - 团队ID:', team.value.id, '成员ID:', member.id)\n        await teamAPI.removeTeamMember(team.value.id, member.id)\n        ElMessage.success('成员移除成功')\n        await fetchMyTeam() // 重新获取团队信息\n      } catch (error) {\n        if (error !== 'cancel') {\n          console.error('移除成员失败:', error)\n          ElMessage.error('移除成员失败: ' + (error.response?.data?.message || error.message || '未知错误'))\n        }\n      }\n    }\n\n\n\n    onMounted(() => {\n      fetchMyTeam()\n    })\n\n    return {\n      team,\n      members,\n      loading,\n      pendingApplicationsCount,\n      myApplication,\n      isLeader,\n      currentUser,\n      isProjectActive,\n      getStatusType,\n      getStatusText,\n      getStatusClass,\n      getApplicationStatusType,\n      getApplicationStatusText,\n      getStatusAlertTitle,\n      getStatusAlertDescription,\n      getStatusAlertType,\n      formatDate,\n      manageApplications,\n      manageTeam,\n      confirmLeaveTeam,\n      editTeam,\n      confirmRemoveMember,\n      fetchMyApplication,\n      // 头像工具函数\n      getAvatarUrl,\n      getInitial\n    }\n  }\n}\n</script>\n\n<style scoped>\n.my-team-container {\n  padding: 0;\n  background: #f8f9fa;\n  min-height: calc(100vh - 120px);\n}\n\n.my-team-container .el-card {\n  border-radius: 12px;\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);\n  border: none;\n  overflow: hidden;\n}\n\n.card-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 24px 0;\n}\n\n.card-header h3 {\n  margin: 0;\n  font-size: 24px;\n  font-weight: 600;\n  color: #1a1a1a;\n}\n\n.header-actions {\n  display: flex;\n  gap: 12px;\n}\n\n.header-actions .el-button {\n  border-radius: 8px;\n  padding: 10px 20px;\n  font-weight: 500;\n}\n\n.application-section {\n  margin-bottom: 32px;\n  padding: 24px;\n  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);\n  border-radius: 12px;\n  border: 1px solid #e9ecef;\n}\n\n.section-title {\n  margin: 0 0 20px 0;\n  color: #1a1a1a;\n  font-size: 20px;\n  font-weight: 600;\n}\n\n.empty-state {\n  text-align: center;\n  padding: 60px 40px;\n  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);\n  border-radius: 16px;\n  border: 2px dashed #d0d7de;\n}\n\n.empty-state .el-icon {\n  font-size: 48px;\n  color: #8c92a4;\n  margin-bottom: 16px;\n}\n\n.empty-state h4 {\n  margin: 16px 0 8px 0;\n  font-size: 18px;\n  font-weight: 600;\n  color: #1a1a1a;\n}\n\n.empty-state p {\n  margin: 0 0 24px 0;\n  color: #6c757d;\n  font-size: 14px;\n}\n\n.empty-actions {\n  margin-top: 24px;\n  display: flex;\n  gap: 16px;\n  justify-content: center;\n}\n\n.empty-actions .el-button {\n  border-radius: 8px;\n  padding: 12px 24px;\n  font-weight: 500;\n}\n\n.team-content {\n  padding: 32px 0;\n}\n\n.team-overview {\n  margin-bottom: 40px;\n  padding: 32px;\n  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);\n  border-radius: 16px;\n  border: 1px solid #e9ecef;\n  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.04);\n}\n\n.team-header {\n  display: flex;\n  align-items: center;\n  gap: 20px;\n  margin-bottom: 24px;\n}\n\n.team-avatar {\n  width: 72px;\n  height: 72px;\n  border-radius: 16px;\n  background: linear-gradient(135deg, #409eff 0%, #66b3ff 100%);\n  color: white;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-size: 28px;\n  font-weight: 700;\n  box-shadow: 0 4px 16px rgba(64, 158, 255, 0.3);\n}\n\n.team-basic-info {\n  flex: 1;\n}\n\n.team-basic-info h4 {\n  margin: 0 0 12px 0;\n  color: #1a1a1a;\n  font-size: 24px;\n  font-weight: 700;\n}\n\n.team-description {\n  color: #606266;\n  margin: 16px 0;\n  line-height: 1.6;\n}\n\n.team-info-grid {\n  display: flex;\n  gap: 20px;\n  margin: 16px 0;\n}\n\n.info-item {\n  display: flex;\n  align-items: center;\n  gap: 6px;\n  color: #909399;\n  font-size: 14px;\n}\n\n.project-section {\n  margin: 30px 0;\n  padding: 20px;\n  background: #f8f9fa;\n  border-radius: 8px;\n}\n\n.project-section h5 {\n  margin: 0 0 16px 0;\n  color: #303133;\n}\n\n.project-info h6 {\n  margin: 0 0 8px 0;\n  color: #303133;\n}\n\n\n\n/* 项目申请状态样式 */\n.application-status-section {\n  margin: 30px 0;\n  padding: 20px;\n  background: #fafafa;\n  border-radius: 8px;\n  border: 1px solid #e4e7ed;\n}\n\n.application-status-section h5 {\n  margin: 0 0 16px 0;\n  color: #303133;\n}\n\n.application-info {\n  background: white;\n  padding: 20px;\n  border-radius: 8px;\n  border: 1px solid #e4e7ed;\n}\n\n.application-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 12px;\n}\n\n.application-header h6 {\n  margin: 0;\n  color: #303133;\n  font-size: 16px;\n  font-weight: 600;\n}\n\n.project-description {\n  margin: 0 0 16px 0;\n  color: #606266;\n  line-height: 1.6;\n  font-size: 14px;\n}\n\n.application-message,\n.teacher-feedback {\n  margin-bottom: 16px;\n  padding: 12px;\n  background: #f8f9fa;\n  border-radius: 6px;\n  border: 1px solid #e4e7ed;\n}\n\n.application-message h6,\n.teacher-feedback h6 {\n  margin: 0 0 8px 0;\n  color: #303133;\n  font-size: 14px;\n  font-weight: 600;\n}\n\n.application-message p,\n.teacher-feedback p {\n  margin: 0;\n  color: #606266;\n  line-height: 1.5;\n  font-size: 14px;\n}\n\n.teacher-feedback {\n  border-left: 4px solid #f56c6c;\n  background: #fef0f0;\n}\n\n.status-description {\n  margin-top: 16px;\n}\n\n.members-section {\n  margin: 30px 0;\n}\n\n.section-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n}\n\n.section-header h5 {\n  margin: 0;\n  color: #303133;\n}\n\n.section-actions {\n  display: flex;\n  gap: 12px;\n}\n\n.members-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));\n  gap: 16px;\n}\n\n.member-card .el-card {\n  height: 100%;\n}\n\n.member-info {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  width: 100%;\n  padding: 4px 0;\n}\n\n.member-left {\n  display: flex;\n  align-items: center;\n  gap: 12px;\n  flex: 1;\n}\n\n.member-avatar {\n  width: 40px;\n  height: 40px;\n  border-radius: 50%;\n  background: #67c23a;\n  color: white;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-weight: bold;\n}\n\n.member-details {\n  flex: 1;\n}\n\n.member-details h6 {\n  margin: 0 0 4px 0;\n  color: #303133;\n}\n\n.member-username {\n  color: #909399;\n  font-size: 12px;\n  margin: 4px 0 0 0;\n}\n\n.member-actions {\n  flex-shrink: 0;\n  margin-left: 12px;\n}\n\n.remove-member-btn {\n  background: linear-gradient(135deg, #f56c6c 0%, #ff8a8a 100%);\n  border-color: #f56c6c;\n  color: white;\n  border-radius: 4px;\n  font-size: 12px;\n  padding: 4px 12px;\n  font-weight: 500;\n  transition: all 0.3s ease;\n  min-width: 50px;\n}\n\n.remove-member-btn:hover {\n  background: linear-gradient(135deg, #e85656 0%, #f07777 100%);\n  border-color: #e85656;\n  transform: translateY(-1px);\n  box-shadow: 0 4px 12px rgba(245, 108, 108, 0.3);\n}\n\n.remove-member-btn:active {\n  transform: translateY(0);\n}\n\n.member-card {\n  transition: all 0.3s ease;\n}\n\n.team-actions {\n  margin-top: 20px;\n  display: flex;\n  gap: 12px;\n  justify-content: flex-start;\n  flex-wrap: wrap;\n}\n\n.action-btn {\n  min-width: 100px;\n  font-weight: 500;\n  border-radius: 6px;\n  transition: all 0.3s ease;\n}\n\n.primary-btn {\n  background: linear-gradient(135deg, #409eff 0%, #66b3ff 100%);\n  border-color: #409eff;\n  color: white;\n}\n\n.primary-btn:hover {\n  background: linear-gradient(135deg, #337ecc 0%, #5aa3e6 100%);\n  border-color: #337ecc;\n  transform: translateY(-2px);\n  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);\n}\n\n.warning-btn {\n  background: linear-gradient(135deg, #e6a23c 0%, #f0b659 100%);\n  border-color: #e6a23c;\n  color: white;\n}\n\n.warning-btn:hover {\n  background: linear-gradient(135deg, #cf9236 0%, #d9a441 100%);\n  border-color: #cf9236;\n  transform: translateY(-2px);\n  box-shadow: 0 4px 12px rgba(230, 162, 60, 0.3);\n}\n\n.danger-btn {\n  background: linear-gradient(135deg, #f56c6c 0%, #ff8a8a 100%);\n  border-color: #f56c6c;\n  color: white;\n}\n\n.danger-btn:hover {\n  background: linear-gradient(135deg, #e85656 0%, #f07777 100%);\n  border-color: #e85656;\n  transform: translateY(-2px);\n  box-shadow: 0 4px 12px rgba(245, 108, 108, 0.3);\n}\n\n\n</style>\n\n\n"], "mappings": ";AAkMA,SAASA,GAAG,EAAEC,SAAS,EAAEC,QAAO,QAAS,KAAI;AAC7C,SAASC,SAAQ,QAAS,YAAW;AACrC,SAASC,QAAO,QAAS,MAAK;AAC9B,SAASC,SAAS,EAAEC,YAAW,QAAS,cAAa;AACrD,SAASC,OAAM,QAAS,OAAM;AAC9B,SAASC,UAAU,EAAEC,OAAO,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,IAAI,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,IAAI,EAAEC,MAAM,EAAEC,IAAG,QAAS,yBAAwB;AACzH,SAASC,YAAY,EAAEC,UAAS,QAAS,gBAAe;AACxD,OAAOC,qBAAoB,MAAO,wCAAuC;AAEzE,eAAe;EACbC,IAAI,EAAE,YAAY;EAClBC,UAAU,EAAE;IACVd,UAAU;IACVC,OAAO;IACPC,OAAO;IACPC,QAAQ;IACRC,IAAI;IACJC,IAAI;IACJC,QAAQ;IACRC,IAAI;IACJC,MAAM;IACNC,IAAI;IACJG;EACF,CAAC;EACDG,KAAKA,CAAA,EAAG;IACN,MAAMC,MAAK,GAAIrB,SAAS,CAAC;IACzB,MAAMsB,KAAI,GAAIrB,QAAQ,CAAC;IACvB,MAAMsB,IAAG,GAAI1B,GAAG,CAAC,IAAI;IACrB,MAAM2B,OAAM,GAAI3B,GAAG,CAAC,EAAE;IACtB,MAAM4B,OAAM,GAAI5B,GAAG,CAAC,IAAI;IACxB,MAAM6B,wBAAuB,GAAI7B,GAAG,CAAC,CAAC;IACtC,MAAM8B,aAAY,GAAI9B,GAAG,CAAC,IAAI;IAE9B,MAAM+B,WAAU,GAAI7B,QAAQ,CAAC,MAAMuB,KAAK,CAACO,OAAO,CAACD,WAAW;IAE5D,MAAME,QAAO,GAAI/B,QAAQ,CAAC,MAAM;MAC9B,OAAOwB,IAAI,CAACQ,KAAK,EAAEC,IAAG,KAAM,QAAO;IACrC,CAAC;;IAED;IACA,MAAMC,eAAc,GAAIlC,QAAQ,CAAC,MAAM;MACrC,IAAI,CAACwB,IAAI,CAACQ,KAAK,EAAEG,MAAM,EAAE,OAAO,KAAI;MACpC;MACA,OAAOX,IAAI,CAACQ,KAAK,CAACG,MAAK,KAAM,SAAQ,IAAKX,IAAI,CAACQ,KAAK,CAACG,MAAK,KAAM,WAAU;IAC5E,CAAC;IAED,MAAMC,WAAU,GAAI,MAAAA,CAAA,KAAY;MAC9B,IAAI;QACFV,OAAO,CAACM,KAAI,GAAI,IAAG;QACnB,MAAMK,QAAO,GAAI,MAAMhC,OAAO,CAACiC,SAAS,CAAC;;QAEzC;QACAd,IAAI,CAACQ,KAAI,GAAIK,QAAO;QAEpB,IAAIb,IAAI,CAACQ,KAAI,IAAKR,IAAI,CAACQ,KAAK,CAACO,EAAE,EAAE;UAC/B,MAAMC,gBAAgB,CAAC;QACzB,OAAO;UACL;UACAhB,IAAI,CAACQ,KAAI,GAAI,IAAG;UAChBP,OAAO,CAACO,KAAI,GAAI,EAAC;UACjB;UACA,MAAMS,kBAAkB,CAAC;QAC3B;MACF,EAAE,OAAOC,KAAK,EAAE;QACdvC,SAAS,CAACuC,KAAK,CAAC,YAAW,IAAKA,KAAK,CAACC,OAAM,IAAK,MAAM,CAAC;MAC1D,UAAU;QACRjB,OAAO,CAACM,KAAI,GAAI,KAAI;MACtB;IACF;IAEA,MAAMS,kBAAiB,GAAI,MAAAA,CAAA,KAAY;MACrC,IAAI;QACF,MAAMJ,QAAO,GAAI,MAAMhC,OAAO,CAACuC,gBAAgB,CAAC;QAChDhB,aAAa,CAACI,KAAI,GAAIK,QAAO;MAC/B,EAAE,OAAOK,KAAK,EAAE;QACd;QACAd,aAAa,CAACI,KAAI,GAAI,IAAG;MAC3B;IACF;IAEA,MAAMQ,gBAAe,GAAI,MAAAA,CAAA,KAAY;MACnC,IAAI;QACF,MAAMH,QAAO,GAAI,MAAMhC,OAAO,CAACwC,cAAc,CAACrB,IAAI,CAACQ,KAAK,CAACO,EAAE;;QAE3D;QACAd,OAAO,CAACO,KAAI,GAAIK,QAAO;;QAEvB;QACA,IAAIb,IAAI,CAACQ,KAAK,CAACC,IAAG,KAAM,QAAQ,EAAE;UAChC,MAAMa,6BAA6B,CAAC;QACtC;MACF,EAAE,OAAOJ,KAAK,EAAE;QACdvC,SAAS,CAACuC,KAAK,CAAC,YAAY;MAC9B;IACF;IAEA,MAAMI,6BAA4B,GAAI,MAAAA,CAAA,KAAY;MAChD,IAAI;QACF,MAAMT,QAAO,GAAI,MAAMhC,OAAO,CAAC0C,sBAAsB,CAAC;UAAEC,IAAI,EAAE,CAAC;UAAEC,IAAI,EAAE;QAAE,CAAC;QAC1E;QACAtB,wBAAwB,CAACK,KAAI,GAAIK,QAAQ,EAAEa,KAAI,IAAK;MACtD,EAAE,OAAOR,KAAK,EAAE;QACdf,wBAAwB,CAACK,KAAI,GAAI;MACnC;IACF;IAEA,MAAMmB,aAAY,GAAKhB,MAAM,IAAK;MAChC,MAAMiB,SAAQ,GAAI;QAChB,SAAS,EAAE,SAAS;QACpB,UAAU,EAAE,SAAS;QACrB,UAAU,EAAE,QAAQ;QACpB,QAAQ,EAAE,SAAS;QACnB,WAAW,EAAE,MAAM;QACnB,YAAY,EAAE,SAAS;QACvB,SAAS,EAAE,SAAS;QACpB,WAAW,EAAE;MACf;MACA,OAAOA,SAAS,CAACjB,MAAM,KAAK,MAAK;IACnC;IAEA,MAAMkB,aAAY,GAAKlB,MAAM,IAAK;MAChC,MAAMiB,SAAQ,GAAI;QAChB,SAAS,EAAE,KAAK;QAChB,UAAU,EAAE,KAAK;QACjB,UAAU,EAAE,KAAK;QACjB,QAAQ,EAAE,KAAK;QACf,WAAW,EAAE;MACf;MACA,OAAOA,SAAS,CAACjB,MAAM,KAAKA,MAAK;IACnC;IAEA,MAAMmB,UAAS,GAAKC,UAAU,IAAK;MACjC,IAAI,CAACA,UAAU,EAAE,OAAO,EAAC;MACzB,OAAO,IAAIC,IAAI,CAACD,UAAU,CAAC,CAACE,kBAAkB,CAAC,OAAO;IACxD;IAEA,MAAMC,kBAAiB,GAAIA,CAAA,KAAM;MAC/B;MACApC,MAAM,CAACqC,IAAI,CAAC,+BAA+B;IAC7C;IAEA,MAAMC,UAAS,GAAIA,CAAA,KAAM;MACvB;MACAtC,MAAM,CAACqC,IAAI,CAAC,oBAAoBnC,IAAI,CAACQ,KAAK,CAACO,EAAE,SAAS;IACxD;IAEA,MAAMsB,gBAAe,GAAI,MAAAA,CAAA,KAAY;MACnC,IAAI;QACF,MAAMzD,YAAY,CAAC0D,OAAO,CACxB,2BAA2B,EAC3B,MAAM,EACN;UACEC,iBAAiB,EAAE,MAAM;UACzBC,gBAAgB,EAAE,IAAI;UACtBC,IAAI,EAAE;QACR,CACF;QAEA,MAAM5D,OAAO,CAAC6D,SAAS,CAAC1C,IAAI,CAACQ,KAAK,CAACO,EAAE;QACrCpC,SAAS,CAACgE,OAAO,CAAC,SAAS;QAC3B,MAAM/B,WAAW,CAAC,GAAE;MACtB,EAAE,OAAOM,KAAK,EAAE;QACd,IAAIA,KAAI,KAAM,QAAQ,EAAE;UACtB0B,OAAO,CAAC1B,KAAK,CAAC,SAAS,EAAEA,KAAK;UAC9BvC,SAAS,CAACuC,KAAK,CAAC,QAAQ;QAC1B;MACF;IACF;;IAEA;IACA,MAAM2B,cAAa,GAAKlC,MAAM,IAAK;MACjC,MAAMmC,QAAO,GAAI;QACf,QAAQ,EAAE,eAAe;QACzB,UAAU,EAAE,iBAAiB;QAC7B,WAAW,EAAE;MACf;MACA,OAAOA,QAAQ,CAACnC,MAAM,KAAK,gBAAe;IAC5C;;IAIA;IACA,MAAMoC,wBAAuB,GAAIA,CAAA,KAAM;MACrC,QAAQ/C,IAAI,CAACQ,KAAK,EAAEG,MAAM;QACxB,KAAK,SAAS;UACZ,OAAO,SAAQ;QACjB,KAAK,UAAU;UACb,OAAO,QAAO;QAChB;UACE,OAAO,MAAK;MAChB;IACF;;IAEA;IACA,MAAMqC,wBAAuB,GAAIA,CAAA,KAAM;MACrC,QAAQhD,IAAI,CAACQ,KAAK,EAAEG,MAAM;QACxB,KAAK,SAAS;UACZ,OAAO,OAAM;QACf,KAAK,UAAU;UACb,OAAO,OAAM;QACf;UACE,OAAO,MAAK;MAChB;IACF;;IAEA;IACA,MAAMsC,mBAAkB,GAAIA,CAAA,KAAM;MAChC,QAAQjD,IAAI,CAACQ,KAAK,EAAEG,MAAM;QACxB,KAAK,SAAS;UACZ,OAAO,SAAQ;QACjB,KAAK,UAAU;UACb,OAAO,SAAQ;QACjB;UACE,OAAO,MAAK;MAChB;IACF;;IAEA;IACA,MAAMuC,yBAAwB,GAAIA,CAAA,KAAM;MACtC,QAAQlD,IAAI,CAACQ,KAAK,EAAEG,MAAM;QACxB,KAAK,SAAS;UACZ,OAAO,qCAAoC;QAC7C,KAAK,UAAU;UACb,OAAO,0CAAyC;QAClD;UACE,OAAO,EAAC;MACZ;IACF;;IAEA;IACA,MAAMwC,kBAAiB,GAAIA,CAAA,KAAM;MAC/B,QAAQnD,IAAI,CAACQ,KAAK,EAAEG,MAAM;QACxB,KAAK,SAAS;UACZ,OAAO,SAAQ;QACjB,KAAK,UAAU;UACb,OAAO,OAAM;QACf;UACE,OAAO,MAAK;MAChB;IACF;IAEA,MAAMyC,QAAO,GAAIA,CAAA,KAAM;MACrBtD,MAAM,CAACqC,IAAI,CAAC,oBAAoBnC,IAAI,CAACQ,KAAK,CAACO,EAAE,OAAO;IACtD;IAMA,MAAMsC,mBAAkB,GAAI,MAAOC,MAAM,IAAK;MAC5CV,OAAO,CAACW,GAAG,CAAC,sBAAsB;MAClCX,OAAO,CAACW,GAAG,CAAC,WAAW,EAAED,MAAM;MAC/BV,OAAO,CAACW,GAAG,CAAC,YAAY,EAAED,MAAM,CAACvC,EAAE;MACnC6B,OAAO,CAACW,GAAG,CAAC,gBAAgB,EAAED,MAAM,CAACE,MAAM;MAC3CZ,OAAO,CAACW,GAAG,CAAC,SAAS,EAAElD,WAAW,CAACG,KAAK,EAAEO,EAAE;MAC5C6B,OAAO,CAACW,GAAG,CAAC,QAAQ,EAAEhD,QAAQ,CAACC,KAAK;MACpCoC,OAAO,CAACW,GAAG,CAAC,SAAS,EAAEtD,OAAO,CAACO,KAAK,CAACiD,MAAM;;MAE3C;MACA,MAAMC,cAAa,GAAIJ,MAAM,CAACvC,EAAC,KAAMV,WAAW,CAACG,KAAK,EAAEO,EAAC;MACzD,MAAM4C,YAAW,GAAI1D,OAAO,CAACO,KAAK,CAACiD,MAAK,KAAM;MAE9C,IAAIC,cAAa,IAAKnD,QAAQ,CAACC,KAAI,IAAKmD,YAAY,EAAE;QACpD;QACA,MAAMtB,gBAAgB,CAAC;QACvB;MACF;MAEA,IAAIqB,cAAa,IAAKnD,QAAQ,CAACC,KAAK,EAAE;QACpC;QACA7B,SAAS,CAACuC,KAAK,CAAC,wBAAwB;QACxC;MACF;MAEA,IAAI;QACF,MAAMtC,YAAY,CAAC0D,OAAO,CACxB,WAAWgB,MAAM,CAACM,QAAO,IAAKN,MAAM,CAACO,QAAQ,KAAK,EAClD,MAAM,EACN;UACEtB,iBAAiB,EAAE,MAAM;UACzBC,gBAAgB,EAAE,IAAI;UACtBC,IAAI,EAAE;QACR,CACF;QAEAG,OAAO,CAACW,GAAG,CAAC,iBAAiB,EAAEvD,IAAI,CAACQ,KAAK,CAACO,EAAE,EAAE,OAAO,EAAEuC,MAAM,CAACvC,EAAE;QAChE,MAAMlC,OAAO,CAACiF,gBAAgB,CAAC9D,IAAI,CAACQ,KAAK,CAACO,EAAE,EAAEuC,MAAM,CAACvC,EAAE;QACvDpC,SAAS,CAACgE,OAAO,CAAC,QAAQ;QAC1B,MAAM/B,WAAW,CAAC,GAAE;MACtB,EAAE,OAAOM,KAAK,EAAE;QACd,IAAIA,KAAI,KAAM,QAAQ,EAAE;UACtB0B,OAAO,CAAC1B,KAAK,CAAC,SAAS,EAAEA,KAAK;UAC9BvC,SAAS,CAACuC,KAAK,CAAC,UAAS,IAAKA,KAAK,CAACL,QAAQ,EAAEkD,IAAI,EAAE5C,OAAM,IAAKD,KAAK,CAACC,OAAM,IAAK,MAAM,CAAC;QACzF;MACF;IACF;IAIA5C,SAAS,CAAC,MAAM;MACdqC,WAAW,CAAC;IACd,CAAC;IAED,OAAO;MACLZ,IAAI;MACJC,OAAO;MACPC,OAAO;MACPC,wBAAwB;MACxBC,aAAa;MACbG,QAAQ;MACRF,WAAW;MACXK,eAAe;MACfiB,aAAa;MACbE,aAAa;MACbgB,cAAc;MACdE,wBAAwB;MACxBC,wBAAwB;MACxBC,mBAAmB;MACnBC,yBAAyB;MACzBC,kBAAkB;MAClBrB,UAAU;MACVI,kBAAkB;MAClBE,UAAU;MACVC,gBAAgB;MAChBe,QAAQ;MACRC,mBAAmB;MACnBpC,kBAAkB;MAClB;MACAzB,YAAY;MACZC;IACF;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}