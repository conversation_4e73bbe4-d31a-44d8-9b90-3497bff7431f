{"ast": null, "code": "import { createApp } from 'vue';\nimport App from './App.vue';\nimport router from './router';\nimport store from './store';\n\n// Element Plus 导入 - 暂时使用全量导入以避免构建问题\nimport ElementPlus, { ElMessage, ElMessageBox, ElNotification, ElLoading } from 'element-plus';\nimport 'element-plus/dist/index.css';\n\n// 图标导入\nimport * as ElementPlusIconsVue from '@element-plus/icons-vue';\nimport request from './utils/request';\n\n// 导入全局组件\nimport ErrorBoundary from './components/ErrorBoundary.vue';\nimport OfflineIndicator from './components/OfflineIndicator.vue';\n\n// 导入服务\n// import notificationService from './services/notificationService'\n// import performanceMonitor from './utils/performance'\n\nconst app = createApp(App);\n\n// 注册Element Plus图标\nfor (const [key, component] of Object.entries(ElementPlusIconsVue)) {\n  app.component(key, component);\n}\n\n// 注册全局组件\napp.component('ErrorBoundary', ErrorBoundary);\napp.component('OfflineIndicator', OfflineIndicator);\n\n// 全局错误处理\napp.config.errorHandler = (error, instance, info) => {\n  // 忽略 ResizeObserver 错误，这是一个常见的浏览器警告，不影响功能\n  if (error.message && error.message.includes('ResizeObserver loop completed with undelivered notifications')) {\n    return;\n  }\n  console.error('Global error:', error, info);\n  ElMessage.error('应用程序出现错误，请刷新页面重试');\n};\n\n// 忽略 ResizeObserver 错误\nwindow.addEventListener('error', event => {\n  if (event.message && event.message.includes('ResizeObserver loop completed with undelivered notifications')) {\n    event.preventDefault();\n    return false;\n  }\n});\n\n// 全局警告处理\napp.config.warnHandler = (msg, instance, trace) => {\n  console.warn('Global warning:', msg, trace);\n};\n\n// 全局配置\napp.config.globalProperties.$http = request;\napp.config.globalProperties.$message = ElMessage;\napp.config.globalProperties.$msgbox = ElMessageBox;\napp.config.globalProperties.$alert = ElMessageBox.alert;\napp.config.globalProperties.$confirm = ElMessageBox.confirm;\napp.config.globalProperties.$prompt = ElMessageBox.prompt;\napp.config.globalProperties.$notify = ElNotification;\napp.config.globalProperties.$loading = ElLoading.service;\n\n// 挂载应用\napp.use(store).use(router).use(ElementPlus, {\n  // 设置Element Plus的初始z-index\n  zIndex: 10000\n}).mount('#app');\n\n// 启动性能监控\n// performanceMonitor.startMonitoring()\n\n// 初始化通知服务\n// router.isReady().then(() => {\n//   // 等待路由准备就绪后再初始化通知服务\n//   if (store.getters.isAuthenticated) {\n//     notificationService.initialize()\n//   }\n\n//   // 生成页面加载性能报告\n//   // setTimeout(() => {\n//   //   performanceMonitor.generateReport()\n//   // }, 2000)\n// })\n\n// 监听登录状态变化\n// store.watch(\n//   (state) => state.isAuthenticated,\n//   (isAuthenticated) => {\n//     if (isAuthenticated) {\n//       notificationService.initialize()\n//     } else {\n//       notificationService.destroy()\n//     }\n//   }\n// )", "map": {"version": 3, "names": ["createApp", "App", "router", "store", "ElementPlus", "ElMessage", "ElMessageBox", "ElNotification", "ElLoading", "ElementPlusIconsVue", "request", "Error<PERSON>ou<PERSON><PERSON>", "OfflineIndicator", "app", "key", "component", "Object", "entries", "config", "<PERSON><PERSON><PERSON><PERSON>", "error", "instance", "info", "message", "includes", "console", "window", "addEventListener", "event", "preventDefault", "warn<PERSON><PERSON>ler", "msg", "trace", "warn", "globalProperties", "$http", "$message", "$msgbox", "$alert", "alert", "$confirm", "confirm", "$prompt", "prompt", "$notify", "$loading", "service", "use", "zIndex", "mount"], "sources": ["D:/workspace/idea/worker/work_cli/src/main.js"], "sourcesContent": ["import { createApp } from 'vue'\r\nimport App from './App.vue'\r\nimport router from './router'\r\nimport store from './store'\r\n\r\n// Element Plus 导入 - 暂时使用全量导入以避免构建问题\r\nimport ElementPlus, { ElMessage, ElMessageBox, ElNotification, ElLoading } from 'element-plus'\r\nimport 'element-plus/dist/index.css'\r\n\r\n// 图标导入\r\nimport * as ElementPlusIconsVue from '@element-plus/icons-vue'\r\n\r\nimport request from './utils/request'\r\n\r\n// 导入全局组件\r\nimport ErrorBoundary from './components/ErrorBoundary.vue'\r\nimport OfflineIndicator from './components/OfflineIndicator.vue'\r\n\r\n// 导入服务\r\n// import notificationService from './services/notificationService'\r\n// import performanceMonitor from './utils/performance'\r\n\r\nconst app = createApp(App)\r\n\r\n// 注册Element Plus图标\r\nfor (const [key, component] of Object.entries(ElementPlusIconsVue)) {\r\n  app.component(key, component)\r\n}\r\n\r\n// 注册全局组件\r\napp.component('ErrorBoundary', ErrorBoundary)\r\napp.component('OfflineIndicator', OfflineIndicator)\r\n\r\n// 全局错误处理\r\napp.config.errorHandler = (error, instance, info) => {\r\n  // 忽略 ResizeObserver 错误，这是一个常见的浏览器警告，不影响功能\r\n  if (error.message && error.message.includes('ResizeObserver loop completed with undelivered notifications')) {\r\n    return\r\n  }\r\n\r\n  console.error('Global error:', error, info)\r\n  ElMessage.error('应用程序出现错误，请刷新页面重试')\r\n}\r\n\r\n// 忽略 ResizeObserver 错误\r\nwindow.addEventListener('error', (event) => {\r\n  if (event.message && event.message.includes('ResizeObserver loop completed with undelivered notifications')) {\r\n    event.preventDefault()\r\n    return false\r\n  }\r\n})\r\n\r\n// 全局警告处理\r\napp.config.warnHandler = (msg, instance, trace) => {\r\n  console.warn('Global warning:', msg, trace)\r\n}\r\n\r\n// 全局配置\r\napp.config.globalProperties.$http = request\r\napp.config.globalProperties.$message = ElMessage\r\napp.config.globalProperties.$msgbox = ElMessageBox\r\napp.config.globalProperties.$alert = ElMessageBox.alert\r\napp.config.globalProperties.$confirm = ElMessageBox.confirm\r\napp.config.globalProperties.$prompt = ElMessageBox.prompt\r\napp.config.globalProperties.$notify = ElNotification\r\napp.config.globalProperties.$loading = ElLoading.service\r\n\r\n// 挂载应用\r\napp.use(store)\r\n   .use(router)\r\n   .use(ElementPlus, {\r\n     // 设置Element Plus的初始z-index\r\n     zIndex: 10000\r\n   })\r\n   .mount('#app')\r\n\r\n// 启动性能监控\r\n// performanceMonitor.startMonitoring()\r\n\r\n// 初始化通知服务\r\n// router.isReady().then(() => {\r\n//   // 等待路由准备就绪后再初始化通知服务\r\n//   if (store.getters.isAuthenticated) {\r\n//     notificationService.initialize()\r\n//   }\r\n\r\n//   // 生成页面加载性能报告\r\n//   // setTimeout(() => {\r\n//   //   performanceMonitor.generateReport()\r\n//   // }, 2000)\r\n// })\r\n\r\n// 监听登录状态变化\r\n// store.watch(\r\n//   (state) => state.isAuthenticated,\r\n//   (isAuthenticated) => {\r\n//     if (isAuthenticated) {\r\n//       notificationService.initialize()\r\n//     } else {\r\n//       notificationService.destroy()\r\n//     }\r\n//   }\r\n// )\r\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,KAAK;AAC/B,OAAOC,GAAG,MAAM,WAAW;AAC3B,OAAOC,MAAM,MAAM,UAAU;AAC7B,OAAOC,KAAK,MAAM,SAAS;;AAE3B;AACA,OAAOC,WAAW,IAAIC,SAAS,EAAEC,YAAY,EAAEC,cAAc,EAAEC,SAAS,QAAQ,cAAc;AAC9F,OAAO,6BAA6B;;AAEpC;AACA,OAAO,KAAKC,mBAAmB,MAAM,yBAAyB;AAE9D,OAAOC,OAAO,MAAM,iBAAiB;;AAErC;AACA,OAAOC,aAAa,MAAM,gCAAgC;AAC1D,OAAOC,gBAAgB,MAAM,mCAAmC;;AAEhE;AACA;AACA;;AAEA,MAAMC,GAAG,GAAGb,SAAS,CAACC,GAAG,CAAC;;AAE1B;AACA,KAAK,MAAM,CAACa,GAAG,EAAEC,SAAS,CAAC,IAAIC,MAAM,CAACC,OAAO,CAACR,mBAAmB,CAAC,EAAE;EAClEI,GAAG,CAACE,SAAS,CAACD,GAAG,EAAEC,SAAS,CAAC;AAC/B;;AAEA;AACAF,GAAG,CAACE,SAAS,CAAC,eAAe,EAAEJ,aAAa,CAAC;AAC7CE,GAAG,CAACE,SAAS,CAAC,kBAAkB,EAAEH,gBAAgB,CAAC;;AAEnD;AACAC,GAAG,CAACK,MAAM,CAACC,YAAY,GAAG,CAACC,KAAK,EAAEC,QAAQ,EAAEC,IAAI,KAAK;EACnD;EACA,IAAIF,KAAK,CAACG,OAAO,IAAIH,KAAK,CAACG,OAAO,CAACC,QAAQ,CAAC,8DAA8D,CAAC,EAAE;IAC3G;EACF;EAEAC,OAAO,CAACL,KAAK,CAAC,eAAe,EAAEA,KAAK,EAAEE,IAAI,CAAC;EAC3CjB,SAAS,CAACe,KAAK,CAAC,kBAAkB,CAAC;AACrC,CAAC;;AAED;AACAM,MAAM,CAACC,gBAAgB,CAAC,OAAO,EAAGC,KAAK,IAAK;EAC1C,IAAIA,KAAK,CAACL,OAAO,IAAIK,KAAK,CAACL,OAAO,CAACC,QAAQ,CAAC,8DAA8D,CAAC,EAAE;IAC3GI,KAAK,CAACC,cAAc,CAAC,CAAC;IACtB,OAAO,KAAK;EACd;AACF,CAAC,CAAC;;AAEF;AACAhB,GAAG,CAACK,MAAM,CAACY,WAAW,GAAG,CAACC,GAAG,EAAEV,QAAQ,EAAEW,KAAK,KAAK;EACjDP,OAAO,CAACQ,IAAI,CAAC,iBAAiB,EAAEF,GAAG,EAAEC,KAAK,CAAC;AAC7C,CAAC;;AAED;AACAnB,GAAG,CAACK,MAAM,CAACgB,gBAAgB,CAACC,KAAK,GAAGzB,OAAO;AAC3CG,GAAG,CAACK,MAAM,CAACgB,gBAAgB,CAACE,QAAQ,GAAG/B,SAAS;AAChDQ,GAAG,CAACK,MAAM,CAACgB,gBAAgB,CAACG,OAAO,GAAG/B,YAAY;AAClDO,GAAG,CAACK,MAAM,CAACgB,gBAAgB,CAACI,MAAM,GAAGhC,YAAY,CAACiC,KAAK;AACvD1B,GAAG,CAACK,MAAM,CAACgB,gBAAgB,CAACM,QAAQ,GAAGlC,YAAY,CAACmC,OAAO;AAC3D5B,GAAG,CAACK,MAAM,CAACgB,gBAAgB,CAACQ,OAAO,GAAGpC,YAAY,CAACqC,MAAM;AACzD9B,GAAG,CAACK,MAAM,CAACgB,gBAAgB,CAACU,OAAO,GAAGrC,cAAc;AACpDM,GAAG,CAACK,MAAM,CAACgB,gBAAgB,CAACW,QAAQ,GAAGrC,SAAS,CAACsC,OAAO;;AAExD;AACAjC,GAAG,CAACkC,GAAG,CAAC5C,KAAK,CAAC,CACV4C,GAAG,CAAC7C,MAAM,CAAC,CACX6C,GAAG,CAAC3C,WAAW,EAAE;EAChB;EACA4C,MAAM,EAAE;AACV,CAAC,CAAC,CACDC,KAAK,CAAC,MAAM,CAAC;;AAEjB;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}