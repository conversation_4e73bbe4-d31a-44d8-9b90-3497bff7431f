<template>
  <div class="task-review-container">
    <el-card class="page-card">
      <template #header>
        <div class="card-header">
          <h2>任务审核</h2>
          <p class="subtitle">审核学生提交的任务</p>
        </div>
      </template>

      <!-- 筛选条件 -->
      <div class="filter-section">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-select v-model="filterStatus" placeholder="选择状态" @change="loadSubmissions">
              <el-option label="全部" value="" />
              <el-option label="待审核" value="SUBMITTED" />
              <el-option label="已通过" value="COMPLETED" />
              <el-option label="已拒绝" value="REJECTED" />
            </el-select>
          </el-col>
          <el-col :span="6">
            <el-select v-model="filterProject" placeholder="选择项目" @change="loadSubmissions">
              <el-option label="全部项目" value="" />
              <el-option 
                v-for="project in myProjects" 
                :key="project.id" 
                :label="project.name || project.title" 
                :value="project.id" 
              />
            </el-select>
          </el-col>
          <el-col :span="6">
            <el-button type="primary" @click="loadSubmissions" :loading="loading">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </el-col>
        </el-row>
      </div>

      <!-- 提交列表 -->
      <div class="submissions-list">
        <div v-if="loading" class="loading-state">
          <el-skeleton :rows="5" animated />
        </div>

        <div v-else-if="submissions.length === 0" class="empty-state">
          <el-empty description="暂无任务提交">
            <el-button type="primary" @click="loadSubmissions">刷新数据</el-button>
          </el-empty>
        </div>

        <div v-else class="submission-items">
          <div v-for="submission in submissions" :key="submission.id" class="submission-item">
            <el-card class="submission-card" shadow="hover">
              <div class="submission-header">
                <div class="submission-info">
                  <h4 class="task-title">{{ submission.title }}</h4>
                  <div class="submission-meta">
                    <el-tag :type="getStatusColor(submission.status)" size="small">
                      {{ getStatusText(submission.status) }}
                    </el-tag>
                    <span class="submitter">提交人：{{ submission.creator?.realName }}</span>
                    <span class="submit-time">提交时间：{{ formatDate(submission.createTime) }}</span>
                  </div>
                </div>
                <div class="submission-actions">
                  <el-button size="small" @click="viewSubmission(submission)">查看详情</el-button>
                  <el-button 
                    v-if="submission.status === 'SUBMITTED'"
                    type="success" 
                    size="small" 
                    @click="approveSubmission(submission)"
                  >
                    通过
                  </el-button>
                  <el-button 
                    v-if="submission.status === 'SUBMITTED'"
                    type="danger" 
                    size="small" 
                    @click="rejectSubmission(submission)"
                  >
                    拒绝
                  </el-button>
                </div>
              </div>
              
              <div class="submission-content">
                <p class="content-preview">{{ submission.content }}</p>
                <div v-if="hasAttachments(submission)" class="attachments">
                  <el-icon><Paperclip /></el-icon>
                  <span>{{ getAttachmentCount(submission) }}个附件</span>
                  <el-button
                    type="text"
                    size="small"
                    @click="viewAttachments(submission)"
                    class="view-attachments-btn"
                  >
                    查看附件
                  </el-button>
                </div>
              </div>

              <!-- 原任务信息 -->
              <div v-if="submission.parentTask" class="parent-task">
                <div class="parent-task-header">
                  <el-icon><Document /></el-icon>
                  <span>原任务：{{ submission.parentTask.title }}</span>
                </div>
                <p class="parent-task-content">{{ submission.parentTask.content }}</p>
              </div>
            </el-card>
          </div>
        </div>
      </div>

      <!-- 分页 -->
      <div v-if="total > 0" class="pagination">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :total="total"
          :page-sizes="[10, 20, 50]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="loadSubmissions"
          @current-change="loadSubmissions"
        />
      </div>
    </el-card>

    <!-- 提交详情对话框 -->
    <el-dialog v-model="showDetailDialog" title="提交详情" width="800px">
      <div v-if="selectedSubmission" class="submission-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="任务标题" :span="2">
            {{ selectedSubmission.parentTask?.title || '未知任务' }}
          </el-descriptions-item>
          <el-descriptions-item label="提交标题" :span="2">
            {{ selectedSubmission.title }}
          </el-descriptions-item>
          <el-descriptions-item label="提交状态">
            <el-tag :type="getStatusColor(selectedSubmission.status)">
              {{ getStatusText(selectedSubmission.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="提交人">
            {{ selectedSubmission.creator?.realName }}
          </el-descriptions-item>
          <el-descriptions-item label="提交时间" :span="2">
            {{ formatDate(selectedSubmission.createTime) }}
          </el-descriptions-item>
          <el-descriptions-item label="提交内容" :span="2">
            <div class="submission-content-detail">
              {{ selectedSubmission.content }}
            </div>
          </el-descriptions-item>
          <el-descriptions-item v-if="hasAttachments(selectedSubmission)" label="附件列表" :span="2">
            <div class="attachment-list">
              <div
                v-for="(attachment, index) in parseAttachments(selectedSubmission.attachments)"
                :key="index"
                class="attachment-item"
              >
                <el-icon><Document /></el-icon>
                <span class="attachment-name">{{ attachment.name }}</span>
                <el-button
                  type="text"
                  size="small"
                  @click="downloadAttachment(attachment)"
                  class="download-btn"
                >
                  下载
                </el-button>
              </div>
            </div>
          </el-descriptions-item>
        </el-descriptions>

        <!-- 原任务详情 -->
        <div v-if="selectedSubmission.parentTask" class="original-task-section">
          <h4>原任务详情</h4>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="任务标题" :span="2">
              {{ selectedSubmission.parentTask.title }}
            </el-descriptions-item>
            <el-descriptions-item label="任务内容" :span="2">
              {{ selectedSubmission.parentTask.content }}
            </el-descriptions-item>
            <el-descriptions-item label="优先级">
              <el-tag :type="getPriorityColor(selectedSubmission.parentTask.priority)" size="small">
                {{ getPriorityText(selectedSubmission.parentTask.priority) }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="截止时间">
              {{ formatDate(selectedSubmission.parentTask.dueDate) }}
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showDetailDialog = false">关闭</el-button>
          <el-button 
            v-if="selectedSubmission?.status === 'SUBMITTED'"
            type="success" 
            @click="approveSubmission(selectedSubmission)"
          >
            通过审核
          </el-button>
          <el-button 
            v-if="selectedSubmission?.status === 'SUBMITTED'"
            type="danger" 
            @click="rejectSubmission(selectedSubmission)"
          >
            拒绝提交
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 附件查看对话框 -->
    <el-dialog v-model="showAttachmentDialog" title="附件列表" width="600px">
      <div v-if="selectedAttachments.length > 0" class="attachment-dialog">
        <div
          v-for="(attachment, index) in selectedAttachments"
          :key="index"
          class="attachment-dialog-item"
        >
          <div class="attachment-info">
            <el-icon size="20"><Document /></el-icon>
            <div class="attachment-details">
              <div class="attachment-name">{{ attachment.name }}</div>
              <div class="attachment-meta">
                <span v-if="attachment.size" class="file-size">{{ formatFileSize(attachment.size) }}</span>
                <span v-if="attachment.type" class="file-type">{{ attachment.type }}</span>
              </div>
            </div>
          </div>
          <div class="attachment-actions">
            <el-button
              type="primary"
              size="small"
              @click="downloadAttachment(attachment)"
            >
              <el-icon><Download /></el-icon>
              下载
            </el-button>
            <el-button
              v-if="isImageFile(attachment.name)"
              type="success"
              size="small"
              @click="previewAttachment(attachment)"
            >
              <el-icon><View /></el-icon>
              预览
            </el-button>
          </div>
        </div>
      </div>
      <div v-else class="no-attachments">
        <el-empty description="暂无附件" />
      </div>

      <template #footer>
        <el-button @click="showAttachmentDialog = false">关闭</el-button>
        <el-button
          v-if="selectedAttachments.length > 0"
          type="primary"
          @click="downloadAllAttachments"
        >
          下载全部
        </el-button>
      </template>
    </el-dialog>

    <!-- 图片预览对话框 -->
    <el-dialog v-model="showImagePreview" title="图片预览" width="80%">
      <div class="image-preview">
        <img
          v-if="previewImageUrl"
          :src="previewImageUrl"
          alt="预览图片"
          class="preview-image"
        />
      </div>
      <template #footer>
        <el-button @click="showImagePreview = false">关闭</el-button>
        <el-button
          type="primary"
          @click="downloadAttachment(currentPreviewAttachment)"
        >
          下载原图
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Refresh, Paperclip, Document, Download, View } from '@element-plus/icons-vue'
import { recordAPI, projectAPI, fileAPI } from '@/api'
import { useStore } from 'vuex'

export default {
  name: 'TaskReviewView',
  components: {
    Refresh,
    Paperclip,
    Document,
    Download,
    View
  },
  setup() {
    const store = useStore()
    const currentUser = computed(() => store.getters.user)

    // 响应式数据
    const loading = ref(false)
    const submissions = ref([])
    const myProjects = ref([])
    const total = ref(0)
    const currentPage = ref(1)
    const pageSize = ref(20)

    // 筛选条件
    const filterStatus = ref('SUBMITTED') // 默认显示待审核
    const filterProject = ref('')

    // 对话框
    const showDetailDialog = ref(false)
    const selectedSubmission = ref(null)

    // 附件相关
    const showAttachmentDialog = ref(false)
    const selectedAttachments = ref([])
    const showImagePreview = ref(false)
    const previewImageUrl = ref('')
    const currentPreviewAttachment = ref(null)

    // 加载我的项目
    const loadMyProjects = async () => {
      try {
        const response = await projectAPI.getMyProjects()
        myProjects.value = response?.records || []
      } catch (error) {
        console.error('加载项目失败:', error)
      }
    }

    // 加载提交列表
    const loadSubmissions = async () => {
      try {
        loading.value = true
        const params = {
          page: currentPage.value,
          size: pageSize.value,
          type: 'SUBMISSION',  // 查询提交类型的记录
          sortBy: 'createTime',  // 按创建时间排序
          sortDir: 'desc'
        }

        console.log('📡 加载提交记录，参数:', params)
        const response = await recordAPI.getRecords(params)
        console.log('📡 提交记录响应:', response)

        let allSubmissions = response?.records || []
        console.log('📋 原始提交记录:', allSubmissions)

        // 筛选提交记录（状态为SUBMITTED、COMPLETED、REJECTED、APPROVED）
        allSubmissions = allSubmissions.filter(submission =>
          ['SUBMITTED', 'COMPLETED', 'REJECTED', 'APPROVED'].includes(submission.status)
        )
        console.log('📋 筛选后的提交记录:', allSubmissions)

        // 前端筛选状态
        if (filterStatus.value) {
          allSubmissions = allSubmissions.filter(submission =>
            submission.status === filterStatus.value
          )
        }

        // 前端筛选项目
        if (filterProject.value) {
          allSubmissions = allSubmissions.filter(submission =>
            submission.projectId === filterProject.value
          )
        }

        submissions.value = allSubmissions
        total.value = allSubmissions.length

        console.log('加载的提交记录:', allSubmissions.map(submission => ({
          id: submission.id,
          title: submission.title,
          status: submission.status,
          parentId: submission.parentId,
          createTime: submission.createTime
        })))

        // 加载父任务信息和附件信息
        await loadParentTasks()
      } catch (error) {
        console.error('加载提交列表失败:', error)
        ElMessage.error('加载提交列表失败')
      } finally {
        loading.value = false
      }
    }

    // 加载原任务信息和附件信息
    const loadParentTasks = async () => {
      for (const submission of submissions.value) {
        // 加载原任务信息
        if (submission.parentId) {
          try {
            const parentTask = await recordAPI.getRecord(submission.parentId)
            submission.parentTask = parentTask
          } catch (error) {
            console.error('加载原任务失败:', error)
          }
        }

        // 加载附件信息
        if (submission.id) {
          try {
            console.log(`🔍 正在加载提交 ${submission.id} 的附件信息...`)
            const response = await fileAPI.getRecordFiles(submission.id)
            console.log(`📎 提交 ${submission.id} 的附件响应:`, response)

            const attachmentFiles = response?.data || response || []
            if (attachmentFiles && attachmentFiles.length > 0) {
              // 将真实的文件信息存储到submission中
              submission.attachmentFiles = attachmentFiles
              // 更新attachments字段为文件数量提示
              submission.attachments = `${attachmentFiles.length}个文件`
              console.log(`✅ 提交 ${submission.id} 加载了 ${attachmentFiles.length} 个附件`)

              // 验证文件信息完整性
              attachmentFiles.forEach((file, index) => {
                if (!file.id) {
                  console.warn(`⚠️ 文件 ${index} 缺少ID:`, file)
                }
              })
            } else {
              console.log(`ℹ️ 提交 ${submission.id} 没有附件`)
            }
          } catch (error) {
            console.error(`❌ 加载提交 ${submission.id} 的附件信息失败:`, error)
            console.error('❌ 错误详情:', {
              status: error.response?.status,
              message: error.message,
              data: error.response?.data
            })
          }
        }
      }
    }

    // 查看提交详情
    const viewSubmission = (submission) => {
      selectedSubmission.value = submission
      showDetailDialog.value = true
    }

    // 通过提交
    const approveSubmission = async (submission) => {
      try {
        await ElMessageBox.confirm(
          `确定要通过"${submission.title}"的提交吗？`,
          '通过审核',
          {
            confirmButtonText: '通过',
            cancelButtonText: '取消',
            type: 'success'
          }
        )

        await recordAPI.updateTaskStatus(submission.id, 'COMPLETED')
        ElMessage.success('审核通过')
        showDetailDialog.value = false
        loadSubmissions()
      } catch (error) {
        if (error !== 'cancel') {
          console.error('审核失败:', error)
          ElMessage.error('审核失败')
        }
      }
    }

    // 拒绝提交
    const rejectSubmission = async (submission) => {
      try {
        const { value: feedback } = await ElMessageBox.prompt(
          '请输入拒绝理由：',
          '拒绝提交',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            inputType: 'textarea',
            inputPlaceholder: '请详细说明拒绝的原因...'
          }
        )

        await recordAPI.updateTaskStatus(submission.id, 'REJECTED')
        ElMessage.success('已拒绝提交')
        showDetailDialog.value = false
        loadSubmissions()
      } catch (error) {
        if (error !== 'cancel') {
          console.error('拒绝失败:', error)
          ElMessage.error('拒绝失败')
        }
      }
    }

    // 状态相关方法
    const getStatusColor = (status) => {
      const colorMap = {
        'SUBMITTED': 'warning',
        'COMPLETED': 'success',
        'REJECTED': 'danger'
      }
      return colorMap[status] || 'info'
    }

    const getStatusText = (status) => {
      const textMap = {
        'SUBMITTED': '待审核',
        'COMPLETED': '已通过',
        'REJECTED': '已拒绝'
      }
      return textMap[status] || status
    }

    const getPriorityColor = (priority) => {
      if (typeof priority === 'number') {
        const numberToEnum = {
          1: 'LOW',
          2: 'MEDIUM',
          3: 'HIGH',
          4: 'URGENT',
          5: 'URGENT'
        }
        priority = numberToEnum[priority] || 'MEDIUM'
      }
      
      const colorMap = {
        'LOW': 'info',
        'MEDIUM': 'primary',
        'HIGH': 'warning',
        'URGENT': 'danger'
      }
      return colorMap[priority] || 'info'
    }

    const getPriorityText = (priority) => {
      if (typeof priority === 'number') {
        const numberToEnum = {
          1: 'LOW',
          2: 'MEDIUM',
          3: 'HIGH',
          4: 'URGENT',
          5: 'URGENT'
        }
        priority = numberToEnum[priority] || 'MEDIUM'
      }
      
      const textMap = {
        'LOW': '低',
        'MEDIUM': '中',
        'HIGH': '高',
        'URGENT': '紧急'
      }
      return textMap[priority] || '中'
    }

    // 格式化日期
    const formatDate = (dateString) => {
      if (!dateString) return '-'
      const date = new Date(dateString)
      return date.toLocaleString('zh-CN')
    }

    // 附件相关方法
    const hasAttachments = (submission) => {
      // 优先检查真实的文件信息
      if (submission.attachmentFiles && submission.attachmentFiles.length > 0) {
        return true
      }

      // 检查attachments字段
      const attachments = submission.attachments
      if (!attachments || attachments.trim() === '') {
        return false
      }

      try {
        // 如果是JSON格式
        if (attachments.startsWith('{') || attachments.startsWith('[')) {
          const parsed = JSON.parse(attachments)
          if (Array.isArray(parsed)) {
            return parsed.length > 0
          }
          return true
        }
        // 如果是分号分隔的文件名，检查是否有非空的文件名
        const fileNames = attachments.split(';').filter(name => name.trim())
        return fileNames.length > 0
      } catch (error) {
        // 如果解析失败，按分号分隔检查
        const fileNames = attachments.split(';').filter(name => name.trim())
        return fileNames.length > 0
      }
    }

    const getAttachmentCount = (submission) => {
      // 优先使用真实的文件信息
      if (submission.attachmentFiles && submission.attachmentFiles.length > 0) {
        return submission.attachmentFiles.length
      }

      // 回退到解析attachments字段
      const attachments = submission.attachments
      if (!attachments) return 0

      try {
        // 如果是JSON格式
        if (attachments.startsWith('{') || attachments.startsWith('[')) {
          const parsed = JSON.parse(attachments)
          if (Array.isArray(parsed)) {
            return parsed.length
          }
          return 1
        }
        // 如果是分号分隔的文件名
        return attachments.split(';').filter(name => name.trim()).length
      } catch (error) {
        // 如果解析失败，按分号分隔计算
        return attachments.split(';').filter(name => name.trim()).length
      }
    }

    const parseAttachments = (attachments) => {
      if (!attachments) return []

      try {
        // 尝试解析JSON格式
        if (attachments.startsWith('{') || attachments.startsWith('[')) {
          const parsed = JSON.parse(attachments)
          if (Array.isArray(parsed)) {
            return parsed.map(item => ({
              name: item.name || item.filename || item,
              id: item.id,
              url: item.url || item.path,
              size: item.size,
              type: item.type || item.contentType
            }))
          }
          return [{
            name: parsed.name || parsed.filename || '未知文件',
            id: parsed.id,
            url: parsed.url || parsed.path,
            size: parsed.size,
            type: parsed.type || parsed.contentType
          }]
        }

        // 按分号分隔的简单格式 - 需要查找文件ID
        return attachments.split(';')
          .filter(name => name.trim())
          .map(name => ({
            name: name.trim(),
            id: null,  // 需要通过API查找
            url: null,
            size: null,
            type: null,
            needsLookup: true  // 标记需要查找文件ID
          }))
      } catch (error) {
        console.error('解析附件信息失败:', error)
        return [{
          name: '附件解析失败',
          url: null,
          size: null,
          type: null
        }]
      }
    }

    const viewAttachments = async (submission) => {
      console.log('🔍 查看附件 - 提交记录:', submission)

      // 优先使用真实的文件信息
      if (submission.attachmentFiles && submission.attachmentFiles.length > 0) {
        console.log('✅ 使用已加载的附件文件信息:', submission.attachmentFiles)
        selectedAttachments.value = submission.attachmentFiles.map(file => ({
          id: file.id,
          name: file.originalName || file.fileName || file.name,
          url: `/api/files/${file.id}/download`,
          previewUrl: `/api/files/${file.id}/preview`,
          size: file.fileSize || file.size,
          type: file.fileType || file.contentType || file.type,
          uploadTime: file.uploadTime || file.createTime
        }))
      } else {
        console.log('🔄 需要查找附件文件信息...')

        // 首先尝试直接通过记录ID获取文件
        try {
          console.log(`📡 调用 fileAPI.getRecordFiles(${submission.id})`)
          const response = await fileAPI.getRecordFiles(submission.id)
          console.log('📡 API响应:', response)

          const files = response?.data || response || []
          if (files && files.length > 0) {
            console.log('✅ 成功获取文件列表:', files)
            selectedAttachments.value = files.map(file => ({
              id: file.id,
              name: file.originalName || file.fileName || file.name,
              url: `/api/files/${file.id}/download`,
              previewUrl: `/api/files/${file.id}/preview`,
              size: file.fileSize || file.size,
              type: file.fileType || file.contentType || file.type,
              uploadTime: file.uploadTime || file.createTime
            }))
          } else {
            console.log('⚠️ 未找到文件，尝试解析attachments字段')
            // 回退到解析attachments字段
            const parsedAttachments = parseAttachments(submission.attachments)
            console.log('📋 解析的附件信息:', parsedAttachments)

            // 查找需要查找ID的附件
            const attachmentsWithIds = await Promise.all(
              parsedAttachments.map(async (attachment) => {
                if (attachment.needsLookup) {
                  try {
                    console.log(`🔍 查找文件: ${attachment.name}`)
                    // 根据记录ID查找关联的文件
                    const files = await fileAPI.getFilesByRecord(submission.id)
                    console.log('🔍 查找结果:', files)

                    const fileList = files?.data || files || []
                    console.log('🔍 文件列表详情:', fileList)
                    console.log('🔍 文件列表长度:', fileList.length)

                    if (fileList.length > 0) {
                      console.log('🔍 可用文件名列表:')
                      fileList.forEach((file, index) => {
                        console.log(`  ${index}: originalName="${file.originalName}", fileName="${file.fileName}", name="${file.name}"`)
                      })
                    }

                    // 尝试多种匹配方式
                    let matchedFile = fileList.find(file =>
                      file.originalName === attachment.name ||
                      file.fileName === attachment.name ||
                      file.name === attachment.name
                    )

                    // 如果精确匹配失败，尝试模糊匹配
                    if (!matchedFile && fileList.length > 0) {
                      console.log('🔍 精确匹配失败，尝试模糊匹配...')
                      matchedFile = fileList.find(file => {
                        const fileName = file.originalName || file.fileName || file.name || ''
                        const attachmentName = attachment.name || ''
                        return fileName.toLowerCase().includes(attachmentName.toLowerCase()) ||
                               attachmentName.toLowerCase().includes(fileName.toLowerCase())
                      })

                      if (matchedFile) {
                        console.log('✅ 模糊匹配成功:', matchedFile)
                      }
                    }

                    // 如果还是没找到，尝试使用第一个文件（如果只有一个文件的话）
                    if (!matchedFile && fileList.length === 1) {
                      console.log('🔍 使用唯一文件作为匹配结果')
                      matchedFile = fileList[0]
                    }

                    if (matchedFile) {
                      console.log('✅ 找到匹配文件:', matchedFile)
                      return {
                        id: matchedFile.id,
                        name: matchedFile.originalName || matchedFile.fileName || matchedFile.name,
                        url: `/api/files/${matchedFile.id}/download`,
                        previewUrl: `/api/files/${matchedFile.id}/preview`,
                        size: matchedFile.fileSize || matchedFile.size,
                        type: matchedFile.fileType || matchedFile.contentType || matchedFile.type,
                        uploadTime: matchedFile.uploadTime || matchedFile.createTime
                      }
                    } else {
                      console.log('❌ 未找到匹配文件:', attachment.name)
                      console.log('❌ 目标文件名:', attachment.name)
                      console.log('❌ 可用文件:', fileList.map(f => f.originalName || f.fileName || f.name))
                    }
                  } catch (error) {
                    console.error('❌ 查找文件ID失败:', error)
                    console.error('❌ 错误详情:', {
                      message: error.message,
                      status: error.response?.status,
                      data: error.response?.data
                    })
                  }
                }
                return attachment
              })
            )

            selectedAttachments.value = attachmentsWithIds
          }
        } catch (error) {
          console.error('❌ 获取记录文件失败:', error)
          ElMessage.error('获取附件信息失败: ' + (error.message || '未知错误'))
          selectedAttachments.value = []
        }
      }

      console.log('📎 最终附件列表:', selectedAttachments.value)
      showAttachmentDialog.value = true
    }

    const downloadAttachment = async (attachment) => {
      console.log('📥 开始下载附件:', attachment)

      try {
        if (attachment.id) {
          console.log(`📡 使用文件ID下载: ${attachment.id}`)
          // 使用文件API下载
          const response = await fileAPI.downloadFile(attachment.id)

          console.log('📡 下载响应:', response)
          console.log('📡 响应类型:', typeof response)
          console.log('📡 是否为Blob:', response instanceof Blob)

          // 处理响应数据
          let blob
          if (response.data && response.data instanceof Blob) {
            // 如果response.data是Blob
            blob = response.data
          } else if (response instanceof Blob) {
            // 如果response本身是Blob
            blob = response
          } else {
            // 如果都不是，尝试创建Blob
            console.error('❌ 响应不是Blob格式:', response)
            ElMessage.error('文件下载失败：响应格式错误')
            return
          }

          // 创建下载链接
          const url = window.URL.createObjectURL(blob)
          const link = document.createElement('a')
          link.href = url
          link.download = attachment.name
          document.body.appendChild(link)
          link.click()
          document.body.removeChild(link)
          window.URL.revokeObjectURL(url)

          console.log('✅ 文件下载成功')
          ElMessage.success('文件下载成功')
        } else if (attachment.url) {
          console.log(`🔗 使用URL下载: ${attachment.url}`)
          // 如果有URL，直接下载
          const link = document.createElement('a')
          link.href = attachment.url
          link.download = attachment.name
          link.target = '_blank'  // 在新窗口打开，避免跨域问题
          document.body.appendChild(link)
          link.click()
          document.body.removeChild(link)

          console.log('✅ URL下载触发成功')
          ElMessage.success('文件下载已开始')
        } else {
          console.log('❌ 附件缺少ID和URL信息:', attachment)

          // 尝试通过当前选中的提交记录查找文件
          if (selectedSubmission.value && attachment.name) {
            console.log('🔄 尝试通过提交记录查找文件...')
            try {
              const files = await fileAPI.getRecordFiles(selectedSubmission.value.id)
              const fileList = files?.data || files || []
              console.log('🔍 查找到的文件列表:', fileList)

              // 查找匹配的文件
              let matchedFile = fileList.find(file =>
                file.originalName === attachment.name ||
                file.fileName === attachment.name ||
                file.name === attachment.name
              )

              // 模糊匹配
              if (!matchedFile) {
                matchedFile = fileList.find(file => {
                  const fileName = file.originalName || file.fileName || file.name || ''
                  return fileName.toLowerCase().includes(attachment.name.toLowerCase()) ||
                         attachment.name.toLowerCase().includes(fileName.toLowerCase())
                })
              }

              // 如果只有一个文件，直接使用
              if (!matchedFile && fileList.length === 1) {
                matchedFile = fileList[0]
              }

              if (matchedFile) {
                console.log('✅ 找到文件，尝试下载:', matchedFile)
                // 递归调用下载方法，这次使用找到的文件信息
                return await downloadAttachment({
                  id: matchedFile.id,
                  name: matchedFile.originalName || matchedFile.fileName || matchedFile.name,
                  url: `/api/files/${matchedFile.id}/download`,
                  size: matchedFile.fileSize || matchedFile.size,
                  type: matchedFile.fileType || matchedFile.contentType || matchedFile.type
                })
              } else {
                console.log('❌ 仍未找到匹配文件')
                ElMessage.warning(`附件"${attachment.name}"暂无下载链接。请尝试刷新页面或联系管理员。`)
              }
            } catch (error) {
              console.error('❌ 查找文件失败:', error)
              ElMessage.error('查找文件失败: ' + (error.message || '未知错误'))
            }
          } else {
            ElMessage.warning(`附件"${attachment.name}"暂无下载链接。请尝试刷新页面或联系管理员。`)
          }

          // 提供调试信息
          console.log('🔍 调试信息:')
          console.log('- 附件对象:', attachment)
          console.log('- 是否有ID:', !!attachment.id)
          console.log('- 是否有URL:', !!attachment.url)
          console.log('- 是否需要查找:', attachment.needsLookup)
          console.log('- 当前提交记录:', selectedSubmission.value)
        }
      } catch (error) {
        console.error('❌ 下载附件失败:', error)
        console.error('❌ 错误详情:', {
          message: error.message,
          status: error.response?.status,
          data: error.response?.data
        })

        // 根据错误类型提供不同的提示
        if (error.response?.status === 404) {
          ElMessage.error('文件不存在或已被删除')
        } else if (error.response?.status === 403) {
          ElMessage.error('没有权限下载此文件')
        } else if (error.response?.status === 401) {
          ElMessage.error('请先登录')
        } else {
          ElMessage.error('下载附件失败: ' + (error.message || '未知错误'))
        }
      }
    }

    const downloadAllAttachments = async () => {
      for (const attachment of selectedAttachments.value) {
        await downloadAttachment(attachment)
        // 添加延迟避免浏览器阻止多个下载
        await new Promise(resolve => setTimeout(resolve, 500))
      }
    }

    const isImageFile = (filename) => {
      if (!filename) return false
      const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg']
      const ext = filename.toLowerCase().substring(filename.lastIndexOf('.'))
      return imageExtensions.includes(ext)
    }

    const previewAttachment = async (attachment) => {
      try {
        if (attachment.id) {
          // 使用文件API预览
          const response = await fileAPI.previewFile(attachment.id)
          const blob = new Blob([response])
          previewImageUrl.value = window.URL.createObjectURL(blob)
          currentPreviewAttachment.value = attachment
          showImagePreview.value = true
        } else if (attachment.previewUrl || attachment.url) {
          previewImageUrl.value = attachment.previewUrl || attachment.url
          currentPreviewAttachment.value = attachment
          showImagePreview.value = true
        } else {
          ElMessage.warning('该图片暂无预览链接')
        }
      } catch (error) {
        console.error('预览图片失败:', error)
        ElMessage.error('预览图片失败')
      }
    }

    const formatFileSize = (bytes) => {
      if (!bytes) return '-'
      const sizes = ['B', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(1024))
      return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i]
    }

    // 生命周期
    onMounted(() => {
      loadMyProjects()
      loadSubmissions()
    })

    return {
      // 数据
      loading,
      submissions,
      myProjects,
      total,
      currentPage,
      pageSize,
      filterStatus,
      filterProject,
      showDetailDialog,
      selectedSubmission,
      showAttachmentDialog,
      selectedAttachments,
      showImagePreview,
      previewImageUrl,
      currentPreviewAttachment,
      currentUser,

      // 方法
      loadSubmissions,
      viewSubmission,
      approveSubmission,
      rejectSubmission,
      getStatusColor,
      getStatusText,
      getPriorityColor,
      getPriorityText,
      formatDate,

      // 附件方法
      hasAttachments,
      getAttachmentCount,
      parseAttachments,
      viewAttachments,
      downloadAttachment,
      downloadAllAttachments,
      isImageFile,
      previewAttachment,
      formatFileSize
    }
  }
}
</script>

<style scoped>
.task-review-container {
  padding: 20px;
}

.page-card {
  min-height: calc(100vh - 120px);
}

.card-header {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.card-header h2 {
  margin: 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.subtitle {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.filter-section {
  margin-bottom: 20px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.submissions-list {
  margin-bottom: 20px;
}

.submission-items {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.submission-card {
  transition: all 0.3s ease;
}

.submission-card:hover {
  transform: translateY(-2px);
}

.submission-header {
  display: flex;
  justify-content: between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.submission-info {
  flex: 1;
}

.task-title {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.submission-meta {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 12px;
  color: #909399;
}

.submission-actions {
  display: flex;
  gap: 8px;
}

.submission-content {
  margin-bottom: 12px;
}

.content-preview {
  margin: 0 0 8px 0;
  color: #606266;
  font-size: 14px;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.attachments {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #409eff;
  font-size: 12px;
}

.view-attachments-btn {
  padding: 0 4px;
  font-size: 12px;
  color: #409eff;
}

.view-attachments-btn:hover {
  color: #66b1ff;
}

.parent-task {
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
  border-left: 3px solid #409eff;
}

.parent-task-header {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-bottom: 8px;
  font-weight: 600;
  color: #303133;
  font-size: 14px;
}

.parent-task-content {
  margin: 0;
  color: #606266;
  font-size: 13px;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.pagination {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.submission-detail {
  max-height: 60vh;
  overflow-y: auto;
}

.submission-content-detail {
  max-height: 200px;
  overflow-y: auto;
  padding: 8px;
  background: #f8f9fa;
  border-radius: 4px;
  white-space: pre-wrap;
  line-height: 1.5;
}

.original-task-section {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

.original-task-section h4 {
  margin: 0 0 12px 0;
  color: #303133;
  font-size: 16px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.loading-state,
.empty-state {
  padding: 40px 20px;
  text-align: center;
}

/* 附件相关样式 */
.attachment-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.attachment-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
}

.attachment-name {
  flex: 1;
  font-size: 14px;
  color: #303133;
}

.download-btn {
  padding: 0 8px;
  font-size: 12px;
}

.attachment-dialog {
  max-height: 400px;
  overflow-y: auto;
}

.attachment-dialog-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  margin-bottom: 12px;
  transition: all 0.3s ease;
}

.attachment-dialog-item:hover {
  border-color: #409eff;
  background: #f0f9ff;
}

.attachment-info {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.attachment-details {
  flex: 1;
}

.attachment-name {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
}

.attachment-meta {
  display: flex;
  gap: 12px;
  font-size: 12px;
  color: #909399;
}

.file-size,
.file-type {
  padding: 2px 6px;
  background: #f0f2f5;
  border-radius: 4px;
}

.attachment-actions {
  display: flex;
  gap: 8px;
}

.no-attachments {
  padding: 40px 20px;
  text-align: center;
}

.image-preview {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
}

.preview-image {
  max-width: 100%;
  max-height: 70vh;
  object-fit: contain;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}
</style>
