{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport \"core-js/modules/es.iterator.reduce.js\";\nimport { ref, reactive, onMounted, computed, watch, nextTick } from 'vue';\nimport { useStore } from 'vuex';\nimport { useRouter } from 'vue-router';\nimport { recordAPI, teamAPI, projectAPI } from '@/api';\nimport { ElMessage, ElMessageBox } from 'element-plus';\nimport { Plus, ArrowDown, UploadFilled, Refresh, Calendar, User } from '@element-plus/icons-vue';\nexport default {\n  name: 'TaskManagementView',\n  components: {\n    Plus,\n    ArrowDown,\n    UploadFilled,\n    Refresh,\n    Calendar,\n    User\n  },\n  setup() {\n    const store = useStore();\n    const router = useRouter();\n    const formRef = ref();\n    const loading = ref(false);\n    const submitting = ref(false);\n    const showCreateDialog = ref(false);\n    const showDetailDialog = ref(false);\n    const showSubmitTaskDialog = ref(false);\n    const editingTask = ref(null);\n    const selectedTask = ref(null);\n    const progressValue = ref(0);\n\n    // 任务提交表单\n    const submitForm = reactive({\n      content: '',\n      fileList: []\n    });\n    const submitFormRules = {\n      content: [{\n        required: true,\n        message: '请输入提交说明',\n        trigger: 'blur'\n      }]\n    };\n    const tasks = ref([]);\n    const myTeams = ref([]);\n    const currentTeamId = ref('');\n    const activeTab = ref('all');\n    const currentPage = ref(1);\n    const pageSize = ref(10);\n    const total = ref(0);\n    const taskForm = reactive({\n      title: '',\n      description: '',\n      priority: 'MEDIUM',\n      deadline: null\n    });\n    const formRules = {\n      title: [{\n        required: true,\n        message: '请输入任务标题',\n        trigger: 'blur'\n      }, {\n        min: 2,\n        max: 100,\n        message: '标题长度在 2 到 100 个字符',\n        trigger: 'blur'\n      }],\n      description: [{\n        required: true,\n        message: '请输入任务描述',\n        trigger: 'blur'\n      }],\n      deadline: [{\n        required: true,\n        message: '请选择截止时间',\n        trigger: 'change'\n      }]\n    };\n    const currentUser = computed(() => store.getters.currentUser);\n    const isTeacher = computed(() => currentUser.value?.role === 'TEACHER');\n\n    // 跳转到任务发布页面\n    const goToTaskPublish = () => {\n      router.push('/dashboard/task-publish');\n    };\n\n    // 任务统计\n    const taskStats = computed(() => {\n      const total = tasks.value.length;\n      const completed = tasks.value.filter(t => t.status === 'COMPLETED').length;\n      const inProgress = tasks.value.filter(t => t.status === 'IN_PROGRESS').length;\n      const completionRate = total > 0 ? Math.round(completed / total * 100) : 0;\n      return {\n        total,\n        completed,\n        inProgress,\n        completionRate\n      };\n    });\n\n    // 教师端：按项目和团队分组的任务数据\n    const groupedTasks = computed(() => {\n      if (!isTeacher.value || tasks.value.length === 0) {\n        return [];\n      }\n\n      // 按项目分组\n      const projectGroups = {};\n      tasks.value.forEach(task => {\n        const projectId = task.projectId || 'unknown';\n        const projectName = task.projectName || '未知项目';\n        const teamId = task.teamId || 'unknown';\n        const teamName = task.teamName || '未知团队';\n        if (!projectGroups[projectId]) {\n          projectGroups[projectId] = {\n            projectId,\n            projectName,\n            teams: {},\n            totalTasks: 0,\n            completedTasks: 0\n          };\n        }\n        if (!projectGroups[projectId].teams[teamId]) {\n          projectGroups[projectId].teams[teamId] = {\n            teamId,\n            teamName,\n            tasks: []\n          };\n        }\n        projectGroups[projectId].teams[teamId].tasks.push(task);\n        projectGroups[projectId].totalTasks++;\n        if (task.status === 'COMPLETED') {\n          projectGroups[projectId].completedTasks++;\n        }\n      });\n\n      // 转换为数组格式\n      return Object.values(projectGroups).map(project => ({\n        ...project,\n        teams: Object.values(project.teams)\n      }));\n    });\n\n    // 加载我的团队\n    const loadMyTeams = async () => {\n      try {\n        console.log('=== 团队加载调试信息 ===');\n        console.log('当前用户:', currentUser.value);\n        console.log('用户角色:', currentUser.value?.role);\n        if (currentUser.value?.role === 'TEACHER') {\n          // 教师：获取自己项目的团队\n          await loadTeacherProjectTeams();\n        } else {\n          // 学生：获取参与的团队\n          console.log('🔍 学生获取团队列表...');\n          const response = await teamAPI.getJoinedTeams();\n          console.log('✅ 学生团队响应:', response);\n          myTeams.value = response?.records || [];\n          console.log('📋 学生团队列表:', myTeams.value.map(team => ({\n            id: team.id,\n            name: team.name,\n            projectId: team.projectId,\n            projectName: team.projectName,\n            status: team.status\n          })));\n        }\n        console.log('团队列表:', myTeams.value);\n        console.log('团队数量:', myTeams.value.length);\n        if (myTeams.value.length > 0) {\n          currentTeamId.value = myTeams.value[0].id;\n          console.log('选中的团队ID:', currentTeamId.value);\n          loadTasks();\n        } else {\n          console.log('没有找到任何团队');\n        }\n      } catch (error) {\n        console.error('加载团队列表失败:', error);\n        ElMessage.error('加载团队列表失败');\n      }\n    };\n\n    // 加载教师项目的团队\n    const loadTeacherProjectTeams = async () => {\n      try {\n        // 1. 先获取教师自己的项目列表\n        const projectsResponse = await projectAPI.getMyProjects();\n        console.log('教师项目响应:', projectsResponse);\n        const myProjects = projectsResponse?.records || [];\n        console.log('教师项目列表:', myProjects);\n        if (myProjects.length === 0) {\n          console.log('教师没有发布任何项目');\n          myTeams.value = [];\n          return;\n        }\n\n        // 2. 获取每个项目的团队列表\n        const allTeams = [];\n        for (const project of myProjects) {\n          try {\n            console.log(`=== 开始获取项目 ${project.name} (ID: ${project.id}) 的团队 ===`);\n            const teamsResponse = await teamAPI.getProjectTeams(project.id);\n            console.log(`项目 ${project.name} 的团队响应:`, teamsResponse);\n            console.log('响应类型:', typeof teamsResponse);\n            console.log('响应结构:', JSON.stringify(teamsResponse, null, 2));\n            const projectTeams = teamsResponse?.records || [];\n            console.log(`项目 ${project.name} 的团队数量:`, projectTeams.length);\n\n            // 为每个团队添加项目信息，便于显示\n            const teamsWithProject = projectTeams.map(team => ({\n              ...team,\n              projectName: project.name,\n              projectId: project.id\n            }));\n            allTeams.push(...teamsWithProject);\n            console.log(`项目 ${project.name} 处理完成，累计团队数:`, allTeams.length);\n          } catch (error) {\n            console.error(`=== 获取项目 ${project.name} 的团队失败 ===`);\n            console.error('错误对象:', error);\n            console.error('错误消息:', error.message);\n            console.error('错误响应:', error.response);\n            console.error('错误状态:', error.response?.status);\n            console.error('错误数据:', error.response?.data);\n          }\n        }\n        console.log('教师所有项目的团队:', allTeams);\n        myTeams.value = allTeams;\n      } catch (error) {\n        console.error('加载教师项目团队失败:', error);\n        throw error;\n      }\n    };\n\n    // 加载任务列表\n    const loadTasks = async () => {\n      if (!currentTeamId.value) {\n        console.log('❌ 没有选择团队，无法加载任务');\n        return;\n      }\n      try {\n        loading.value = true;\n        const params = {\n          page: currentPage.value,\n          size: pageSize.value,\n          type: 'TASK',\n          // 只获取任务类型的记录\n          sortBy: 'createTime',\n          sortDir: 'desc'\n        };\n        console.log('=== 任务加载调试信息 ===');\n        console.log('当前用户:', currentUser.value);\n        console.log('当前团队ID:', currentTeamId.value);\n        console.log('所有团队:', myTeams.value);\n        console.log('请求参数:', params);\n        console.log('请求URL:', `/records/teams/${currentTeamId.value}`);\n\n        // 获取团队任务\n        const response = await recordAPI.getTeamRecords(currentTeamId.value, params);\n        console.log('✅ 团队任务API响应:', response);\n        console.log('响应数据结构:', {\n          hasRecords: !!response?.records,\n          recordsLength: response?.records?.length || 0,\n          totalElements: response?.totalElements,\n          totalPages: response?.totalPages,\n          currentPage: response?.currentPage\n        });\n        let allTasks = response?.records || [];\n        console.log('📋 获取到的任务数量:', allTasks.length);\n\n        // 显示任务的详细信息\n        if (allTasks.length > 0) {\n          console.log('任务详情:', allTasks.map(task => ({\n            id: task.id,\n            title: task.title,\n            type: task.type,\n            status: task.status,\n            teamId: task.teamId,\n            projectId: task.projectId\n          })));\n          console.log('任务状态分布:', allTasks.reduce((acc, task) => {\n            acc[task.status] = (acc[task.status] || 0) + 1;\n            return acc;\n          }, {}));\n        } else {\n          console.log('❌ 没有获取到任何任务数据');\n\n          // 尝试获取所有任务进行对比\n          try {\n            console.log('🔍 尝试获取所有任务进行对比...');\n            const allTasksResponse = await recordAPI.getRecords({\n              type: 'TASK',\n              page: 1,\n              size: 50\n            });\n            console.log('所有任务响应:', allTasksResponse);\n            const allTasksInSystem = allTasksResponse?.records || [];\n            console.log('系统中所有任务:', allTasksInSystem.map(task => ({\n              id: task.id,\n              title: task.title,\n              teamId: task.teamId,\n              projectId: task.projectId,\n              status: task.status,\n              creator: task.creator?.realName\n            })));\n\n            // 调用调试接口\n            console.log('🔍 调用调试接口获取任务详情...');\n            const debugResponse = await fetch('/api/records/debug/tasks', {\n              headers: {\n                'Authorization': `Bearer ${localStorage.getItem('token')}`\n              }\n            });\n            const debugData = await debugResponse.json();\n            console.log('调试接口响应:', debugData);\n          } catch (error) {\n            console.log('获取所有任务失败:', error);\n          }\n        }\n\n        // 为缺少status字段的任务添加默认状态\n        allTasks = allTasks.map(task => ({\n          ...task,\n          status: task.status || 'PUBLISHED',\n          // 默认状态为PUBLISHED（已发布）\n          deadline: task.dueDate || null,\n          // 使用dueDate字段\n          assignee: task.assignee || null,\n          progress: task.progress || 0\n        }));\n\n        // 前端过滤任务状态\n        console.log('当前选中的标签页:', activeTab.value);\n        console.log('过滤前所有任务:', allTasks.map(task => ({\n          id: task.id,\n          title: task.title,\n          status: task.status\n        })));\n        if (activeTab.value !== 'all') {\n          const filteredTasks = allTasks.filter(task => task.status === activeTab.value);\n          console.log(`过滤前任务数量: ${allTasks.length}, 过滤后任务数量: ${filteredTasks.length}`);\n          console.log('过滤后的任务:', filteredTasks.map(task => ({\n            id: task.id,\n            title: task.title,\n            status: task.status\n          })));\n          allTasks = filteredTasks;\n        }\n\n        // 强制触发响应式更新\n        tasks.value = [];\n        await nextTick();\n        tasks.value = [...allTasks];\n        total.value = allTasks.length;\n      } catch (error) {\n        console.error('=== 任务加载错误信息 ===');\n        console.error('错误对象:', error);\n        console.error('错误消息:', error.message);\n        console.error('错误响应:', error.response);\n        console.error('错误状态:', error.response?.status);\n        console.error('错误数据:', error.response?.data);\n        ElMessage.error('加载任务失败: ' + (error.response?.data?.message || error.message || '未知错误'));\n      } finally {\n        loading.value = false;\n      }\n    };\n\n    // 提交任务\n    const submitTask = async () => {\n      if (!formRef.value) return;\n      try {\n        await formRef.value.validate();\n        submitting.value = true;\n        const taskData = {\n          title: taskForm.title,\n          content: taskForm.description,\n          priority: getPriorityNumber(taskForm.priority),\n          dueDate: taskForm.deadline ? new Date(taskForm.deadline).toISOString() : null\n        };\n\n        // 创建任务时需要额外的字段\n        if (!editingTask.value) {\n          taskData.type = 'TASK';\n          taskData.teamId = currentTeamId.value;\n        }\n        console.log('提交任务数据:', taskData);\n        console.log('是否为编辑模式:', !!editingTask.value);\n        if (editingTask.value) {\n          console.log('更新任务ID:', editingTask.value.id);\n          await recordAPI.updateRecord(editingTask.value.id, taskData);\n          ElMessage.success('任务更新成功');\n        } else {\n          console.log('创建新任务');\n          await recordAPI.createRecord(taskData);\n          ElMessage.success('任务创建成功');\n        }\n        showCreateDialog.value = false;\n        resetForm();\n        loadTasks();\n      } catch (error) {\n        console.error('提交任务失败:', error);\n        console.error('错误详情:', error.response?.data);\n        const errorMessage = error.response?.data?.message || error.message || '提交任务失败';\n        ElMessage.error(errorMessage);\n      } finally {\n        submitting.value = false;\n      }\n    };\n\n    // 重置表单\n    const resetForm = () => {\n      Object.assign(taskForm, {\n        title: '',\n        description: '',\n        priority: 'MEDIUM',\n        deadline: null\n      });\n      editingTask.value = null;\n    };\n\n    // 查看任务详情\n    const viewTask = task => {\n      selectedTask.value = task;\n      progressValue.value = task.progress || 0;\n      showDetailDialog.value = true;\n    };\n\n    // 编辑任务\n    const editTask = task => {\n      editingTask.value = task;\n      Object.assign(taskForm, {\n        title: task.title,\n        description: task.content,\n        priority: task.priority,\n        deadline: task.dueDate ? new Date(task.dueDate) : null\n      });\n      showCreateDialog.value = true;\n    };\n\n    // 处理任务操作\n    const handleTaskAction = async ({\n      action,\n      task\n    }) => {\n      try {\n        let message = '';\n        let status = '';\n        switch (action) {\n          case 'start':\n            status = 'IN_PROGRESS';\n            message = '任务已开始';\n            break;\n          case 'submit':\n            // 学生提交任务，显示提交对话框\n            showSubmitDialog(task);\n            return;\n          case 'approve':\n            // 教师审核通过任务\n            const approveParams = new URLSearchParams();\n            approveParams.append('approved', 'true');\n            await recordAPI.reviewTask(task.id, approveParams);\n            ElMessage.success('任务审核通过');\n            loadTasks();\n            return;\n          case 'reject':\n            // 教师拒绝任务，需要提供反馈\n            const feedback = await ElMessageBox.prompt('请输入拒绝理由', '任务审核', {\n              confirmButtonText: '确定',\n              cancelButtonText: '取消',\n              inputType: 'textarea'\n            });\n            const rejectParams = new URLSearchParams();\n            rejectParams.append('approved', 'false');\n            rejectParams.append('feedback', feedback.value || '');\n            await recordAPI.reviewTask(task.id, rejectParams);\n            ElMessage.success('任务已拒绝，学生需要重新完成');\n            loadTasks();\n            return;\n          case 'activate':\n            // 教师激活任务\n            await recordAPI.updateTaskStatus(task.id, 'ACTIVE');\n            ElMessage.success('任务已激活');\n            loadTasks();\n            return;\n          case 'cancel':\n            status = 'CANCELLED';\n            message = '任务已取消';\n            break;\n          case 'delete':\n            await ElMessageBox.confirm('确定要删除这个任务吗？', '确认删除', {\n              confirmButtonText: '确定',\n              cancelButtonText: '取消',\n              type: 'warning'\n            });\n            await recordAPI.deleteRecord(task.id);\n            ElMessage.success('任务删除成功');\n            loadTasks();\n            return;\n        }\n        if (status) {\n          await recordAPI.updateRecord(task.id, {\n            status\n          });\n          ElMessage.success(message);\n          loadTasks();\n        }\n      } catch (error) {\n        if (error !== 'cancel') {\n          console.error('任务操作失败:', error);\n          ElMessage.error('操作失败');\n        }\n      }\n    };\n\n    // 更新进度\n    const updateProgress = async value => {\n      if (!selectedTask.value) return;\n      try {\n        await recordAPI.updateRecord(selectedTask.value.id, {\n          progress: value\n        });\n        ElMessage.success('进度更新成功');\n        loadTasks();\n      } catch (error) {\n        console.error('更新进度失败:', error);\n        ElMessage.error('更新进度失败');\n      }\n    };\n\n    // 开始任务\n    const startTask = async task => {\n      try {\n        await ElMessageBox.confirm(`确定要开始任务\"${task.title}\"吗？开始后任务状态将变为活跃状态。`, '开始任务', {\n          confirmButtonText: '开始',\n          cancelButtonText: '取消',\n          type: 'info'\n        });\n\n        // 调用API更新任务状态为ACTIVE\n        await recordAPI.updateTaskStatus(task.id, 'ACTIVE');\n        ElMessage.success('任务已开始');\n        loadTasks(); // 刷新任务列表\n      } catch (error) {\n        if (error !== 'cancel') {\n          console.error('开始任务失败:', error);\n          ElMessage.error('开始任务失败');\n        }\n      }\n    };\n\n    // 显示任务提交对话框\n    const showSubmitDialog = task => {\n      selectedTask.value = task;\n      submitForm.content = '';\n      submitForm.fileList = [];\n      showSubmitTaskDialog.value = true;\n    };\n\n    // 文件上传处理\n    const uploadRef = ref();\n    const handleFileChange = (_, fileList) => {\n      submitForm.fileList = fileList;\n    };\n    const handleFileRemove = (_, fileList) => {\n      submitForm.fileList = fileList;\n    };\n    const beforeUpload = file => {\n      const isLt10M = file.size / 1024 / 1024 < 10;\n      if (!isLt10M) {\n        ElMessage.error('文件大小不能超过10MB!');\n        return false;\n      }\n      return true;\n    };\n\n    // 提交任务完成\n    const submitTaskCompletion = async () => {\n      try {\n        submitting.value = true;\n\n        // 使用FormData支持文件上传\n        const formData = new FormData();\n        formData.append('submissionContent', submitForm.content);\n\n        // 添加文件\n        if (submitForm.fileList && submitForm.fileList.length > 0) {\n          submitForm.fileList.forEach(fileItem => {\n            if (fileItem.raw) {\n              formData.append('files', fileItem.raw);\n            }\n          });\n        }\n        await recordAPI.submitTaskWithFiles(selectedTask.value.id, formData);\n        ElMessage.success('任务提交成功');\n        showSubmitTaskDialog.value = false;\n        loadTasks();\n      } catch (error) {\n        console.error('任务提交失败:', error);\n        ElMessage.error('任务提交失败');\n      } finally {\n        submitting.value = false;\n      }\n    };\n\n    // 权限检查\n    const canEdit = task => {\n      return task.creator?.id === currentUser.value?.id || currentUser.value?.role === 'TEACHER';\n    };\n    const canManage = task => {\n      return task.creator?.id === currentUser.value?.id || currentUser.value?.role === 'TEACHER';\n    };\n\n    // 工具方法\n    const formatDate = date => {\n      if (!date) return '';\n      return new Date(date).toLocaleString('zh-CN');\n    };\n    const isOverdue = deadline => {\n      return new Date(deadline) < new Date();\n    };\n    const getStatusColor = status => {\n      const colorMap = {\n        'ACTIVE': 'success',\n        'PUBLISHED': 'info',\n        'IN_PROGRESS': 'primary',\n        'SUBMITTED': 'warning',\n        'COMPLETED': 'success',\n        'CANCELLED': 'danger',\n        'REJECTED': 'danger',\n        'APPROVED': 'success',\n        'DRAFT': 'info'\n      };\n      return colorMap[status] || 'info'; // 默认返回 'info' 而不是空字符串\n    };\n    const getStatusText = status => {\n      const textMap = {\n        'ACTIVE': '活跃',\n        'PUBLISHED': '已发布',\n        'IN_PROGRESS': '进行中',\n        'SUBMITTED': '待审核',\n        'COMPLETED': '已完成',\n        'CANCELLED': '已取消',\n        'REJECTED': '已拒绝',\n        'APPROVED': '已通过',\n        'DRAFT': '草稿'\n      };\n      return textMap[status] || status;\n    };\n    const getPriorityColor = priority => {\n      // 处理数字类型的优先级\n      let priorityKey = priority;\n      if (typeof priority === 'number') {\n        const numberToEnum = {\n          1: 'LOW',\n          2: 'MEDIUM',\n          3: 'HIGH',\n          4: 'URGENT',\n          5: 'URGENT'\n        };\n        priorityKey = numberToEnum[priority] || 'MEDIUM';\n      }\n      const colorMap = {\n        'LOW': 'info',\n        'MEDIUM': 'primary',\n        'HIGH': 'warning',\n        'URGENT': 'danger'\n      };\n      return colorMap[priorityKey] || 'info'; // 默认返回 'info' 而不是空字符串\n    };\n    const getPriorityText = priority => {\n      // 处理数字类型的优先级\n      let priorityKey = priority;\n      if (typeof priority === 'number') {\n        const numberToEnum = {\n          1: 'LOW',\n          2: 'MEDIUM',\n          3: 'HIGH',\n          4: 'URGENT',\n          5: 'URGENT'\n        };\n        priorityKey = numberToEnum[priority] || 'MEDIUM';\n      }\n      const textMap = {\n        'LOW': '低',\n        'MEDIUM': '中',\n        'HIGH': '高',\n        'URGENT': '紧急'\n      };\n      return textMap[priorityKey] || '中'; // 默认返回 '中'\n    };\n    const getPriorityNumber = priority => {\n      const numberMap = {\n        'LOW': 1,\n        'MEDIUM': 2,\n        'HIGH': 3,\n        'URGENT': 4\n      };\n      return numberMap[priority] || 2;\n    };\n\n    // 监听团队变化\n    watch(currentTeamId, newTeamId => {\n      if (newTeamId) {\n        loadTasks();\n      }\n    });\n    onMounted(() => {\n      loadTasks();\n      loadMyTeams();\n    });\n    return {\n      loading,\n      submitting,\n      showCreateDialog,\n      showDetailDialog,\n      showSubmitTaskDialog,\n      editingTask,\n      selectedTask,\n      progressValue,\n      submitForm,\n      submitFormRules,\n      tasks,\n      myTeams,\n      currentTeamId,\n      activeTab,\n      currentPage,\n      pageSize,\n      total,\n      taskForm,\n      formRules,\n      formRef,\n      currentUser,\n      isTeacher,\n      taskStats,\n      groupedTasks,\n      goToTaskPublish,\n      loadTasks,\n      submitTask,\n      viewTask,\n      editTask,\n      handleTaskAction,\n      updateProgress,\n      startTask,\n      showSubmitDialog,\n      submitTaskCompletion,\n      uploadRef,\n      handleFileChange,\n      handleFileRemove,\n      beforeUpload,\n      canEdit,\n      canManage,\n      formatDate,\n      isOverdue,\n      getStatusColor,\n      getStatusText,\n      getPriorityColor,\n      getPriorityText,\n      getPriorityNumber\n    };\n  }\n};", "map": {"version": 3, "names": ["ref", "reactive", "onMounted", "computed", "watch", "nextTick", "useStore", "useRouter", "recordAPI", "teamAPI", "projectAPI", "ElMessage", "ElMessageBox", "Plus", "ArrowDown", "UploadFilled", "Refresh", "Calendar", "User", "name", "components", "setup", "store", "router", "formRef", "loading", "submitting", "showCreateDialog", "showDetailDialog", "showSubmitTaskDialog", "editingTask", "selectedTask", "progressValue", "submitForm", "content", "fileList", "submitFormRules", "required", "message", "trigger", "tasks", "myTeams", "currentTeamId", "activeTab", "currentPage", "pageSize", "total", "taskForm", "title", "description", "priority", "deadline", "formRules", "min", "max", "currentUser", "getters", "<PERSON><PERSON><PERSON>er", "value", "role", "goToTaskPublish", "push", "taskStats", "length", "completed", "filter", "t", "status", "inProgress", "completionRate", "Math", "round", "groupedTasks", "projectGroups", "for<PERSON>ach", "task", "projectId", "projectName", "teamId", "teamName", "teams", "totalTasks", "completedTasks", "Object", "values", "map", "project", "loadMyTeams", "console", "log", "loadTeacherProjectTeams", "response", "getJoinedTeams", "records", "team", "id", "loadTasks", "error", "projectsResponse", "getMyProjects", "myProjects", "allTeams", "teamsResponse", "getProjectTeams", "JSON", "stringify", "projectTeams", "teamsWithProject", "data", "params", "page", "size", "type", "sortBy", "sortDir", "getTeamRecords", "hasRecords", "recordsLength", "totalElements", "totalPages", "allTasks", "reduce", "acc", "allTasksResponse", "getRecords", "allTasksInSystem", "creator", "realName", "debugResponse", "fetch", "headers", "localStorage", "getItem", "debugData", "json", "dueDate", "assignee", "progress", "filteredTasks", "submitTask", "validate", "taskData", "getPriorityNumber", "Date", "toISOString", "updateRecord", "success", "createRecord", "resetForm", "errorMessage", "assign", "viewTask", "editTask", "handleTaskAction", "action", "showSubmitDialog", "approveParams", "URLSearchParams", "append", "reviewTask", "feedback", "prompt", "confirmButtonText", "cancelButtonText", "inputType", "rejectParams", "updateTaskStatus", "confirm", "deleteRecord", "updateProgress", "startTask", "uploadRef", "handleFileChange", "_", "handleFileRemove", "beforeUpload", "file", "isLt10M", "submitTaskCompletion", "formData", "FormData", "fileItem", "raw", "submitTaskWithFiles", "canEdit", "canManage", "formatDate", "date", "toLocaleString", "isOverdue", "getStatusColor", "colorMap", "getStatusText", "textMap", "getPriorityColor", "<PERSON><PERSON><PERSON>", "numberToEnum", "getPriorityText", "numberMap", "newTeamId"], "sources": ["D:\\workspace\\idea\\worker\\work_cli\\src\\views\\collaboration\\TaskManagementView.vue"], "sourcesContent": ["<template>\n  <div class=\"task-management\">\n    <el-card>\n      <template #header>\n        <div class=\"card-header\">\n          <h3>任务管理</h3>\n          <div class=\"header-actions\">\n            <el-select v-model=\"currentTeamId\" placeholder=\"选择团队\" @change=\"loadTasks\" style=\"width: 200px;\">\n              <el-option\n                v-for=\"team in myTeams\"\n                :key=\"team.id\"\n                :label=\"team.projectName ? `${team.name} (${team.projectName})` : team.name\"\n                :value=\"team.id\"\n              />\n            </el-select>\n            <el-button v-if=\"isTeacher\" type=\"primary\" @click=\"goToTaskPublish\" :icon=\"Plus\">\n              发布任务\n            </el-button>\n            <el-button @click=\"loadTasks\" :loading=\"loading\" :icon=\"Refresh\">\n              刷新\n            </el-button>\n          </div>\n        </div>\n      </template>\n      \n      <!-- 任务统计 -->\n      <div class=\"task-stats\">\n        <el-row :gutter=\"16\">\n          <el-col :span=\"6\">\n            <el-statistic title=\"总任务数\" :value=\"taskStats.total\" />\n          </el-col>\n          <el-col :span=\"6\">\n            <el-statistic title=\"进行中\" :value=\"taskStats.inProgress\" />\n          </el-col>\n          <el-col :span=\"6\">\n            <el-statistic title=\"已完成\" :value=\"taskStats.completed\" />\n          </el-col>\n          <el-col :span=\"6\">\n            <el-statistic title=\"完成率\" :value=\"taskStats.completionRate\" suffix=\"%\" />\n          </el-col>\n        </el-row>\n      </div>\n      \n\n\n      <!-- 任务列表 -->\n      <div class=\"task-list\" v-loading=\"loading\">\n        <el-tabs v-model=\"activeTab\" @tab-change=\"loadTasks\">\n          <el-tab-pane label=\"全部任务\" name=\"all\" />\n          <el-tab-pane label=\"已发布\" name=\"PUBLISHED\" />\n          <el-tab-pane label=\"活跃任务\" name=\"ACTIVE\" />\n          <el-tab-pane label=\"进行中\" name=\"IN_PROGRESS\" />\n          <el-tab-pane label=\"待审核\" name=\"SUBMITTED\" />\n          <el-tab-pane label=\"已完成\" name=\"COMPLETED\" />\n          <el-tab-pane label=\"已取消\" name=\"CANCELLED\" />\n        </el-tabs>\n\n        <div v-if=\"tasks.length === 0 && !loading\" class=\"empty-state\">\n          <el-empty description=\"暂无任务\">\n            <el-button v-if=\"isTeacher\" type=\"primary\" @click=\"goToTaskPublish\">\n              发布第一个任务\n            </el-button>\n            <template v-else>\n              <p style=\"color: #909399; font-size: 14px; margin-top: 16px;\">\n                等待教师发布任务\n              </p>\n            </template>\n          </el-empty>\n        </div>\n\n        <div v-else class=\"task-grid\">\n          <div v-for=\"task in tasks\" :key=\"task.id\" class=\"task-card\">\n            <el-card shadow=\"hover\" @click=\"viewTask(task)\">\n              <h4>{{ task.title }}</h4>\n              <p class=\"task-description\">{{ task.content || task.description || '暂无描述' }}</p>\n              <div class=\"task-meta\">\n                <el-tag :type=\"getStatusColor(task.status)\" size=\"small\">\n                  {{ getStatusText(task.status) }}\n                </el-tag>\n                <el-tag v-if=\"task.priority\" :type=\"getPriorityColor(task.priority)\" size=\"small\">\n                  {{ getPriorityText(task.priority) }}\n                </el-tag>\n              </div>\n              <div class=\"task-info\">\n                <div class=\"info-item\" v-if=\"task.deadline\">\n                  <el-icon><Calendar /></el-icon>\n                  <span>{{ formatDate(task.deadline) }}</span>\n                </div>\n                <div class=\"info-item\" v-if=\"task.assignee\">\n                  <el-icon><User /></el-icon>\n                  <span>{{ task.assignee }}</span>\n                </div>\n              </div>\n              <div class=\"task-footer\">\n                <el-button size=\"small\" @click.stop=\"viewTask(task)\">\n                  查看详情\n                </el-button>\n\n                <!-- 学生操作 -->\n                <template v-if=\"!isTeacher\">\n                  <el-button\n                    v-if=\"task.status === 'PUBLISHED'\"\n                    size=\"small\"\n                    type=\"primary\"\n                    @click.stop=\"startTask(task)\"\n                  >\n                    开始任务\n                  </el-button>\n                  <el-button\n                    v-if=\"task.status === 'ACTIVE' || task.status === 'IN_PROGRESS'\"\n                    size=\"small\"\n                    type=\"success\"\n                    @click.stop=\"showSubmitDialog(task)\"\n                  >\n                    提交任务\n                  </el-button>\n                </template>\n\n                <!-- 教师操作 -->\n                <template v-if=\"isTeacher\">\n                  <el-button v-if=\"canEdit(task)\" size=\"small\" @click.stop=\"editTask(task)\">\n                    编辑\n                  </el-button>\n                  <el-dropdown v-if=\"canManage(task)\" @command=\"handleTaskAction\" @click.stop>\n                    <el-button size=\"small\" :icon=\"ArrowDown\">\n                      更多\n                    </el-button>\n                    <template #dropdown>\n                      <el-dropdown-menu>\n                        <el-dropdown-item\n                          v-if=\"task.status === 'SUBMITTED'\"\n                          :command=\"{action: 'approve', task}\"\n                        >\n                          通过审核\n                        </el-dropdown-item>\n                        <el-dropdown-item\n                          v-if=\"task.status === 'SUBMITTED'\"\n                          :command=\"{action: 'reject', task}\"\n                        >\n                          拒绝任务\n                        </el-dropdown-item>\n                        <el-dropdown-item\n                          v-if=\"task.status === 'PUBLISHED'\"\n                          :command=\"{action: 'activate', task}\"\n                        >\n                          激活任务\n                        </el-dropdown-item>\n                        <el-dropdown-item\n                          v-if=\"['PUBLISHED', 'ACTIVE', 'IN_PROGRESS'].includes(task.status)\"\n                          :command=\"{action: 'cancel', task}\"\n                        >\n                          取消任务\n                        </el-dropdown-item>\n                        <el-dropdown-item\n                          :command=\"{action: 'delete', task}\"\n                          divided\n                        >\n                          删除任务\n                        </el-dropdown-item>\n                      </el-dropdown-menu>\n                    </template>\n                  </el-dropdown>\n                </template>\n              </div>\n            </el-card>\n          </div>\n        </div>\n      </div>\n\n      \n      <!-- 分页 -->\n      <div v-if=\"total > 0\" class=\"pagination\">\n        <el-pagination\n          v-model:current-page=\"currentPage\"\n          v-model:page-size=\"pageSize\"\n          :total=\"total\"\n          :page-sizes=\"[10, 20, 50]\"\n          layout=\"total, sizes, prev, pager, next, jumper\"\n          @size-change=\"loadTasks\"\n          @current-change=\"loadTasks\"\n        />\n      </div>\n    </el-card>\n    \n    <!-- 创建/编辑任务对话框 -->\n    <el-dialog\n      v-model=\"showCreateDialog\"\n      :title=\"editingTask ? '编辑任务' : '创建任务'\"\n      width=\"600px\"\n    >\n      <el-form\n        ref=\"formRef\"\n        :model=\"taskForm\"\n        :rules=\"formRules\"\n        label-width=\"80px\"\n      >\n        <el-form-item label=\"任务标题\" prop=\"title\">\n          <el-input v-model=\"taskForm.title\" placeholder=\"请输入任务标题\" />\n        </el-form-item>\n        \n        <el-form-item label=\"任务描述\" prop=\"description\">\n          <el-input\n            v-model=\"taskForm.description\"\n            type=\"textarea\"\n            :rows=\"4\"\n            placeholder=\"请输入任务描述\"\n          />\n        </el-form-item>\n        \n        <el-form-item label=\"负责人\">\n          <el-input :value=\"currentUser?.realName || '当前用户'\" disabled />\n          <div class=\"form-help-text\">任务创建者即为负责人</div>\n        </el-form-item>\n        \n        <el-form-item label=\"优先级\" prop=\"priority\">\n          <el-select v-model=\"taskForm.priority\" placeholder=\"请选择优先级\">\n            <el-option label=\"低\" value=\"LOW\" />\n            <el-option label=\"中\" value=\"MEDIUM\" />\n            <el-option label=\"高\" value=\"HIGH\" />\n            <el-option label=\"紧急\" value=\"URGENT\" />\n          </el-select>\n        </el-form-item>\n        \n        <el-form-item label=\"截止时间\" prop=\"deadline\">\n          <el-date-picker\n            v-model=\"taskForm.deadline\"\n            type=\"datetime\"\n            placeholder=\"请选择截止时间\"\n            style=\"width: 100%\"\n          />\n        </el-form-item>\n      </el-form>\n      \n      <template #footer>\n        <el-button @click=\"showCreateDialog = false\">取消</el-button>\n        <el-button type=\"primary\" @click=\"submitTask\" :loading=\"submitting\">\n          {{ editingTask ? '更新' : '创建' }}\n        </el-button>\n      </template>\n    </el-dialog>\n    \n    <!-- 任务详情对话框 -->\n    <el-dialog v-model=\"showDetailDialog\" title=\"任务详情\" width=\"700px\">\n      <div v-if=\"selectedTask\" class=\"task-detail\">\n        <el-descriptions :column=\"2\" border>\n          <el-descriptions-item label=\"任务标题\" :span=\"2\">{{ selectedTask.title }}</el-descriptions-item>\n          <el-descriptions-item label=\"任务状态\">\n            <el-tag :type=\"getStatusColor(selectedTask.status)\">\n              {{ getStatusText(selectedTask.status) }}\n            </el-tag>\n          </el-descriptions-item>\n          <el-descriptions-item label=\"优先级\">\n            <el-tag :type=\"getPriorityColor(selectedTask.priority)\">\n              {{ getPriorityText(selectedTask.priority) }}\n            </el-tag>\n          </el-descriptions-item>\n          <el-descriptions-item label=\"创建人\">{{ selectedTask.creator?.realName }}</el-descriptions-item>\n          <el-descriptions-item label=\"创建时间\">{{ formatDate(selectedTask.createTime) }}</el-descriptions-item>\n          <el-descriptions-item label=\"截止时间\">{{ formatDate(selectedTask.deadline) }}</el-descriptions-item>\n          <el-descriptions-item label=\"任务描述\" :span=\"2\">{{ selectedTask.content || selectedTask.description || '暂无描述' }}</el-descriptions-item>\n        </el-descriptions>\n        \n        <!-- 进度更新 -->\n        <div v-if=\"selectedTask.status === 'IN_PROGRESS'\" class=\"progress-update\">\n          <h4>更新进度</h4>\n          <el-slider\n            v-model=\"progressValue\"\n            :max=\"100\"\n            :step=\"5\"\n            show-stops\n            show-input\n            @change=\"updateProgress\"\n          />\n        </div>\n      </div>\n      \n      <template #footer>\n        <el-button @click=\"showDetailDialog = false\">关闭</el-button>\n      </template>\n    </el-dialog>\n\n    <!-- 任务提交对话框 -->\n    <el-dialog\n      v-model=\"showSubmitTaskDialog\"\n      title=\"提交任务\"\n      width=\"600px\"\n    >\n      <el-form\n        ref=\"submitFormRef\"\n        :model=\"submitForm\"\n        :rules=\"submitFormRules\"\n        label-width=\"100px\"\n      >\n        <el-form-item label=\"任务名称\">\n          <span>{{ selectedTask?.title }}</span>\n        </el-form-item>\n\n        <el-form-item label=\"提交说明\" prop=\"content\">\n          <el-input\n            v-model=\"submitForm.content\"\n            type=\"textarea\"\n            :rows=\"5\"\n            placeholder=\"请详细描述您的任务完成情况...\"\n          />\n        </el-form-item>\n\n        <el-form-item label=\"提交文件\">\n          <el-upload\n            ref=\"uploadRef\"\n            :file-list=\"submitForm.fileList\"\n            :on-change=\"handleFileChange\"\n            :on-remove=\"handleFileRemove\"\n            :before-upload=\"beforeUpload\"\n            :auto-upload=\"false\"\n            multiple\n            drag\n          >\n            <el-icon class=\"el-icon--upload\"><upload-filled /></el-icon>\n            <div class=\"el-upload__text\">\n              将文件拖到此处，或<em>点击上传</em>\n            </div>\n            <template #tip>\n              <div class=\"el-upload__tip\">\n                支持多个文件上传，单个文件大小不超过10MB\n              </div>\n            </template>\n          </el-upload>\n        </el-form-item>\n      </el-form>\n\n      <template #footer>\n        <el-button @click=\"showSubmitTaskDialog = false\">取消</el-button>\n        <el-button type=\"primary\" @click=\"submitTaskCompletion\" :loading=\"submitting\">\n          提交\n        </el-button>\n      </template>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport { ref, reactive, onMounted, computed, watch, nextTick } from 'vue'\nimport { useStore } from 'vuex'\nimport { useRouter } from 'vue-router'\nimport { recordAPI, teamAPI, projectAPI } from '@/api'\nimport { ElMessage, ElMessageBox } from 'element-plus'\nimport { Plus, ArrowDown, UploadFilled, Refresh, Calendar, User } from '@element-plus/icons-vue'\n\nexport default {\n  name: 'TaskManagementView',\n  components: {\n    Plus,\n    ArrowDown,\n    UploadFilled,\n    Refresh,\n    Calendar,\n    User\n  },\n  setup() {\n    const store = useStore()\n    const router = useRouter()\n    const formRef = ref()\n    \n    const loading = ref(false)\n    const submitting = ref(false)\n    const showCreateDialog = ref(false)\n    const showDetailDialog = ref(false)\n    const showSubmitTaskDialog = ref(false)\n    const editingTask = ref(null)\n    const selectedTask = ref(null)\n    const progressValue = ref(0)\n\n    // 任务提交表单\n    const submitForm = reactive({\n      content: '',\n      fileList: []\n    })\n    const submitFormRules = {\n      content: [\n        { required: true, message: '请输入提交说明', trigger: 'blur' }\n      ]\n    }\n    \n    const tasks = ref([])\n    const myTeams = ref([])\n    const currentTeamId = ref('')\n    const activeTab = ref('all')\n    const currentPage = ref(1)\n    const pageSize = ref(10)\n    const total = ref(0)\n    \n    const taskForm = reactive({\n      title: '',\n      description: '',\n      priority: 'MEDIUM',\n      deadline: null\n    })\n    \n    const formRules = {\n      title: [\n        { required: true, message: '请输入任务标题', trigger: 'blur' },\n        { min: 2, max: 100, message: '标题长度在 2 到 100 个字符', trigger: 'blur' }\n      ],\n      description: [\n        { required: true, message: '请输入任务描述', trigger: 'blur' }\n      ],\n      deadline: [\n        { required: true, message: '请选择截止时间', trigger: 'change' }\n      ]\n    }\n    \n    const currentUser = computed(() => store.getters.currentUser)\n    const isTeacher = computed(() => currentUser.value?.role === 'TEACHER')\n\n    // 跳转到任务发布页面\n    const goToTaskPublish = () => {\n      router.push('/dashboard/task-publish')\n    }\n    \n    // 任务统计\n    const taskStats = computed(() => {\n      const total = tasks.value.length\n      const completed = tasks.value.filter(t => t.status === 'COMPLETED').length\n      const inProgress = tasks.value.filter(t => t.status === 'IN_PROGRESS').length\n      const completionRate = total > 0 ? Math.round((completed / total) * 100) : 0\n\n      return {\n        total,\n        completed,\n        inProgress,\n        completionRate\n      }\n    })\n\n    // 教师端：按项目和团队分组的任务数据\n    const groupedTasks = computed(() => {\n      if (!isTeacher.value || tasks.value.length === 0) {\n        return []\n      }\n\n      // 按项目分组\n      const projectGroups = {}\n\n      tasks.value.forEach(task => {\n        const projectId = task.projectId || 'unknown'\n        const projectName = task.projectName || '未知项目'\n        const teamId = task.teamId || 'unknown'\n        const teamName = task.teamName || '未知团队'\n\n        if (!projectGroups[projectId]) {\n          projectGroups[projectId] = {\n            projectId,\n            projectName,\n            teams: {},\n            totalTasks: 0,\n            completedTasks: 0\n          }\n        }\n\n        if (!projectGroups[projectId].teams[teamId]) {\n          projectGroups[projectId].teams[teamId] = {\n            teamId,\n            teamName,\n            tasks: []\n          }\n        }\n\n        projectGroups[projectId].teams[teamId].tasks.push(task)\n        projectGroups[projectId].totalTasks++\n\n        if (task.status === 'COMPLETED') {\n          projectGroups[projectId].completedTasks++\n        }\n      })\n\n      // 转换为数组格式\n      return Object.values(projectGroups).map(project => ({\n        ...project,\n        teams: Object.values(project.teams)\n      }))\n    })\n    \n    // 加载我的团队\n    const loadMyTeams = async () => {\n      try {\n        console.log('=== 团队加载调试信息 ===')\n        console.log('当前用户:', currentUser.value)\n        console.log('用户角色:', currentUser.value?.role)\n\n        if (currentUser.value?.role === 'TEACHER') {\n          // 教师：获取自己项目的团队\n          await loadTeacherProjectTeams()\n        } else {\n          // 学生：获取参与的团队\n          console.log('🔍 学生获取团队列表...')\n          const response = await teamAPI.getJoinedTeams()\n          console.log('✅ 学生团队响应:', response)\n          myTeams.value = response?.records || []\n          console.log('📋 学生团队列表:', myTeams.value.map(team => ({\n            id: team.id,\n            name: team.name,\n            projectId: team.projectId,\n            projectName: team.projectName,\n            status: team.status\n          })))\n        }\n\n        console.log('团队列表:', myTeams.value)\n        console.log('团队数量:', myTeams.value.length)\n\n        if (myTeams.value.length > 0) {\n          currentTeamId.value = myTeams.value[0].id\n          console.log('选中的团队ID:', currentTeamId.value)\n          loadTasks()\n        } else {\n          console.log('没有找到任何团队')\n        }\n      } catch (error) {\n        console.error('加载团队列表失败:', error)\n        ElMessage.error('加载团队列表失败')\n      }\n    }\n\n    // 加载教师项目的团队\n    const loadTeacherProjectTeams = async () => {\n      try {\n        // 1. 先获取教师自己的项目列表\n        const projectsResponse = await projectAPI.getMyProjects()\n        console.log('教师项目响应:', projectsResponse)\n        const myProjects = projectsResponse?.records || []\n        console.log('教师项目列表:', myProjects)\n\n        if (myProjects.length === 0) {\n          console.log('教师没有发布任何项目')\n          myTeams.value = []\n          return\n        }\n\n        // 2. 获取每个项目的团队列表\n        const allTeams = []\n        for (const project of myProjects) {\n          try {\n            console.log(`=== 开始获取项目 ${project.name} (ID: ${project.id}) 的团队 ===`)\n            const teamsResponse = await teamAPI.getProjectTeams(project.id)\n            console.log(`项目 ${project.name} 的团队响应:`, teamsResponse)\n            console.log('响应类型:', typeof teamsResponse)\n            console.log('响应结构:', JSON.stringify(teamsResponse, null, 2))\n\n            const projectTeams = teamsResponse?.records || []\n            console.log(`项目 ${project.name} 的团队数量:`, projectTeams.length)\n\n            // 为每个团队添加项目信息，便于显示\n            const teamsWithProject = projectTeams.map(team => ({\n              ...team,\n              projectName: project.name,\n              projectId: project.id\n            }))\n\n            allTeams.push(...teamsWithProject)\n            console.log(`项目 ${project.name} 处理完成，累计团队数:`, allTeams.length)\n          } catch (error) {\n            console.error(`=== 获取项目 ${project.name} 的团队失败 ===`)\n            console.error('错误对象:', error)\n            console.error('错误消息:', error.message)\n            console.error('错误响应:', error.response)\n            console.error('错误状态:', error.response?.status)\n            console.error('错误数据:', error.response?.data)\n          }\n        }\n\n        console.log('教师所有项目的团队:', allTeams)\n        myTeams.value = allTeams\n\n      } catch (error) {\n        console.error('加载教师项目团队失败:', error)\n        throw error\n      }\n    }\n\n    // 加载任务列表\n    const loadTasks = async () => {\n      if (!currentTeamId.value) {\n        console.log('❌ 没有选择团队，无法加载任务')\n        return\n      }\n\n      try {\n        loading.value = true\n        const params = {\n          page: currentPage.value,\n          size: pageSize.value,\n          type: 'TASK',  // 只获取任务类型的记录\n          sortBy: 'createTime',\n          sortDir: 'desc'\n        }\n\n        console.log('=== 任务加载调试信息 ===')\n        console.log('当前用户:', currentUser.value)\n        console.log('当前团队ID:', currentTeamId.value)\n        console.log('所有团队:', myTeams.value)\n        console.log('请求参数:', params)\n        console.log('请求URL:', `/records/teams/${currentTeamId.value}`)\n\n        // 获取团队任务\n        const response = await recordAPI.getTeamRecords(currentTeamId.value, params)\n        console.log('✅ 团队任务API响应:', response)\n        console.log('响应数据结构:', {\n          hasRecords: !!response?.records,\n          recordsLength: response?.records?.length || 0,\n          totalElements: response?.totalElements,\n          totalPages: response?.totalPages,\n          currentPage: response?.currentPage\n        })\n\n        let allTasks = response?.records || []\n        console.log('📋 获取到的任务数量:', allTasks.length)\n\n        // 显示任务的详细信息\n        if (allTasks.length > 0) {\n          console.log('任务详情:', allTasks.map(task => ({\n            id: task.id,\n            title: task.title,\n            type: task.type,\n            status: task.status,\n            teamId: task.teamId,\n            projectId: task.projectId\n          })))\n\n          console.log('任务状态分布:', allTasks.reduce((acc, task) => {\n            acc[task.status] = (acc[task.status] || 0) + 1\n            return acc\n          }, {}))\n        } else {\n          console.log('❌ 没有获取到任何任务数据')\n\n          // 尝试获取所有任务进行对比\n          try {\n            console.log('🔍 尝试获取所有任务进行对比...')\n            const allTasksResponse = await recordAPI.getRecords({ type: 'TASK', page: 1, size: 50 })\n            console.log('所有任务响应:', allTasksResponse)\n            const allTasksInSystem = allTasksResponse?.records || []\n            console.log('系统中所有任务:', allTasksInSystem.map(task => ({\n              id: task.id,\n              title: task.title,\n              teamId: task.teamId,\n              projectId: task.projectId,\n              status: task.status,\n              creator: task.creator?.realName\n            })))\n\n            // 调用调试接口\n            console.log('🔍 调用调试接口获取任务详情...')\n            const debugResponse = await fetch('/api/records/debug/tasks', {\n              headers: {\n                'Authorization': `Bearer ${localStorage.getItem('token')}`\n              }\n            })\n            const debugData = await debugResponse.json()\n            console.log('调试接口响应:', debugData)\n          } catch (error) {\n            console.log('获取所有任务失败:', error)\n          }\n        }\n\n\n\n        // 为缺少status字段的任务添加默认状态\n        allTasks = allTasks.map(task => ({\n          ...task,\n          status: task.status || 'PUBLISHED',  // 默认状态为PUBLISHED（已发布）\n          deadline: task.dueDate || null,  // 使用dueDate字段\n          assignee: task.assignee || null,\n          progress: task.progress || 0\n        }))\n\n        // 前端过滤任务状态\n        console.log('当前选中的标签页:', activeTab.value)\n        console.log('过滤前所有任务:', allTasks.map(task => ({ id: task.id, title: task.title, status: task.status })))\n\n        if (activeTab.value !== 'all') {\n          const filteredTasks = allTasks.filter(task => task.status === activeTab.value)\n          console.log(`过滤前任务数量: ${allTasks.length}, 过滤后任务数量: ${filteredTasks.length}`)\n          console.log('过滤后的任务:', filteredTasks.map(task => ({ id: task.id, title: task.title, status: task.status })))\n          allTasks = filteredTasks\n        }\n\n        // 强制触发响应式更新\n        tasks.value = []\n        await nextTick()\n        tasks.value = [...allTasks]\n        total.value = allTasks.length\n\n      } catch (error) {\n        console.error('=== 任务加载错误信息 ===')\n        console.error('错误对象:', error)\n        console.error('错误消息:', error.message)\n        console.error('错误响应:', error.response)\n        console.error('错误状态:', error.response?.status)\n        console.error('错误数据:', error.response?.data)\n\n        ElMessage.error('加载任务失败: ' + (error.response?.data?.message || error.message || '未知错误'))\n      } finally {\n        loading.value = false\n      }\n    }\n    \n    // 提交任务\n    const submitTask = async () => {\n      if (!formRef.value) return\n\n      try {\n        await formRef.value.validate()\n        submitting.value = true\n\n        const taskData = {\n          title: taskForm.title,\n          content: taskForm.description,\n          priority: getPriorityNumber(taskForm.priority),\n          dueDate: taskForm.deadline ? new Date(taskForm.deadline).toISOString() : null\n        }\n\n        // 创建任务时需要额外的字段\n        if (!editingTask.value) {\n          taskData.type = 'TASK'\n          taskData.teamId = currentTeamId.value\n        }\n\n        console.log('提交任务数据:', taskData)\n        console.log('是否为编辑模式:', !!editingTask.value)\n\n        if (editingTask.value) {\n          console.log('更新任务ID:', editingTask.value.id)\n          await recordAPI.updateRecord(editingTask.value.id, taskData)\n          ElMessage.success('任务更新成功')\n        } else {\n          console.log('创建新任务')\n          await recordAPI.createRecord(taskData)\n          ElMessage.success('任务创建成功')\n        }\n\n        showCreateDialog.value = false\n        resetForm()\n        loadTasks()\n      } catch (error) {\n        console.error('提交任务失败:', error)\n        console.error('错误详情:', error.response?.data)\n        const errorMessage = error.response?.data?.message || error.message || '提交任务失败'\n        ElMessage.error(errorMessage)\n      } finally {\n        submitting.value = false\n      }\n    }\n    \n    // 重置表单\n    const resetForm = () => {\n      Object.assign(taskForm, {\n        title: '',\n        description: '',\n        priority: 'MEDIUM',\n        deadline: null\n      })\n      editingTask.value = null\n    }\n    \n    // 查看任务详情\n    const viewTask = (task) => {\n      selectedTask.value = task\n      progressValue.value = task.progress || 0\n      showDetailDialog.value = true\n    }\n    \n    // 编辑任务\n    const editTask = (task) => {\n      editingTask.value = task\n      Object.assign(taskForm, {\n        title: task.title,\n        description: task.content,\n        priority: task.priority,\n        deadline: task.dueDate ? new Date(task.dueDate) : null\n      })\n      showCreateDialog.value = true\n    }\n    \n    // 处理任务操作\n    const handleTaskAction = async ({ action, task }) => {\n      try {\n        let message = ''\n        let status = ''\n        \n        switch (action) {\n          case 'start':\n            status = 'IN_PROGRESS'\n            message = '任务已开始'\n            break\n          case 'submit':\n            // 学生提交任务，显示提交对话框\n            showSubmitDialog(task)\n            return\n          case 'approve':\n            // 教师审核通过任务\n            const approveParams = new URLSearchParams()\n            approveParams.append('approved', 'true')\n            await recordAPI.reviewTask(task.id, approveParams)\n            ElMessage.success('任务审核通过')\n            loadTasks()\n            return\n          case 'reject':\n            // 教师拒绝任务，需要提供反馈\n            const feedback = await ElMessageBox.prompt('请输入拒绝理由', '任务审核', {\n              confirmButtonText: '确定',\n              cancelButtonText: '取消',\n              inputType: 'textarea'\n            })\n            const rejectParams = new URLSearchParams()\n            rejectParams.append('approved', 'false')\n            rejectParams.append('feedback', feedback.value || '')\n            await recordAPI.reviewTask(task.id, rejectParams)\n            ElMessage.success('任务已拒绝，学生需要重新完成')\n            loadTasks()\n            return\n          case 'activate':\n            // 教师激活任务\n            await recordAPI.updateTaskStatus(task.id, 'ACTIVE')\n            ElMessage.success('任务已激活')\n            loadTasks()\n            return\n          case 'cancel':\n            status = 'CANCELLED'\n            message = '任务已取消'\n            break\n          case 'delete':\n            await ElMessageBox.confirm('确定要删除这个任务吗？', '确认删除', {\n              confirmButtonText: '确定',\n              cancelButtonText: '取消',\n              type: 'warning'\n            })\n            await recordAPI.deleteRecord(task.id)\n            ElMessage.success('任务删除成功')\n            loadTasks()\n            return\n        }\n        \n        if (status) {\n          await recordAPI.updateRecord(task.id, { status })\n          ElMessage.success(message)\n          loadTasks()\n        }\n      } catch (error) {\n        if (error !== 'cancel') {\n          console.error('任务操作失败:', error)\n          ElMessage.error('操作失败')\n        }\n      }\n    }\n    \n    // 更新进度\n    const updateProgress = async (value) => {\n      if (!selectedTask.value) return\n\n      try {\n        await recordAPI.updateRecord(selectedTask.value.id, { progress: value })\n        ElMessage.success('进度更新成功')\n        loadTasks()\n      } catch (error) {\n        console.error('更新进度失败:', error)\n        ElMessage.error('更新进度失败')\n      }\n    }\n\n    // 开始任务\n    const startTask = async (task) => {\n      try {\n        await ElMessageBox.confirm(\n          `确定要开始任务\"${task.title}\"吗？开始后任务状态将变为活跃状态。`,\n          '开始任务',\n          {\n            confirmButtonText: '开始',\n            cancelButtonText: '取消',\n            type: 'info'\n          }\n        )\n\n        // 调用API更新任务状态为ACTIVE\n        await recordAPI.updateTaskStatus(task.id, 'ACTIVE')\n        ElMessage.success('任务已开始')\n        loadTasks() // 刷新任务列表\n      } catch (error) {\n        if (error !== 'cancel') {\n          console.error('开始任务失败:', error)\n          ElMessage.error('开始任务失败')\n        }\n      }\n    }\n\n    // 显示任务提交对话框\n    const showSubmitDialog = (task) => {\n      selectedTask.value = task\n      submitForm.content = ''\n      submitForm.fileList = []\n      showSubmitTaskDialog.value = true\n    }\n\n    // 文件上传处理\n    const uploadRef = ref()\n\n    const handleFileChange = (_, fileList) => {\n      submitForm.fileList = fileList\n    }\n\n    const handleFileRemove = (_, fileList) => {\n      submitForm.fileList = fileList\n    }\n\n    const beforeUpload = (file) => {\n      const isLt10M = file.size / 1024 / 1024 < 10\n      if (!isLt10M) {\n        ElMessage.error('文件大小不能超过10MB!')\n        return false\n      }\n      return true\n    }\n\n    // 提交任务完成\n    const submitTaskCompletion = async () => {\n      try {\n        submitting.value = true\n\n        // 使用FormData支持文件上传\n        const formData = new FormData()\n        formData.append('submissionContent', submitForm.content)\n\n        // 添加文件\n        if (submitForm.fileList && submitForm.fileList.length > 0) {\n          submitForm.fileList.forEach((fileItem) => {\n            if (fileItem.raw) {\n              formData.append('files', fileItem.raw)\n            }\n          })\n        }\n\n        await recordAPI.submitTaskWithFiles(selectedTask.value.id, formData)\n\n        ElMessage.success('任务提交成功')\n        showSubmitTaskDialog.value = false\n        loadTasks()\n      } catch (error) {\n        console.error('任务提交失败:', error)\n        ElMessage.error('任务提交失败')\n      } finally {\n        submitting.value = false\n      }\n    }\n    \n    // 权限检查\n    const canEdit = (task) => {\n      return task.creator?.id === currentUser.value?.id || currentUser.value?.role === 'TEACHER'\n    }\n\n    const canManage = (task) => {\n      return task.creator?.id === currentUser.value?.id || currentUser.value?.role === 'TEACHER'\n    }\n    \n    // 工具方法\n    const formatDate = (date) => {\n      if (!date) return ''\n      return new Date(date).toLocaleString('zh-CN')\n    }\n    \n    const isOverdue = (deadline) => {\n      return new Date(deadline) < new Date()\n    }\n    \n    const getStatusColor = (status) => {\n      const colorMap = {\n        'ACTIVE': 'success',\n        'PUBLISHED': 'info',\n        'IN_PROGRESS': 'primary',\n        'SUBMITTED': 'warning',\n        'COMPLETED': 'success',\n        'CANCELLED': 'danger',\n        'REJECTED': 'danger',\n        'APPROVED': 'success',\n        'DRAFT': 'info'\n      }\n      return colorMap[status] || 'info'  // 默认返回 'info' 而不是空字符串\n    }\n\n    const getStatusText = (status) => {\n      const textMap = {\n        'ACTIVE': '活跃',\n        'PUBLISHED': '已发布',\n        'IN_PROGRESS': '进行中',\n        'SUBMITTED': '待审核',\n        'COMPLETED': '已完成',\n        'CANCELLED': '已取消',\n        'REJECTED': '已拒绝',\n        'APPROVED': '已通过',\n        'DRAFT': '草稿'\n      }\n      return textMap[status] || status\n    }\n    \n    const getPriorityColor = (priority) => {\n      // 处理数字类型的优先级\n      let priorityKey = priority\n      if (typeof priority === 'number') {\n        const numberToEnum = {\n          1: 'LOW',\n          2: 'MEDIUM',\n          3: 'HIGH',\n          4: 'URGENT',\n          5: 'URGENT'\n        }\n        priorityKey = numberToEnum[priority] || 'MEDIUM'\n      }\n\n      const colorMap = {\n        'LOW': 'info',\n        'MEDIUM': 'primary',\n        'HIGH': 'warning',\n        'URGENT': 'danger'\n      }\n      return colorMap[priorityKey] || 'info'  // 默认返回 'info' 而不是空字符串\n    }\n\n    const getPriorityText = (priority) => {\n      // 处理数字类型的优先级\n      let priorityKey = priority\n      if (typeof priority === 'number') {\n        const numberToEnum = {\n          1: 'LOW',\n          2: 'MEDIUM',\n          3: 'HIGH',\n          4: 'URGENT',\n          5: 'URGENT'\n        }\n        priorityKey = numberToEnum[priority] || 'MEDIUM'\n      }\n\n      const textMap = {\n        'LOW': '低',\n        'MEDIUM': '中',\n        'HIGH': '高',\n        'URGENT': '紧急'\n      }\n      return textMap[priorityKey] || '中'  // 默认返回 '中'\n    }\n\n    const getPriorityNumber = (priority) => {\n      const numberMap = {\n        'LOW': 1,\n        'MEDIUM': 2,\n        'HIGH': 3,\n        'URGENT': 4\n      }\n      return numberMap[priority] || 2\n    }\n    \n    // 监听团队变化\n    watch(currentTeamId, (newTeamId) => {\n      if (newTeamId) {\n        loadTasks()\n      }\n    })\n\n\n    \n\n\n    onMounted(() => {\n      loadTasks()\n      loadMyTeams()\n    })\n    \n    return {\n      loading,\n      submitting,\n      showCreateDialog,\n      showDetailDialog,\n      showSubmitTaskDialog,\n      editingTask,\n      selectedTask,\n      progressValue,\n      submitForm,\n      submitFormRules,\n      tasks,\n      myTeams,\n      currentTeamId,\n      activeTab,\n      currentPage,\n      pageSize,\n      total,\n      taskForm,\n      formRules,\n      formRef,\n      currentUser,\n      isTeacher,\n      taskStats,\n      groupedTasks,\n      goToTaskPublish,\n      loadTasks,\n      submitTask,\n      viewTask,\n      editTask,\n      handleTaskAction,\n      updateProgress,\n      startTask,\n      showSubmitDialog,\n      submitTaskCompletion,\n      uploadRef,\n      handleFileChange,\n      handleFileRemove,\n      beforeUpload,\n      canEdit,\n      canManage,\n      formatDate,\n      isOverdue,\n      getStatusColor,\n      getStatusText,\n      getPriorityColor,\n      getPriorityText,\n      getPriorityNumber\n    }\n  }\n}\n</script>\n\n<style scoped>\n.task-management {\n  padding: 0;\n  background: #f8f9fa;\n  min-height: calc(100vh - 120px);\n}\n\n.task-management .el-card {\n  border-radius: 12px;\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);\n  border: none;\n  overflow: hidden;\n}\n\n.card-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 24px 0;\n}\n\n.card-header h3 {\n  margin: 0;\n  font-size: 24px;\n  font-weight: 600;\n  color: #1a1a1a;\n}\n\n.header-actions {\n  display: flex;\n  gap: 12px;\n  align-items: center;\n}\n\n.header-actions .el-button {\n  border-radius: 8px;\n  padding: 10px 20px;\n  font-weight: 500;\n}\n\n.header-actions .el-select {\n  border-radius: 8px;\n}\n\n.task-management .el-tabs {\n  margin-top: 20px;\n}\n\n.task-management .el-tabs__header {\n  margin-bottom: 24px;\n}\n\n.task-management .el-tabs__nav-wrap {\n  background: #ffffff;\n  border-radius: 12px;\n  padding: 8px;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\n}\n\n.task-management .el-tabs__item {\n  border-radius: 8px;\n  padding: 12px 20px;\n  font-weight: 500;\n  transition: all 0.3s ease;\n}\n\n.task-management .el-tabs__item.is-active {\n  background: #409eff;\n  color: white;\n}\n\n.task-grid {\n  display: grid;\n  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));\n  gap: 24px;\n  margin-top: 24px;\n}\n\n.task-card {\n  cursor: pointer;\n  transition: all 0.3s ease;\n  height: 100%;\n}\n\n.task-card:hover {\n  transform: translateY(-6px);\n}\n\n.task-card .el-card {\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n  border-radius: 16px;\n  border: 1px solid #e9ecef;\n  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);\n  overflow: hidden;\n  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);\n}\n\n.task-card .el-card:hover {\n  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.15);\n}\n\n.task-card .el-card__body {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  min-height: 240px;\n  padding: 24px;\n}\n\n.task-card h4 {\n  margin: 0 0 16px 0;\n  color: #1a1a1a;\n  font-size: 18px;\n  font-weight: 700;\n  line-height: 1.4;\n}\n\n.task-description {\n  color: #6c757d;\n  margin: 0 0 20px 0;\n  display: -webkit-box;\n  -webkit-line-clamp: 3;\n  -webkit-box-orient: vertical;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  line-height: 1.6;\n  height: 4.8em;\n  flex: none;\n  font-size: 14px;\n  background: #f8f9fa;\n  padding: 12px;\n  border-radius: 8px;\n  border-left: 3px solid #409eff;\n}\n\n.task-meta {\n  display: flex;\n  gap: 10px;\n  margin: 20px 0;\n  flex-wrap: wrap;\n}\n\n.task-meta .el-tag {\n  border-radius: 6px;\n  padding: 4px 12px;\n  font-weight: 500;\n  font-size: 12px;\n}\n\n.task-info {\n  margin: 16px 0;\n  flex: 1;\n}\n\n.info-item {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  margin-bottom: 8px;\n  color: #6c757d;\n  font-size: 13px;\n  padding: 6px 12px;\n  background: #f8f9fa;\n  border-radius: 6px;\n  border: 1px solid #e9ecef;\n}\n\n.info-item .el-icon {\n  color: #409eff;\n  font-size: 14px;\n}\n\n.task-footer {\n  margin-top: auto;\n  display: flex;\n  justify-content: flex-end;\n  align-items: center;\n  gap: 12px;\n  flex-wrap: wrap;\n  padding-top: 16px;\n  border-top: 1px solid #e9ecef;\n}\n\n.task-footer .el-button {\n  border-radius: 8px;\n  padding: 8px 16px;\n  font-weight: 500;\n  font-size: 13px;\n}\n\n.empty-state {\n  text-align: center;\n  padding: 60px 40px;\n  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);\n  border-radius: 16px;\n  border: 2px dashed #d0d7de;\n  margin-top: 24px;\n}\n\n.empty-state .el-empty {\n  padding: 0;\n}\n\n.empty-state .el-button {\n  border-radius: 8px;\n  padding: 12px 24px;\n  font-weight: 500;\n}\n\n.pagination {\n  margin-top: 32px;\n  text-align: center;\n  padding: 20px 0;\n}\n</style>\n"], "mappings": ";;;;;;AAqVA,SAASA,GAAG,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,QAAO,QAAS,KAAI;AACxE,SAASC,QAAO,QAAS,MAAK;AAC9B,SAASC,SAAQ,QAAS,YAAW;AACrC,SAASC,SAAS,EAAEC,OAAO,EAAEC,UAAS,QAAS,OAAM;AACrD,SAASC,SAAS,EAAEC,YAAW,QAAS,cAAa;AACrD,SAASC,IAAI,EAAEC,SAAS,EAAEC,YAAY,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,IAAG,QAAS,yBAAwB;AAE/F,eAAe;EACbC,IAAI,EAAE,oBAAoB;EAC1BC,UAAU,EAAE;IACVP,IAAI;IACJC,SAAS;IACTC,YAAY;IACZC,OAAO;IACPC,QAAQ;IACRC;EACF,CAAC;EACDG,KAAKA,CAAA,EAAG;IACN,MAAMC,KAAI,GAAIhB,QAAQ,CAAC;IACvB,MAAMiB,MAAK,GAAIhB,SAAS,CAAC;IACzB,MAAMiB,OAAM,GAAIxB,GAAG,CAAC;IAEpB,MAAMyB,OAAM,GAAIzB,GAAG,CAAC,KAAK;IACzB,MAAM0B,UAAS,GAAI1B,GAAG,CAAC,KAAK;IAC5B,MAAM2B,gBAAe,GAAI3B,GAAG,CAAC,KAAK;IAClC,MAAM4B,gBAAe,GAAI5B,GAAG,CAAC,KAAK;IAClC,MAAM6B,oBAAmB,GAAI7B,GAAG,CAAC,KAAK;IACtC,MAAM8B,WAAU,GAAI9B,GAAG,CAAC,IAAI;IAC5B,MAAM+B,YAAW,GAAI/B,GAAG,CAAC,IAAI;IAC7B,MAAMgC,aAAY,GAAIhC,GAAG,CAAC,CAAC;;IAE3B;IACA,MAAMiC,UAAS,GAAIhC,QAAQ,CAAC;MAC1BiC,OAAO,EAAE,EAAE;MACXC,QAAQ,EAAE;IACZ,CAAC;IACD,MAAMC,eAAc,GAAI;MACtBF,OAAO,EAAE,CACP;QAAEG,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,SAAS;QAAEC,OAAO,EAAE;MAAO;IAE1D;IAEA,MAAMC,KAAI,GAAIxC,GAAG,CAAC,EAAE;IACpB,MAAMyC,OAAM,GAAIzC,GAAG,CAAC,EAAE;IACtB,MAAM0C,aAAY,GAAI1C,GAAG,CAAC,EAAE;IAC5B,MAAM2C,SAAQ,GAAI3C,GAAG,CAAC,KAAK;IAC3B,MAAM4C,WAAU,GAAI5C,GAAG,CAAC,CAAC;IACzB,MAAM6C,QAAO,GAAI7C,GAAG,CAAC,EAAE;IACvB,MAAM8C,KAAI,GAAI9C,GAAG,CAAC,CAAC;IAEnB,MAAM+C,QAAO,GAAI9C,QAAQ,CAAC;MACxB+C,KAAK,EAAE,EAAE;MACTC,WAAW,EAAE,EAAE;MACfC,QAAQ,EAAE,QAAQ;MAClBC,QAAQ,EAAE;IACZ,CAAC;IAED,MAAMC,SAAQ,GAAI;MAChBJ,KAAK,EAAE,CACL;QAAEX,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,SAAS;QAAEC,OAAO,EAAE;MAAO,CAAC,EACvD;QAAEc,GAAG,EAAE,CAAC;QAAEC,GAAG,EAAE,GAAG;QAAEhB,OAAO,EAAE,mBAAmB;QAAEC,OAAO,EAAE;MAAO,EACnE;MACDU,WAAW,EAAE,CACX;QAAEZ,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,SAAS;QAAEC,OAAO,EAAE;MAAO,EACvD;MACDY,QAAQ,EAAE,CACR;QAAEd,QAAQ,EAAE,IAAI;QAAEC,OAAO,EAAE,SAAS;QAAEC,OAAO,EAAE;MAAS;IAE5D;IAEA,MAAMgB,WAAU,GAAIpD,QAAQ,CAAC,MAAMmB,KAAK,CAACkC,OAAO,CAACD,WAAW;IAC5D,MAAME,SAAQ,GAAItD,QAAQ,CAAC,MAAMoD,WAAW,CAACG,KAAK,EAAEC,IAAG,KAAM,SAAS;;IAEtE;IACA,MAAMC,eAAc,GAAIA,CAAA,KAAM;MAC5BrC,MAAM,CAACsC,IAAI,CAAC,yBAAyB;IACvC;;IAEA;IACA,MAAMC,SAAQ,GAAI3D,QAAQ,CAAC,MAAM;MAC/B,MAAM2C,KAAI,GAAIN,KAAK,CAACkB,KAAK,CAACK,MAAK;MAC/B,MAAMC,SAAQ,GAAIxB,KAAK,CAACkB,KAAK,CAACO,MAAM,CAACC,CAAA,IAAKA,CAAC,CAACC,MAAK,KAAM,WAAW,CAAC,CAACJ,MAAK;MACzE,MAAMK,UAAS,GAAI5B,KAAK,CAACkB,KAAK,CAACO,MAAM,CAACC,CAAA,IAAKA,CAAC,CAACC,MAAK,KAAM,aAAa,CAAC,CAACJ,MAAK;MAC5E,MAAMM,cAAa,GAAIvB,KAAI,GAAI,IAAIwB,IAAI,CAACC,KAAK,CAAEP,SAAQ,GAAIlB,KAAK,GAAI,GAAG,IAAI;MAE3E,OAAO;QACLA,KAAK;QACLkB,SAAS;QACTI,UAAU;QACVC;MACF;IACF,CAAC;;IAED;IACA,MAAMG,YAAW,GAAIrE,QAAQ,CAAC,MAAM;MAClC,IAAI,CAACsD,SAAS,CAACC,KAAI,IAAKlB,KAAK,CAACkB,KAAK,CAACK,MAAK,KAAM,CAAC,EAAE;QAChD,OAAO,EAAC;MACV;;MAEA;MACA,MAAMU,aAAY,GAAI,CAAC;MAEvBjC,KAAK,CAACkB,KAAK,CAACgB,OAAO,CAACC,IAAG,IAAK;QAC1B,MAAMC,SAAQ,GAAID,IAAI,CAACC,SAAQ,IAAK,SAAQ;QAC5C,MAAMC,WAAU,GAAIF,IAAI,CAACE,WAAU,IAAK,MAAK;QAC7C,MAAMC,MAAK,GAAIH,IAAI,CAACG,MAAK,IAAK,SAAQ;QACtC,MAAMC,QAAO,GAAIJ,IAAI,CAACI,QAAO,IAAK,MAAK;QAEvC,IAAI,CAACN,aAAa,CAACG,SAAS,CAAC,EAAE;UAC7BH,aAAa,CAACG,SAAS,IAAI;YACzBA,SAAS;YACTC,WAAW;YACXG,KAAK,EAAE,CAAC,CAAC;YACTC,UAAU,EAAE,CAAC;YACbC,cAAc,EAAE;UAClB;QACF;QAEA,IAAI,CAACT,aAAa,CAACG,SAAS,CAAC,CAACI,KAAK,CAACF,MAAM,CAAC,EAAE;UAC3CL,aAAa,CAACG,SAAS,CAAC,CAACI,KAAK,CAACF,MAAM,IAAI;YACvCA,MAAM;YACNC,QAAQ;YACRvC,KAAK,EAAE;UACT;QACF;QAEAiC,aAAa,CAACG,SAAS,CAAC,CAACI,KAAK,CAACF,MAAM,CAAC,CAACtC,KAAK,CAACqB,IAAI,CAACc,IAAI;QACtDF,aAAa,CAACG,SAAS,CAAC,CAACK,UAAU,EAAC;QAEpC,IAAIN,IAAI,CAACR,MAAK,KAAM,WAAW,EAAE;UAC/BM,aAAa,CAACG,SAAS,CAAC,CAACM,cAAc,EAAC;QAC1C;MACF,CAAC;;MAED;MACA,OAAOC,MAAM,CAACC,MAAM,CAACX,aAAa,CAAC,CAACY,GAAG,CAACC,OAAM,KAAM;QAClD,GAAGA,OAAO;QACVN,KAAK,EAAEG,MAAM,CAACC,MAAM,CAACE,OAAO,CAACN,KAAK;MACpC,CAAC,CAAC;IACJ,CAAC;;IAED;IACA,MAAMO,WAAU,GAAI,MAAAA,CAAA,KAAY;MAC9B,IAAI;QACFC,OAAO,CAACC,GAAG,CAAC,kBAAkB;QAC9BD,OAAO,CAACC,GAAG,CAAC,OAAO,EAAElC,WAAW,CAACG,KAAK;QACtC8B,OAAO,CAACC,GAAG,CAAC,OAAO,EAAElC,WAAW,CAACG,KAAK,EAAEC,IAAI;QAE5C,IAAIJ,WAAW,CAACG,KAAK,EAAEC,IAAG,KAAM,SAAS,EAAE;UACzC;UACA,MAAM+B,uBAAuB,CAAC;QAChC,OAAO;UACL;UACAF,OAAO,CAACC,GAAG,CAAC,gBAAgB;UAC5B,MAAME,QAAO,GAAI,MAAMlF,OAAO,CAACmF,cAAc,CAAC;UAC9CJ,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEE,QAAQ;UACjClD,OAAO,CAACiB,KAAI,GAAIiC,QAAQ,EAAEE,OAAM,IAAK,EAAC;UACtCL,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEhD,OAAO,CAACiB,KAAK,CAAC2B,GAAG,CAACS,IAAG,KAAM;YACnDC,EAAE,EAAED,IAAI,CAACC,EAAE;YACX5E,IAAI,EAAE2E,IAAI,CAAC3E,IAAI;YACfyD,SAAS,EAAEkB,IAAI,CAAClB,SAAS;YACzBC,WAAW,EAAEiB,IAAI,CAACjB,WAAW;YAC7BV,MAAM,EAAE2B,IAAI,CAAC3B;UACf,CAAC,CAAC,CAAC;QACL;QAEAqB,OAAO,CAACC,GAAG,CAAC,OAAO,EAAEhD,OAAO,CAACiB,KAAK;QAClC8B,OAAO,CAACC,GAAG,CAAC,OAAO,EAAEhD,OAAO,CAACiB,KAAK,CAACK,MAAM;QAEzC,IAAItB,OAAO,CAACiB,KAAK,CAACK,MAAK,GAAI,CAAC,EAAE;UAC5BrB,aAAa,CAACgB,KAAI,GAAIjB,OAAO,CAACiB,KAAK,CAAC,CAAC,CAAC,CAACqC,EAAC;UACxCP,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE/C,aAAa,CAACgB,KAAK;UAC3CsC,SAAS,CAAC;QACZ,OAAO;UACLR,OAAO,CAACC,GAAG,CAAC,UAAU;QACxB;MACF,EAAE,OAAOQ,KAAK,EAAE;QACdT,OAAO,CAACS,KAAK,CAAC,WAAW,EAAEA,KAAK;QAChCtF,SAAS,CAACsF,KAAK,CAAC,UAAU;MAC5B;IACF;;IAEA;IACA,MAAMP,uBAAsB,GAAI,MAAAA,CAAA,KAAY;MAC1C,IAAI;QACF;QACA,MAAMQ,gBAAe,GAAI,MAAMxF,UAAU,CAACyF,aAAa,CAAC;QACxDX,OAAO,CAACC,GAAG,CAAC,SAAS,EAAES,gBAAgB;QACvC,MAAME,UAAS,GAAIF,gBAAgB,EAAEL,OAAM,IAAK,EAAC;QACjDL,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEW,UAAU;QAEjC,IAAIA,UAAU,CAACrC,MAAK,KAAM,CAAC,EAAE;UAC3ByB,OAAO,CAACC,GAAG,CAAC,YAAY;UACxBhD,OAAO,CAACiB,KAAI,GAAI,EAAC;UACjB;QACF;;QAEA;QACA,MAAM2C,QAAO,GAAI,EAAC;QAClB,KAAK,MAAMf,OAAM,IAAKc,UAAU,EAAE;UAChC,IAAI;YACFZ,OAAO,CAACC,GAAG,CAAC,cAAcH,OAAO,CAACnE,IAAI,SAASmE,OAAO,CAACS,EAAE,WAAW;YACpE,MAAMO,aAAY,GAAI,MAAM7F,OAAO,CAAC8F,eAAe,CAACjB,OAAO,CAACS,EAAE;YAC9DP,OAAO,CAACC,GAAG,CAAC,MAAMH,OAAO,CAACnE,IAAI,SAAS,EAAEmF,aAAa;YACtDd,OAAO,CAACC,GAAG,CAAC,OAAO,EAAE,OAAOa,aAAa;YACzCd,OAAO,CAACC,GAAG,CAAC,OAAO,EAAEe,IAAI,CAACC,SAAS,CAACH,aAAa,EAAE,IAAI,EAAE,CAAC,CAAC;YAE3D,MAAMI,YAAW,GAAIJ,aAAa,EAAET,OAAM,IAAK,EAAC;YAChDL,OAAO,CAACC,GAAG,CAAC,MAAMH,OAAO,CAACnE,IAAI,SAAS,EAAEuF,YAAY,CAAC3C,MAAM;;YAE5D;YACA,MAAM4C,gBAAe,GAAID,YAAY,CAACrB,GAAG,CAACS,IAAG,KAAM;cACjD,GAAGA,IAAI;cACPjB,WAAW,EAAES,OAAO,CAACnE,IAAI;cACzByD,SAAS,EAAEU,OAAO,CAACS;YACrB,CAAC,CAAC;YAEFM,QAAQ,CAACxC,IAAI,CAAC,GAAG8C,gBAAgB;YACjCnB,OAAO,CAACC,GAAG,CAAC,MAAMH,OAAO,CAACnE,IAAI,cAAc,EAAEkF,QAAQ,CAACtC,MAAM;UAC/D,EAAE,OAAOkC,KAAK,EAAE;YACdT,OAAO,CAACS,KAAK,CAAC,YAAYX,OAAO,CAACnE,IAAI,YAAY;YAClDqE,OAAO,CAACS,KAAK,CAAC,OAAO,EAAEA,KAAK;YAC5BT,OAAO,CAACS,KAAK,CAAC,OAAO,EAAEA,KAAK,CAAC3D,OAAO;YACpCkD,OAAO,CAACS,KAAK,CAAC,OAAO,EAAEA,KAAK,CAACN,QAAQ;YACrCH,OAAO,CAACS,KAAK,CAAC,OAAO,EAAEA,KAAK,CAACN,QAAQ,EAAExB,MAAM;YAC7CqB,OAAO,CAACS,KAAK,CAAC,OAAO,EAAEA,KAAK,CAACN,QAAQ,EAAEiB,IAAI;UAC7C;QACF;QAEApB,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEY,QAAQ;QAClC5D,OAAO,CAACiB,KAAI,GAAI2C,QAAO;MAEzB,EAAE,OAAOJ,KAAK,EAAE;QACdT,OAAO,CAACS,KAAK,CAAC,aAAa,EAAEA,KAAK;QAClC,MAAMA,KAAI;MACZ;IACF;;IAEA;IACA,MAAMD,SAAQ,GAAI,MAAAA,CAAA,KAAY;MAC5B,IAAI,CAACtD,aAAa,CAACgB,KAAK,EAAE;QACxB8B,OAAO,CAACC,GAAG,CAAC,iBAAiB;QAC7B;MACF;MAEA,IAAI;QACFhE,OAAO,CAACiC,KAAI,GAAI,IAAG;QACnB,MAAMmD,MAAK,GAAI;UACbC,IAAI,EAAElE,WAAW,CAACc,KAAK;UACvBqD,IAAI,EAAElE,QAAQ,CAACa,KAAK;UACpBsD,IAAI,EAAE,MAAM;UAAG;UACfC,MAAM,EAAE,YAAY;UACpBC,OAAO,EAAE;QACX;QAEA1B,OAAO,CAACC,GAAG,CAAC,kBAAkB;QAC9BD,OAAO,CAACC,GAAG,CAAC,OAAO,EAAElC,WAAW,CAACG,KAAK;QACtC8B,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE/C,aAAa,CAACgB,KAAK;QAC1C8B,OAAO,CAACC,GAAG,CAAC,OAAO,EAAEhD,OAAO,CAACiB,KAAK;QAClC8B,OAAO,CAACC,GAAG,CAAC,OAAO,EAAEoB,MAAM;QAC3BrB,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAE,kBAAkB/C,aAAa,CAACgB,KAAK,EAAE;;QAE7D;QACA,MAAMiC,QAAO,GAAI,MAAMnF,SAAS,CAAC2G,cAAc,CAACzE,aAAa,CAACgB,KAAK,EAAEmD,MAAM;QAC3ErB,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEE,QAAQ;QACpCH,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE;UACrB2B,UAAU,EAAE,CAAC,CAACzB,QAAQ,EAAEE,OAAO;UAC/BwB,aAAa,EAAE1B,QAAQ,EAAEE,OAAO,EAAE9B,MAAK,IAAK,CAAC;UAC7CuD,aAAa,EAAE3B,QAAQ,EAAE2B,aAAa;UACtCC,UAAU,EAAE5B,QAAQ,EAAE4B,UAAU;UAChC3E,WAAW,EAAE+C,QAAQ,EAAE/C;QACzB,CAAC;QAED,IAAI4E,QAAO,GAAI7B,QAAQ,EAAEE,OAAM,IAAK,EAAC;QACrCL,OAAO,CAACC,GAAG,CAAC,cAAc,EAAE+B,QAAQ,CAACzD,MAAM;;QAE3C;QACA,IAAIyD,QAAQ,CAACzD,MAAK,GAAI,CAAC,EAAE;UACvByB,OAAO,CAACC,GAAG,CAAC,OAAO,EAAE+B,QAAQ,CAACnC,GAAG,CAACV,IAAG,KAAM;YACzCoB,EAAE,EAAEpB,IAAI,CAACoB,EAAE;YACX/C,KAAK,EAAE2B,IAAI,CAAC3B,KAAK;YACjBgE,IAAI,EAAErC,IAAI,CAACqC,IAAI;YACf7C,MAAM,EAAEQ,IAAI,CAACR,MAAM;YACnBW,MAAM,EAAEH,IAAI,CAACG,MAAM;YACnBF,SAAS,EAAED,IAAI,CAACC;UAClB,CAAC,CAAC,CAAC;UAEHY,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE+B,QAAQ,CAACC,MAAM,CAAC,CAACC,GAAG,EAAE/C,IAAI,KAAK;YACpD+C,GAAG,CAAC/C,IAAI,CAACR,MAAM,IAAI,CAACuD,GAAG,CAAC/C,IAAI,CAACR,MAAM,KAAK,CAAC,IAAI;YAC7C,OAAOuD,GAAE;UACX,CAAC,EAAE,CAAC,CAAC,CAAC;QACR,OAAO;UACLlC,OAAO,CAACC,GAAG,CAAC,eAAe;;UAE3B;UACA,IAAI;YACFD,OAAO,CAACC,GAAG,CAAC,oBAAoB;YAChC,MAAMkC,gBAAe,GAAI,MAAMnH,SAAS,CAACoH,UAAU,CAAC;cAAEZ,IAAI,EAAE,MAAM;cAAEF,IAAI,EAAE,CAAC;cAAEC,IAAI,EAAE;YAAG,CAAC;YACvFvB,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEkC,gBAAgB;YACvC,MAAME,gBAAe,GAAIF,gBAAgB,EAAE9B,OAAM,IAAK,EAAC;YACvDL,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEoC,gBAAgB,CAACxC,GAAG,CAACV,IAAG,KAAM;cACpDoB,EAAE,EAAEpB,IAAI,CAACoB,EAAE;cACX/C,KAAK,EAAE2B,IAAI,CAAC3B,KAAK;cACjB8B,MAAM,EAAEH,IAAI,CAACG,MAAM;cACnBF,SAAS,EAAED,IAAI,CAACC,SAAS;cACzBT,MAAM,EAAEQ,IAAI,CAACR,MAAM;cACnB2D,OAAO,EAAEnD,IAAI,CAACmD,OAAO,EAAEC;YACzB,CAAC,CAAC,CAAC;;YAEH;YACAvC,OAAO,CAACC,GAAG,CAAC,oBAAoB;YAChC,MAAMuC,aAAY,GAAI,MAAMC,KAAK,CAAC,0BAA0B,EAAE;cAC5DC,OAAO,EAAE;gBACP,eAAe,EAAE,UAAUC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;cAC1D;YACF,CAAC;YACD,MAAMC,SAAQ,GAAI,MAAML,aAAa,CAACM,IAAI,CAAC;YAC3C9C,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE4C,SAAS;UAClC,EAAE,OAAOpC,KAAK,EAAE;YACdT,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEQ,KAAK;UAChC;QACF;;QAIA;QACAuB,QAAO,GAAIA,QAAQ,CAACnC,GAAG,CAACV,IAAG,KAAM;UAC/B,GAAGA,IAAI;UACPR,MAAM,EAAEQ,IAAI,CAACR,MAAK,IAAK,WAAW;UAAG;UACrChB,QAAQ,EAAEwB,IAAI,CAAC4D,OAAM,IAAK,IAAI;UAAG;UACjCC,QAAQ,EAAE7D,IAAI,CAAC6D,QAAO,IAAK,IAAI;UAC/BC,QAAQ,EAAE9D,IAAI,CAAC8D,QAAO,IAAK;QAC7B,CAAC,CAAC;;QAEF;QACAjD,OAAO,CAACC,GAAG,CAAC,WAAW,EAAE9C,SAAS,CAACe,KAAK;QACxC8B,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE+B,QAAQ,CAACnC,GAAG,CAACV,IAAG,KAAM;UAAEoB,EAAE,EAAEpB,IAAI,CAACoB,EAAE;UAAE/C,KAAK,EAAE2B,IAAI,CAAC3B,KAAK;UAAEmB,MAAM,EAAEQ,IAAI,CAACR;QAAO,CAAC,CAAC,CAAC;QAEvG,IAAIxB,SAAS,CAACe,KAAI,KAAM,KAAK,EAAE;UAC7B,MAAMgF,aAAY,GAAIlB,QAAQ,CAACvD,MAAM,CAACU,IAAG,IAAKA,IAAI,CAACR,MAAK,KAAMxB,SAAS,CAACe,KAAK;UAC7E8B,OAAO,CAACC,GAAG,CAAC,YAAY+B,QAAQ,CAACzD,MAAM,cAAc2E,aAAa,CAAC3E,MAAM,EAAE;UAC3EyB,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEiD,aAAa,CAACrD,GAAG,CAACV,IAAG,KAAM;YAAEoB,EAAE,EAAEpB,IAAI,CAACoB,EAAE;YAAE/C,KAAK,EAAE2B,IAAI,CAAC3B,KAAK;YAAEmB,MAAM,EAAEQ,IAAI,CAACR;UAAO,CAAC,CAAC,CAAC;UAC3GqD,QAAO,GAAIkB,aAAY;QACzB;;QAEA;QACAlG,KAAK,CAACkB,KAAI,GAAI,EAAC;QACf,MAAMrD,QAAQ,CAAC;QACfmC,KAAK,CAACkB,KAAI,GAAI,CAAC,GAAG8D,QAAQ;QAC1B1E,KAAK,CAACY,KAAI,GAAI8D,QAAQ,CAACzD,MAAK;MAE9B,EAAE,OAAOkC,KAAK,EAAE;QACdT,OAAO,CAACS,KAAK,CAAC,kBAAkB;QAChCT,OAAO,CAACS,KAAK,CAAC,OAAO,EAAEA,KAAK;QAC5BT,OAAO,CAACS,KAAK,CAAC,OAAO,EAAEA,KAAK,CAAC3D,OAAO;QACpCkD,OAAO,CAACS,KAAK,CAAC,OAAO,EAAEA,KAAK,CAACN,QAAQ;QACrCH,OAAO,CAACS,KAAK,CAAC,OAAO,EAAEA,KAAK,CAACN,QAAQ,EAAExB,MAAM;QAC7CqB,OAAO,CAACS,KAAK,CAAC,OAAO,EAAEA,KAAK,CAACN,QAAQ,EAAEiB,IAAI;QAE3CjG,SAAS,CAACsF,KAAK,CAAC,UAAS,IAAKA,KAAK,CAACN,QAAQ,EAAEiB,IAAI,EAAEtE,OAAM,IAAK2D,KAAK,CAAC3D,OAAM,IAAK,MAAM,CAAC;MACzF,UAAU;QACRb,OAAO,CAACiC,KAAI,GAAI,KAAI;MACtB;IACF;;IAEA;IACA,MAAMiF,UAAS,GAAI,MAAAA,CAAA,KAAY;MAC7B,IAAI,CAACnH,OAAO,CAACkC,KAAK,EAAE;MAEpB,IAAI;QACF,MAAMlC,OAAO,CAACkC,KAAK,CAACkF,QAAQ,CAAC;QAC7BlH,UAAU,CAACgC,KAAI,GAAI,IAAG;QAEtB,MAAMmF,QAAO,GAAI;UACf7F,KAAK,EAAED,QAAQ,CAACC,KAAK;UACrBd,OAAO,EAAEa,QAAQ,CAACE,WAAW;UAC7BC,QAAQ,EAAE4F,iBAAiB,CAAC/F,QAAQ,CAACG,QAAQ,CAAC;UAC9CqF,OAAO,EAAExF,QAAQ,CAACI,QAAO,GAAI,IAAI4F,IAAI,CAAChG,QAAQ,CAACI,QAAQ,CAAC,CAAC6F,WAAW,CAAC,IAAI;QAC3E;;QAEA;QACA,IAAI,CAAClH,WAAW,CAAC4B,KAAK,EAAE;UACtBmF,QAAQ,CAAC7B,IAAG,GAAI,MAAK;UACrB6B,QAAQ,CAAC/D,MAAK,GAAIpC,aAAa,CAACgB,KAAI;QACtC;QAEA8B,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEoD,QAAQ;QAC/BrD,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE,CAAC,CAAC3D,WAAW,CAAC4B,KAAK;QAE3C,IAAI5B,WAAW,CAAC4B,KAAK,EAAE;UACrB8B,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE3D,WAAW,CAAC4B,KAAK,CAACqC,EAAE;UAC3C,MAAMvF,SAAS,CAACyI,YAAY,CAACnH,WAAW,CAAC4B,KAAK,CAACqC,EAAE,EAAE8C,QAAQ;UAC3DlI,SAAS,CAACuI,OAAO,CAAC,QAAQ;QAC5B,OAAO;UACL1D,OAAO,CAACC,GAAG,CAAC,OAAO;UACnB,MAAMjF,SAAS,CAAC2I,YAAY,CAACN,QAAQ;UACrClI,SAAS,CAACuI,OAAO,CAAC,QAAQ;QAC5B;QAEAvH,gBAAgB,CAAC+B,KAAI,GAAI,KAAI;QAC7B0F,SAAS,CAAC;QACVpD,SAAS,CAAC;MACZ,EAAE,OAAOC,KAAK,EAAE;QACdT,OAAO,CAACS,KAAK,CAAC,SAAS,EAAEA,KAAK;QAC9BT,OAAO,CAACS,KAAK,CAAC,OAAO,EAAEA,KAAK,CAACN,QAAQ,EAAEiB,IAAI;QAC3C,MAAMyC,YAAW,GAAIpD,KAAK,CAACN,QAAQ,EAAEiB,IAAI,EAAEtE,OAAM,IAAK2D,KAAK,CAAC3D,OAAM,IAAK,QAAO;QAC9E3B,SAAS,CAACsF,KAAK,CAACoD,YAAY;MAC9B,UAAU;QACR3H,UAAU,CAACgC,KAAI,GAAI,KAAI;MACzB;IACF;;IAEA;IACA,MAAM0F,SAAQ,GAAIA,CAAA,KAAM;MACtBjE,MAAM,CAACmE,MAAM,CAACvG,QAAQ,EAAE;QACtBC,KAAK,EAAE,EAAE;QACTC,WAAW,EAAE,EAAE;QACfC,QAAQ,EAAE,QAAQ;QAClBC,QAAQ,EAAE;MACZ,CAAC;MACDrB,WAAW,CAAC4B,KAAI,GAAI,IAAG;IACzB;;IAEA;IACA,MAAM6F,QAAO,GAAK5E,IAAI,IAAK;MACzB5C,YAAY,CAAC2B,KAAI,GAAIiB,IAAG;MACxB3C,aAAa,CAAC0B,KAAI,GAAIiB,IAAI,CAAC8D,QAAO,IAAK;MACvC7G,gBAAgB,CAAC8B,KAAI,GAAI,IAAG;IAC9B;;IAEA;IACA,MAAM8F,QAAO,GAAK7E,IAAI,IAAK;MACzB7C,WAAW,CAAC4B,KAAI,GAAIiB,IAAG;MACvBQ,MAAM,CAACmE,MAAM,CAACvG,QAAQ,EAAE;QACtBC,KAAK,EAAE2B,IAAI,CAAC3B,KAAK;QACjBC,WAAW,EAAE0B,IAAI,CAACzC,OAAO;QACzBgB,QAAQ,EAAEyB,IAAI,CAACzB,QAAQ;QACvBC,QAAQ,EAAEwB,IAAI,CAAC4D,OAAM,GAAI,IAAIQ,IAAI,CAACpE,IAAI,CAAC4D,OAAO,IAAI;MACpD,CAAC;MACD5G,gBAAgB,CAAC+B,KAAI,GAAI,IAAG;IAC9B;;IAEA;IACA,MAAM+F,gBAAe,GAAI,MAAAA,CAAO;MAAEC,MAAM;MAAE/E;IAAK,CAAC,KAAK;MACnD,IAAI;QACF,IAAIrC,OAAM,GAAI,EAAC;QACf,IAAI6B,MAAK,GAAI,EAAC;QAEd,QAAQuF,MAAM;UACZ,KAAK,OAAO;YACVvF,MAAK,GAAI,aAAY;YACrB7B,OAAM,GAAI,OAAM;YAChB;UACF,KAAK,QAAQ;YACX;YACAqH,gBAAgB,CAAChF,IAAI;YACrB;UACF,KAAK,SAAS;YACZ;YACA,MAAMiF,aAAY,GAAI,IAAIC,eAAe,CAAC;YAC1CD,aAAa,CAACE,MAAM,CAAC,UAAU,EAAE,MAAM;YACvC,MAAMtJ,SAAS,CAACuJ,UAAU,CAACpF,IAAI,CAACoB,EAAE,EAAE6D,aAAa;YACjDjJ,SAAS,CAACuI,OAAO,CAAC,QAAQ;YAC1BlD,SAAS,CAAC;YACV;UACF,KAAK,QAAQ;YACX;YACA,MAAMgE,QAAO,GAAI,MAAMpJ,YAAY,CAACqJ,MAAM,CAAC,SAAS,EAAE,MAAM,EAAE;cAC5DC,iBAAiB,EAAE,IAAI;cACvBC,gBAAgB,EAAE,IAAI;cACtBC,SAAS,EAAE;YACb,CAAC;YACD,MAAMC,YAAW,GAAI,IAAIR,eAAe,CAAC;YACzCQ,YAAY,CAACP,MAAM,CAAC,UAAU,EAAE,OAAO;YACvCO,YAAY,CAACP,MAAM,CAAC,UAAU,EAAEE,QAAQ,CAACtG,KAAI,IAAK,EAAE;YACpD,MAAMlD,SAAS,CAACuJ,UAAU,CAACpF,IAAI,CAACoB,EAAE,EAAEsE,YAAY;YAChD1J,SAAS,CAACuI,OAAO,CAAC,gBAAgB;YAClClD,SAAS,CAAC;YACV;UACF,KAAK,UAAU;YACb;YACA,MAAMxF,SAAS,CAAC8J,gBAAgB,CAAC3F,IAAI,CAACoB,EAAE,EAAE,QAAQ;YAClDpF,SAAS,CAACuI,OAAO,CAAC,OAAO;YACzBlD,SAAS,CAAC;YACV;UACF,KAAK,QAAQ;YACX7B,MAAK,GAAI,WAAU;YACnB7B,OAAM,GAAI,OAAM;YAChB;UACF,KAAK,QAAQ;YACX,MAAM1B,YAAY,CAAC2J,OAAO,CAAC,aAAa,EAAE,MAAM,EAAE;cAChDL,iBAAiB,EAAE,IAAI;cACvBC,gBAAgB,EAAE,IAAI;cACtBnD,IAAI,EAAE;YACR,CAAC;YACD,MAAMxG,SAAS,CAACgK,YAAY,CAAC7F,IAAI,CAACoB,EAAE;YACpCpF,SAAS,CAACuI,OAAO,CAAC,QAAQ;YAC1BlD,SAAS,CAAC;YACV;QACJ;QAEA,IAAI7B,MAAM,EAAE;UACV,MAAM3D,SAAS,CAACyI,YAAY,CAACtE,IAAI,CAACoB,EAAE,EAAE;YAAE5B;UAAO,CAAC;UAChDxD,SAAS,CAACuI,OAAO,CAAC5G,OAAO;UACzB0D,SAAS,CAAC;QACZ;MACF,EAAE,OAAOC,KAAK,EAAE;QACd,IAAIA,KAAI,KAAM,QAAQ,EAAE;UACtBT,OAAO,CAACS,KAAK,CAAC,SAAS,EAAEA,KAAK;UAC9BtF,SAAS,CAACsF,KAAK,CAAC,MAAM;QACxB;MACF;IACF;;IAEA;IACA,MAAMwE,cAAa,GAAI,MAAO/G,KAAK,IAAK;MACtC,IAAI,CAAC3B,YAAY,CAAC2B,KAAK,EAAE;MAEzB,IAAI;QACF,MAAMlD,SAAS,CAACyI,YAAY,CAAClH,YAAY,CAAC2B,KAAK,CAACqC,EAAE,EAAE;UAAE0C,QAAQ,EAAE/E;QAAM,CAAC;QACvE/C,SAAS,CAACuI,OAAO,CAAC,QAAQ;QAC1BlD,SAAS,CAAC;MACZ,EAAE,OAAOC,KAAK,EAAE;QACdT,OAAO,CAACS,KAAK,CAAC,SAAS,EAAEA,KAAK;QAC9BtF,SAAS,CAACsF,KAAK,CAAC,QAAQ;MAC1B;IACF;;IAEA;IACA,MAAMyE,SAAQ,GAAI,MAAO/F,IAAI,IAAK;MAChC,IAAI;QACF,MAAM/D,YAAY,CAAC2J,OAAO,CACxB,WAAW5F,IAAI,CAAC3B,KAAK,oBAAoB,EACzC,MAAM,EACN;UACEkH,iBAAiB,EAAE,IAAI;UACvBC,gBAAgB,EAAE,IAAI;UACtBnD,IAAI,EAAE;QACR,CACF;;QAEA;QACA,MAAMxG,SAAS,CAAC8J,gBAAgB,CAAC3F,IAAI,CAACoB,EAAE,EAAE,QAAQ;QAClDpF,SAAS,CAACuI,OAAO,CAAC,OAAO;QACzBlD,SAAS,CAAC,GAAE;MACd,EAAE,OAAOC,KAAK,EAAE;QACd,IAAIA,KAAI,KAAM,QAAQ,EAAE;UACtBT,OAAO,CAACS,KAAK,CAAC,SAAS,EAAEA,KAAK;UAC9BtF,SAAS,CAACsF,KAAK,CAAC,QAAQ;QAC1B;MACF;IACF;;IAEA;IACA,MAAM0D,gBAAe,GAAKhF,IAAI,IAAK;MACjC5C,YAAY,CAAC2B,KAAI,GAAIiB,IAAG;MACxB1C,UAAU,CAACC,OAAM,GAAI,EAAC;MACtBD,UAAU,CAACE,QAAO,GAAI,EAAC;MACvBN,oBAAoB,CAAC6B,KAAI,GAAI,IAAG;IAClC;;IAEA;IACA,MAAMiH,SAAQ,GAAI3K,GAAG,CAAC;IAEtB,MAAM4K,gBAAe,GAAIA,CAACC,CAAC,EAAE1I,QAAQ,KAAK;MACxCF,UAAU,CAACE,QAAO,GAAIA,QAAO;IAC/B;IAEA,MAAM2I,gBAAe,GAAIA,CAACD,CAAC,EAAE1I,QAAQ,KAAK;MACxCF,UAAU,CAACE,QAAO,GAAIA,QAAO;IAC/B;IAEA,MAAM4I,YAAW,GAAKC,IAAI,IAAK;MAC7B,MAAMC,OAAM,GAAID,IAAI,CAACjE,IAAG,GAAI,IAAG,GAAI,IAAG,GAAI,EAAC;MAC3C,IAAI,CAACkE,OAAO,EAAE;QACZtK,SAAS,CAACsF,KAAK,CAAC,eAAe;QAC/B,OAAO,KAAI;MACb;MACA,OAAO,IAAG;IACZ;;IAEA;IACA,MAAMiF,oBAAmB,GAAI,MAAAA,CAAA,KAAY;MACvC,IAAI;QACFxJ,UAAU,CAACgC,KAAI,GAAI,IAAG;;QAEtB;QACA,MAAMyH,QAAO,GAAI,IAAIC,QAAQ,CAAC;QAC9BD,QAAQ,CAACrB,MAAM,CAAC,mBAAmB,EAAE7H,UAAU,CAACC,OAAO;;QAEvD;QACA,IAAID,UAAU,CAACE,QAAO,IAAKF,UAAU,CAACE,QAAQ,CAAC4B,MAAK,GAAI,CAAC,EAAE;UACzD9B,UAAU,CAACE,QAAQ,CAACuC,OAAO,CAAE2G,QAAQ,IAAK;YACxC,IAAIA,QAAQ,CAACC,GAAG,EAAE;cAChBH,QAAQ,CAACrB,MAAM,CAAC,OAAO,EAAEuB,QAAQ,CAACC,GAAG;YACvC;UACF,CAAC;QACH;QAEA,MAAM9K,SAAS,CAAC+K,mBAAmB,CAACxJ,YAAY,CAAC2B,KAAK,CAACqC,EAAE,EAAEoF,QAAQ;QAEnExK,SAAS,CAACuI,OAAO,CAAC,QAAQ;QAC1BrH,oBAAoB,CAAC6B,KAAI,GAAI,KAAI;QACjCsC,SAAS,CAAC;MACZ,EAAE,OAAOC,KAAK,EAAE;QACdT,OAAO,CAACS,KAAK,CAAC,SAAS,EAAEA,KAAK;QAC9BtF,SAAS,CAACsF,KAAK,CAAC,QAAQ;MAC1B,UAAU;QACRvE,UAAU,CAACgC,KAAI,GAAI,KAAI;MACzB;IACF;;IAEA;IACA,MAAM8H,OAAM,GAAK7G,IAAI,IAAK;MACxB,OAAOA,IAAI,CAACmD,OAAO,EAAE/B,EAAC,KAAMxC,WAAW,CAACG,KAAK,EAAEqC,EAAC,IAAKxC,WAAW,CAACG,KAAK,EAAEC,IAAG,KAAM,SAAQ;IAC3F;IAEA,MAAM8H,SAAQ,GAAK9G,IAAI,IAAK;MAC1B,OAAOA,IAAI,CAACmD,OAAO,EAAE/B,EAAC,KAAMxC,WAAW,CAACG,KAAK,EAAEqC,EAAC,IAAKxC,WAAW,CAACG,KAAK,EAAEC,IAAG,KAAM,SAAQ;IAC3F;;IAEA;IACA,MAAM+H,UAAS,GAAKC,IAAI,IAAK;MAC3B,IAAI,CAACA,IAAI,EAAE,OAAO,EAAC;MACnB,OAAO,IAAI5C,IAAI,CAAC4C,IAAI,CAAC,CAACC,cAAc,CAAC,OAAO;IAC9C;IAEA,MAAMC,SAAQ,GAAK1I,QAAQ,IAAK;MAC9B,OAAO,IAAI4F,IAAI,CAAC5F,QAAQ,IAAI,IAAI4F,IAAI,CAAC;IACvC;IAEA,MAAM+C,cAAa,GAAK3H,MAAM,IAAK;MACjC,MAAM4H,QAAO,GAAI;QACf,QAAQ,EAAE,SAAS;QACnB,WAAW,EAAE,MAAM;QACnB,aAAa,EAAE,SAAS;QACxB,WAAW,EAAE,SAAS;QACtB,WAAW,EAAE,SAAS;QACtB,WAAW,EAAE,QAAQ;QACrB,UAAU,EAAE,QAAQ;QACpB,UAAU,EAAE,SAAS;QACrB,OAAO,EAAE;MACX;MACA,OAAOA,QAAQ,CAAC5H,MAAM,KAAK,MAAK,EAAG;IACrC;IAEA,MAAM6H,aAAY,GAAK7H,MAAM,IAAK;MAChC,MAAM8H,OAAM,GAAI;QACd,QAAQ,EAAE,IAAI;QACd,WAAW,EAAE,KAAK;QAClB,aAAa,EAAE,KAAK;QACpB,WAAW,EAAE,KAAK;QAClB,WAAW,EAAE,KAAK;QAClB,WAAW,EAAE,KAAK;QAClB,UAAU,EAAE,KAAK;QACjB,UAAU,EAAE,KAAK;QACjB,OAAO,EAAE;MACX;MACA,OAAOA,OAAO,CAAC9H,MAAM,KAAKA,MAAK;IACjC;IAEA,MAAM+H,gBAAe,GAAKhJ,QAAQ,IAAK;MACrC;MACA,IAAIiJ,WAAU,GAAIjJ,QAAO;MACzB,IAAI,OAAOA,QAAO,KAAM,QAAQ,EAAE;QAChC,MAAMkJ,YAAW,GAAI;UACnB,CAAC,EAAE,KAAK;UACR,CAAC,EAAE,QAAQ;UACX,CAAC,EAAE,MAAM;UACT,CAAC,EAAE,QAAQ;UACX,CAAC,EAAE;QACL;QACAD,WAAU,GAAIC,YAAY,CAAClJ,QAAQ,KAAK,QAAO;MACjD;MAEA,MAAM6I,QAAO,GAAI;QACf,KAAK,EAAE,MAAM;QACb,QAAQ,EAAE,SAAS;QACnB,MAAM,EAAE,SAAS;QACjB,QAAQ,EAAE;MACZ;MACA,OAAOA,QAAQ,CAACI,WAAW,KAAK,MAAK,EAAG;IAC1C;IAEA,MAAME,eAAc,GAAKnJ,QAAQ,IAAK;MACpC;MACA,IAAIiJ,WAAU,GAAIjJ,QAAO;MACzB,IAAI,OAAOA,QAAO,KAAM,QAAQ,EAAE;QAChC,MAAMkJ,YAAW,GAAI;UACnB,CAAC,EAAE,KAAK;UACR,CAAC,EAAE,QAAQ;UACX,CAAC,EAAE,MAAM;UACT,CAAC,EAAE,QAAQ;UACX,CAAC,EAAE;QACL;QACAD,WAAU,GAAIC,YAAY,CAAClJ,QAAQ,KAAK,QAAO;MACjD;MAEA,MAAM+I,OAAM,GAAI;QACd,KAAK,EAAE,GAAG;QACV,QAAQ,EAAE,GAAG;QACb,MAAM,EAAE,GAAG;QACX,QAAQ,EAAE;MACZ;MACA,OAAOA,OAAO,CAACE,WAAW,KAAK,GAAE,EAAG;IACtC;IAEA,MAAMrD,iBAAgB,GAAK5F,QAAQ,IAAK;MACtC,MAAMoJ,SAAQ,GAAI;QAChB,KAAK,EAAE,CAAC;QACR,QAAQ,EAAE,CAAC;QACX,MAAM,EAAE,CAAC;QACT,QAAQ,EAAE;MACZ;MACA,OAAOA,SAAS,CAACpJ,QAAQ,KAAK;IAChC;;IAEA;IACA9C,KAAK,CAACsC,aAAa,EAAG6J,SAAS,IAAK;MAClC,IAAIA,SAAS,EAAE;QACbvG,SAAS,CAAC;MACZ;IACF,CAAC;IAMD9F,SAAS,CAAC,MAAM;MACd8F,SAAS,CAAC;MACVT,WAAW,CAAC;IACd,CAAC;IAED,OAAO;MACL9D,OAAO;MACPC,UAAU;MACVC,gBAAgB;MAChBC,gBAAgB;MAChBC,oBAAoB;MACpBC,WAAW;MACXC,YAAY;MACZC,aAAa;MACbC,UAAU;MACVG,eAAe;MACfI,KAAK;MACLC,OAAO;MACPC,aAAa;MACbC,SAAS;MACTC,WAAW;MACXC,QAAQ;MACRC,KAAK;MACLC,QAAQ;MACRK,SAAS;MACT5B,OAAO;MACP+B,WAAW;MACXE,SAAS;MACTK,SAAS;MACTU,YAAY;MACZZ,eAAe;MACfoC,SAAS;MACT2C,UAAU;MACVY,QAAQ;MACRC,QAAQ;MACRC,gBAAgB;MAChBgB,cAAc;MACdC,SAAS;MACTf,gBAAgB;MAChBuB,oBAAoB;MACpBP,SAAS;MACTC,gBAAgB;MAChBE,gBAAgB;MAChBC,YAAY;MACZS,OAAO;MACPC,SAAS;MACTC,UAAU;MACVG,SAAS;MACTC,cAAc;MACdE,aAAa;MACbE,gBAAgB;MAChBG,eAAe;MACfvD;IACF;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}